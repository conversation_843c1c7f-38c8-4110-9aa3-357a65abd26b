<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class RoleController extends Controller
{
    /**
     * 显示角色列表
     */
    public function index(Request $request)
    {
        $search = $request->input('search', '');
        
        try {
            $roles = Role::getRoleList($search);
            
            return Inertia::render('Settings/Roles/Index', [
                'roles' => $roles,
                'filters' => $request->only(['search']),
            ]);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 显示创建角色表单
     */
    public function create()
    {
        try {
            $formData = Role::getCreateFormData();
            
            return Inertia::render('Settings/Roles/Create', $formData);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 保存新创建的角色
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:50|unique:roles',
            'description' => 'nullable|string',
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        try {
            Role::createRole($validated);
            
            return redirect()->route('settings.roles')
                ->with('message', '角色创建成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 显示指定角色
     */
    public function show(Role $role)
    {
        try {
            $roleData = Role::getRoleWithUsers($role->id);
            
            return Inertia::render('Settings/Roles/Show', $roleData);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 显示编辑角色表单
     */
    public function edit(Role $role)
    {
        try {
            $editData = Role::getEditFormData($role->id);
            
            return Inertia::render('Settings/Roles/Edit', $editData);
        } catch (\Exception $e) {
            return redirect()->route('settings.roles')
                ->with('error', $e->getMessage());
        }
    }

    /**
     * 更新指定角色
     */
    public function update(Request $request, Role $role)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:50', Rule::unique('roles')->ignore($role->id)],
            'description' => 'nullable|string',
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        try {
            Role::updateRole($role->id, $validated);
            
            return redirect()->route('settings.roles')
                ->with('message', '角色更新成功');
        } catch (\Exception $e) {
            return redirect()->route('settings.roles')
                ->with('error', $e->getMessage());
        }
    }

    /**
     * 删除指定角色
     */
    public function destroy(Role $role)
    {
        try {
            Role::deleteRole($role->id);
            
            return redirect()->route('settings.roles')
                ->with('message', '角色已删除');
        } catch (\Exception $e) {
            return redirect()->route('settings.roles')
                ->with('error', $e->getMessage());
        }
    }

    /**
     * 为用户分配角色
     */
    public function assignUsers(Request $request, Role $role)
    {
        $validated = $request->validate([
            'users' => 'required|array',
            'users.*' => 'exists:users,id',
        ]);

        try {
            Role::assignUsers($role->id, $validated['users']);
            
            return redirect()->route('settings.roles.show', $role->id)
                ->with('message', '用户分配成功');
        } catch (\Exception $e) {
            return redirect()->route('settings.roles.show', $role->id)
                ->with('error', $e->getMessage());
        }
    }
} 