<?php

namespace App\Http\Controllers;

use App\Models\BOM;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BOMController extends Controller
{
    /**
     * 获取BOM列表
     *
     * @param Request $request 请求对象
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // 验证请求参数
        $validated = $request->validate([
            'company_code' => 'nullable|string|max:10',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:10|max:100',
            'search' => 'nullable|string|max:255',
            'sort_field' => 'nullable|string|in:category_code,category_name,figure,product_name,specification,material_code',
            'sort_direction' => 'nullable|string|in:asc,desc',
            'categories' => 'nullable|string',
        ]);

        try {
            $companyCode = $validated['company_code'] ?? Auth::user()->default_company_code;
            BOM::validateCompanyCode($companyCode);

            // 处理分类参数
            $categories = null;
            if (!empty($validated['categories'])) {
                $categories = explode(',', $validated['categories']);
            }

            $BOMs = BOM::getBOMList(
                $companyCode,
                $validated['page'] ?? 1,
                $validated['per_page'] ?? 20,
                $validated['search'] ?? null,
                $validated['sort_field'] ?? null,
                $validated['sort_direction'] ?? 'asc',
                $categories
            );

            return response()->json([
                'status' => 'success',
                'data' => $BOMs->items(),
                'pagination' => [
                    'total' => $BOMs->total(),
                    'per_page' => $BOMs->perPage(),
                    'current_page' => $BOMs->currentPage(),
                    'last_page' => $BOMs->lastPage(),
                    'from' => $BOMs->firstItem() ?? 0,
                    'to' => $BOMs->lastItem() ?? 0,
                ],
                'sort' => [
                    'field' => $validated['sort_field'] ?? null,
                    'direction' => $validated['sort_direction'] ?? 'asc'
                ],
                'categories' => $categories,
                'company_code' => $companyCode
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取产品分类
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductCategory(Request $request)
    {
        // 验证请求参数
        $validated = $request->validate([
            'company_code' => 'required|string|max:10',
        ]);

        try {
            BOM::validateCompanyCode($validated['company_code']);
            
            $categories = BOM::getProductCategory($validated['company_code']);
            
            return response()->json([
                'status' => 'success',
                'data' => $categories
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取指定BOM信息（包含树结构）
     *
     * @param string $code BOM编码
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBOMByCode($code, Request $request)
    {
        // 验证请求参数
        $validated = $request->validate([
            'company_code' => 'nullable|string|max:10',
            'mode' => 'nullable|string|in:multi,leaf',
            'date' => 'nullable|date',
            'include_optional' => 'nullable|boolean',
            'customer_code' => 'nullable|string|max:20',
        ]);

        try {
            $companyCode = $validated['company_code'] ?? 'TB';
            BOM::validateCompanyCode($companyCode);
            BOM::validateBOMCode($code);

            // 处理日期参数
            $date = $validated['date'] ?? Carbon::today()->toDateString();
            $date = Carbon::parse($date)->addDays(1)->subSecond();
            
            $bomTree = BOM::getBOMTreeByCode(
                $companyCode,
                $code,
                $validated['mode'] ?? 'multi',
                $date,
                $validated['include_optional'] ?? false,
                $validated['customer_code'] ?? null
            );
            
            if (empty($bomTree)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '未找到该BOM记录'
                ], 404);
            }
            
            return response()->json([
                'status' => 'success',
                'data' => $bomTree,
                'mode' => $validated['mode'] ?? 'multi',
                'include_optional' => $validated['include_optional'] ?? false,
                'customer_code' => $validated['customer_code'] ?? null
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取客户列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCustomers(Request $request)
    {
        // 验证请求参数
        $validated = $request->validate([
            'code' => 'required|string|max:50',
            'company_code' => 'nullable|string|max:10',
            'date' => 'nullable|date',
        ]);

        try {
            $companyCode = $validated['company_code'] ?? 'TB';
            BOM::validateCompanyCode($companyCode);
            BOM::validateBOMCode($validated['code']);
            
            // 处理日期参数
            $date = $validated['date'] ?? Carbon::today()->toDateString();
            $date = Carbon::parse($date)->addDays(1)->subSecond();

            $customers = BOM::getCustomers($companyCode, $validated['code'], $date);
                
            return response()->json([
                'status' => 'success',
                'data' => $customers
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
