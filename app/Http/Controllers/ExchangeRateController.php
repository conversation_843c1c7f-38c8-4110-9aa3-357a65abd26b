<?php

namespace App\Http\Controllers;

use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;


class ExchangeRateController extends Controller
{
    /**
     * 获取美元兑人民币汇率
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUsdCnyRate()
    {
        try {
            $client = new Client();
            $url = 'https://finance.pae.baidu.com/vapi/v1/getquotation?group=huilv_minute&need_reverse_real=1&code=USDCNY&finClientType=pc'; //百度股市通接口地址
            $response = $client->request('POST', $url);
            $body = json_decode($response->getBody(), false);
            // 检查响应状态
            if ($body->ResultCode == 0) {
                
                // 解析响应数据
                if (isset($body->Result) && isset($body->Result->cur)) {
                    $price = $body->Result->cur->price;
                    if ($price) {
                        return response()->json([
                            'success' => true,
                            'rate' => $price
                        ]);
                    }
                }
                
                return response()->json([
                    'success' => false,
                    'message' => '无法解析汇率数据'
                ], 500);
            }
            
            return response()->json([
                'success' => false,
                'message' => '获取汇率数据失败',
                // 'status_code' => $response->status()
            ], 500);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取汇率数据异常: ' . $e->getMessage()
            ], 500);
        }
    }
}
