<?php

namespace App\Http\Controllers;

use App\Models\Department;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DepartmentController extends Controller
{
    /**
     * 显示部门列表
     */
    public function index(Request $request)
    {
        // 验证搜索参数
        $search = $request->input('search', '');
        $status = $request->input('status', '');
        
        try {
            $departments = Department::getDepartmentList($search, $status);
            
            return Inertia::render('Settings/Departments/Index', [
                'departments' => $departments,
                'filters' => $request->only(['search', 'status']),
            ]);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 显示创建部门表单
     */
    public function create()
    {
        try {
            $formData = Department::getFormData();
            
            return Inertia::render('Settings/Departments/Create', $formData);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 存储新部门
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'parent_id' => 'nullable|exists:departments,id',
            'manager_ids' => 'nullable|array',
            'manager_ids.*' => 'exists:users,id',
            'description' => 'nullable|string',
            'status' => 'required|in:正常,停用',
            'order' => 'nullable|integer',
        ]);

        try {
            Department::createDepartment($validated);
            
            return redirect()->route('settings.departments')
                ->with('message', '部门创建成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * 显示部门详情
     */
    public function show(Department $department)
    {
        $department->load(['parent', 'managers', 'children', 'employees']);
        
        // 获取部门员工并分页
        $users = $department->employees()->paginate(10);
        
        return Inertia::render('Settings/Departments/Show', [
            'department' => $department,
            'users' => $users
        ]);
    }

    /**
     * 显示编辑部门表单
     */
    public function edit(Department $department)
    {
        $department->load(['parent', 'managers']);
        $departments = Department::where('id', '!=', $department->id)
            ->where('status', '正常')
            ->get();
        $managers = \App\Models\User::where('status', '在职')->get();
        $managerIds = $department->managers->pluck('id')->toArray();

        return Inertia::render('Settings/Departments/Edit', [
            'department' => $department,
            'departments' => $departments,
            'managers' => $managers,
            'managerIds' => $managerIds,
        ]);
    }

    /**
     * 更新部门
     */
    public function update(Request $request, Department $department)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'parent_id' => 'nullable|exists:departments,id',
            'manager_ids' => 'nullable|array',
            'manager_ids.*' => 'exists:users,id',
            'description' => 'nullable|string',
            'status' => 'required|in:正常,停用',
            'order' => 'nullable|integer',
        ]);

        // 分离主管ID数组
        $managerIds = $validated['manager_ids'] ?? [];
        unset($validated['manager_ids']);

        // 更新部门基本信息
        $department->update($validated);

        // 同步部门主管
        $department->managers()->sync($managerIds);

        return redirect()->route('settings.departments')
            ->with('message', '部门更新成功');
    }

    /**
     * 删除部门
     */
    public function destroy(Department $department)
    {
        try {
            Department::deleteDepartment($department->id);
            
            return redirect()->route('settings.departments')
                ->with('message', '部门删除成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }
}
