<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    /**
     * 获取订单列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // 验证请求参数
        $validated = $request->validate([
            'company_code' => 'nullable|string|max:10',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'department_id' => 'nullable|array',
            'department_id.*' => 'integer',
            'sales_account' => 'nullable|array',
            'sales_account.*' => 'string|max:20',
            'customer_code' => 'nullable|array',
            'customer_code.*' => 'string|max:20',
            'status' => 'nullable|string|in:draft,confirmed,approved,cancelled,closed',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:10|max:100',
            'sort_field' => 'nullable|string|in:order_code,customer_code,customer_name,sales_account,sales_name,department_name,order_date,status,currency_code,exchange_rate',
            'sort_direction' => 'nullable|string|in:asc,desc',
        ]);

        try {
            $orders = Order::getOrderList(
                $validated['company_code'] ?? Auth::user()->default_company_code,
                $validated['start_date'],
                $validated['end_date'],
                $validated['department_id'] ?? [],
                $validated['sales_account'] ?? [],
                $validated['customer_code'] ?? [],
                $validated['status'] ?? null,
                $validated['page'] ?? 1,
                $validated['per_page'] ?? 10,
                $validated['sort_field'] ?? 'order_date',
                $validated['sort_direction'] ?? 'desc'
            );

            return response()->json([
                'status' => 'success',
                'data' => $orders->items(),
                'pagination' => [
                    'total' => $orders->total(),
                    'per_page' => $orders->perPage(),
                    'current_page' => $orders->currentPage(),
                    'last_page' => $orders->lastPage(),
                    'from' => $orders->firstItem() ?? 0,
                    'to' => $orders->lastItem() ?? 0
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取订单表单所需的下拉选项数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFormData(Request $request)
    {
        // 验证请求参数
        $validated = $request->validate([
            'company_code' => 'nullable|string|max:10',
        ]);

        try {
            $formData = Order::getFormData($validated['company_code'] ?? Auth::user()->default_company_code);
            
            return response()->json([
                'status' => 'success',
                'data' => $formData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取物料列表（用于订单明细选择）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMaterials(Request $request)
    {
        // 验证请求参数
        $validated = $request->validate([
            'company_code' => 'nullable|string|max:10',
            'search' => 'nullable|string|max:255',
            'page' => 'nullable|integer|min:1',
            'perPage' => 'nullable|integer|min:10|max:100',
        ]);

        try {
            $result = Order::getMaterials(
                $validated['company_code'] ?? Auth::user()->default_company_code,
                $validated['search'] ?? '',
                $validated['page'] ?? 1,
                $validated['perPage'] ?? 20
            );
            
            return response()->json([
                'status' => 'success',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建新订单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'company_code' => 'required|string|max:10',
            'customer_code' => 'required|string|max:20',
            'sales_account' => 'required|string|max:20',
            'department_id' => 'required|integer',
            'order_date' => 'required|date',
            'sales_type_code' => 'required|string|max:20',
            'currency_code' => 'required|string|max:20',
            'exchange_rate' => 'required|numeric',
            'exchange_rate_base_code' => 'required|integer',
            'tax_type_code' => 'required|string|max:20',
            'trade_term_code' => 'required|string|max:20',
            'receipt_payment_term_code' => 'required|string|max:20',
            'invoice_type_code' => 'required|string|max:20',
            'pricing_method_code' => 'required|string|max:20',
            'customer_order_number' => 'nullable|string|max:50',
            'details' => 'required|array|min:1',
            'details.*.material_code' => 'required|string|max:20',
            'details.*.quantity' => 'required|numeric|min:0.01',
            'details.*.unit_price' => 'required|numeric|min:0',
            'details.*.production_quantity' => 'nullable|numeric|min:0',
            'details.*.tax_rate' => 'nullable|numeric|min:0|max:100',
            'details.*.expected_completion_date' => 'nullable|date',
            'details.*.expected_delivery_date' => 'nullable|date',
        ]);

        try {
            $orderData = $validated;
            $orderData['create_account'] = Auth::user()->account;
            $orderData['update_account'] = Auth::user()->account;
            
            $result = Order::createOrder($orderData);
            
            return response()->json([
                'status' => 'success',
                'message' => '订单创建成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '订单创建失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取订单详情
     *
     * @param string $orderCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($orderCode)
    {
        try {
            $order = Order::getOrderDetails($orderCode);
            
            return response()->json([
                'status' => 'success',
                'data' => ['order' => $order]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * 更新订单信息
     *
     * @param Request $request
     * @param string $orderCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $orderCode)
    {
        $validated = $request->validate([
            'customer_code' => 'required|string|max:20',
            'sales_account' => 'required|string|max:20',
            'department_id' => 'required|integer',
            'order_date' => 'required|date',
            'sales_type_code' => 'required|string|max:20',
            'currency_code' => 'required|string|max:20',
            'exchange_rate' => 'required|numeric',
            'exchange_rate_base_code' => 'required|integer',
            'tax_type_code' => 'required|string|max:20',
            'trade_term_code' => 'required|string|max:20',
            'receipt_payment_term_code' => 'required|string|max:20',
            'invoice_type_code' => 'required|string|max:20',
            'pricing_method_code' => 'required|string|max:20',
            'customer_order_number' => 'nullable|string|max:50',
            'details' => 'required|array|min:1',
            'details.*.material_code' => 'required|string|max:20',
            'details.*.quantity' => 'required|numeric|min:0.01',
            'details.*.unit_price' => 'required|numeric|min:0',
            'details.*.production_quantity' => 'nullable|numeric|min:0',
            'details.*.tax_rate' => 'nullable|numeric|min:0|max:100',
            'details.*.expected_completion_date' => 'nullable|date',
            'details.*.expected_delivery_date' => 'nullable|date',
        ]);

        try {
            $orderData = $validated;
            $orderData['update_account'] = Auth::user()->account;
            
            $result = Order::updateOrder($orderCode, $orderData);
            
            return response()->json([
                'status' => 'success',
                'message' => '订单更新成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '订单更新失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除订单
     *
     * @param string $orderCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($orderCode)
    {
        try {
            Order::deleteOrder($orderCode);
            
            return response()->json([
                'status' => 'success',
                'message' => '订单删除成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '订单删除失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 订单审核
     *
     * @param string $orderCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function approve($orderCode)
    {
        try {
            Order::approveOrder($orderCode, Auth::user()->account);
            
            return response()->json([
                'status' => 'success',
                'message' => '订单审核成功'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '订单审核失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 订单作废
     *
     * @param string $orderCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancel($orderCode)
    {
        try {
            Order::cancelOrder($orderCode, Auth::user()->account);
            
            return response()->json([
                'status' => 'success',
                'message' => '订单已作废'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '订单作废失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 订单结案
     *
     * @param string $orderCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function close($orderCode)
    {
        try {
            Order::closeOrder($orderCode, Auth::user()->account);
            
            return response()->json([
                'status' => 'success',
                'message' => '订单已结案'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '订单结案失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取客户列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCustomerList()
    {
        try {
            $customers = Order::getCustomerList();
            
            return response()->json([
                'status' => 'success',
                'data' => $customers
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 根据客户编号获取客户默认信息
     *
     * @param string $customerCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCustomerDefaults($customerCode)
    {
        try {
            $customerDefaults = Order::getCustomerDefaults($customerCode);

            return response()->json([
                'status' => 'success',
                'data' => $customerDefaults
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
