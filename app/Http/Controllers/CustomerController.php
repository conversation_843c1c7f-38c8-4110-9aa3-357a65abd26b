<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Currency;
use App\Models\TaxType;
use App\Models\TradeTerm;
use App\Models\PurchasePricingMethod;
use App\Models\InvoiceType;
use App\Models\ReceiptPaymentTerm;
use App\Models\AccountReceivablePayableType;
use App\Models\SalesType;
use App\Models\DomesticForeignType;
use App\Models\ExchangeRateBasis;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Models\CustomerType;
use App\Models\Region;
use Auth;

class CustomerController extends Controller
{
    /**
     * 显示客户列表
     */
    public function index(Request $request)
    {
        // 获取分页参数
        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', 10);

        // 获取排序参数
        $sortField = $request->input('sort_field');
        $sortDirection = $request->input('sort_direction', 'asc');

        // 获取搜索参数
        $search = $request->input('search');

        // 获取状态筛选参数
        $status = $request->input('status',null);

        // 限制每页数量在10-100之间
        $perPage = max(10, min(100, $perPage));

        $user = Auth::user();
        // 获取公司筛选参数
        $companyCode = $request->input('company_code');
        if (empty($companyCode)) {
            // 如果公司编码为空，则使用当前用户默认公司编码
            $companyCode = $user->default_company_code;
        } else {
            // 校验公司编码是否在授权公司列表中
            if (!$user->companies->contains('company_code', $companyCode)) {
                throw new \Exception('Company code is not authorized.');
            }
        }

        try {
            $customers = Customer::getCustomerList($companyCode, $page, $perPage, $search, $sortField, $sortDirection,$status);

            return response()->json([
                'status' => 'success',
                'data' => $customers->items(),
                'pagination' => [
                    'total' => $customers->total(),
                    'per_page' => $customers->perPage(),
                    'current_page' => $customers->currentPage(),
                    'last_page' => $customers->lastPage(),
                    'from' => $customers->firstItem() ?? 0,
                    'to' => $customers->lastItem() ?? 0,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * 获取所有客户明细数据，用于创建/编辑表单
     */
    public function getFormData()
    {
        $data = [
            'currencies' => Currency::where('status', 'Y')->get(),
            'taxTypes' => TaxType::where('status', 'Y')->get(),
            'tradeTerms' => TradeTerm::where('status', 'Y')->get(),
            'purchasePricingMethods' => PurchasePricingMethod::where('status', 'Y')->get(),
            'invoiceTypes' => InvoiceType::where('status', 'Y')->where('type','2')->get(),
            'receiptPaymentTerms' => ReceiptPaymentTerm::where('status', 'Y')->where('type', 'S')->get(),
            'accountReceivablePayableTypes' => AccountReceivablePayableType::where('status', 'Y')->where('type', 'R')->get(),
            'salesTypes' => SalesType::where('status', 'Y')->get(),
            'exchangeRateBases' => ExchangeRateBasis::where('status', 'Y')->where('type', 'S')->get(),
        ];

        return response()->json([
            'status' => 'success',
            'data' => $data
        ]);
    }

    /**
     * 保存新客户
     */
    public function store(Request $request)
    {
        \Log::info('CustomerController::store - 开始创建客户', ['request' => $request->all()]);
        $validator = Validator::make($request->all(), [
            'short_name' => 'required|string|max:100',
            'full_name' => 'required|string|max:200',
            'currency_id' => 'nullable|exists:currencies,id',
            'tax_type_id' => 'nullable|exists:tax_types,id',
            'trade_term_id' => 'nullable|exists:trade_terms,id',
            'pricing_method_id' => 'nullable|exists:pricing_methods,id',
            'invoice_type_id' => 'nullable|exists:invoice_types,id',
            'payment_term_id' => 'nullable|exists:payment_terms,id',
            'account_receivable_type_id' => 'nullable|exists:account_receivable_types,id',
            'sales_type_id' => 'nullable|exists:sales_types,id',
            'exchange_rate_basis_id' => 'nullable|exists:exchange_rate_bases,id',
            'tax_number' => 'nullable|string|max:50',
            'status' => ['required', Rule::in(['Y', 'N'])],
            'remarks' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $customer = Customer::createCustomer($request->all());

            return response()->json([
                'status' => 'success',
                'message' => '客户创建成功',
                'data' => $customer
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 显示指定客户
     */
    public function show(string $id)
    {
        $customer = Customer::with([
            'currency',
            'taxType',
            'tradeTerm',
            'pricingMethod',
            'invoiceType',
            'paymentTerm',
            'accountReceivableType',
            'salesType',
            'exchangeRateBasis'
        ])->findOrFail($id);

        return response()->json([
            'status' => 'success',
            'data' => $customer
        ]);
    }

    /**
     * 更新客户信息
     */
    public function update(Request $request, string $id)
    {
        \Log::info('CustomerController::update - 开始更新客户', ['id' => $id, 'request' => $request->all()]);
        $customer = Customer::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'short_name' => 'required|string|max:100',
            'full_name' => 'required|string|max:200',
            'currency_id' => 'nullable|exists:currencies,id',
            'tax_type_id' => 'nullable|exists:tax_types,id',
            'trade_term_id' => 'nullable|exists:trade_terms,id',
            'pricing_method_id' => 'nullable|exists:pricing_methods,id',
            'invoice_type_id' => 'nullable|exists:invoice_types,id',
            'payment_term_id' => 'nullable|exists:payment_terms,id',
            'account_receivable_type_id' => 'nullable|exists:account_receivable_types,id',
            'sales_type_id' => 'nullable|exists:sales_types,id',
            'exchange_rate_basis_id' => 'nullable|exists:exchange_rate_bases,id',
            'tax_number' => 'nullable|string|max:50',
            'status' => ['required', Rule::in(['Y', 'N'])],
            'remarks' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        // 客户编号不允许修改，排除code字段
        $data = $request->except('code', 'activity_level');

        $customer->update($data);

        return response()->json([
            'status' => 'success',
            'message' => '客户更新成功',
            'data' => $customer->fresh()
        ]);
    }

    /**
     * 删除客户（软删除）
     */
    public function destroy(string $id)
    {
        $customer = Customer::findOrFail($id);
        $customer->delete();

        return response()->json([
            'status' => 'success',
            'message' => '客户删除成功'
        ]);
    }

    public function getSimpleList()
    {
        $customers = Customer::where('status', 'Y')->get();
        return response()->json($customers);
    }
}
