<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // 验证搜索参数
        $search = $request->input('search', '');
        $status = $request->input('status', '');
        $departmentId = $request->input('department_id');
        
        try {
            $users = User::getUserList($search, $status, $departmentId);
            
            return Inertia::render('Settings/Users/<USER>', [
                'users' => $users,
                'filters' => $request->only(['search', 'status', 'department']),
            ]);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        try {
            $formData = User::getCreateFormData(Auth::user()->locale);
            
            return Inertia::render('Settings/Users/<USER>', $formData);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'account' => 'required|string|max:255',
            'locale' => 'required|exists:locale,locale',
            'default_company_code' => 'required|exists:companies,company_code',
            'company_codes' => 'required|array',
            'company_codes.*' => 'exists:companies,company_code',
            'name' => 'required|string|max:255',
            'email' => 'nullable|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'employee_id' => 'nullable|string|max:50|unique:users',
            'department_id' => 'nullable|exists:departments,id',
            'position' => 'nullable|string|max:100',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'hire_date' => 'nullable|date',
            'status' => 'required|string|in:在职,离职,休假',
            'emergency_contact' => 'nullable|string|max:50',
            'emergency_phone' => 'nullable|string|max:20',
            'notes' => 'nullable|string',
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,id',
        ]);

        try {
            User::createUser($validated);
            
            return redirect()->route('settings.users')
                ->with('success', '人员创建成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        try {
            $userData = User::getUserWithDetails($user->id, Auth::user()->locale);
            
            return Inertia::render('Settings/Users/<USER>', $userData);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        try {
            $editData = User::getEditFormData($user->id, Auth::user()->locale);
            
            return Inertia::render('Settings/Users/<USER>', $editData);
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['nullable', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'employee_id' => ['nullable', 'string', 'max:50', Rule::unique('users')->ignore($user->id)],
            'default_company_code' => 'required|exists:companies,company_code',
            'company_codes' => 'required|array',
            'company_codes.*' => 'exists:companies,company_code',
            'department_id' => 'nullable|exists:departments,id',
            'position' => 'nullable|string|max:100',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'hire_date' => 'nullable|date',
            'status' => 'required|string|in:在职,离职,休假',
            'emergency_contact' => 'nullable|string|max:50',
            'emergency_phone' => 'nullable|string|max:20',
            'notes' => 'nullable|string',
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,id',
        ]);

        try {
            User::updateUser($user->id, $validated);
            
            return redirect()->route('settings.users')
                ->with('message', '人员信息更新成功');
        } catch (\Exception $e) {
            return back()->withErrors(['message' => $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        try {
            User::deleteUser($user->id, Auth::id());
            
            return redirect()->route('settings.users')
                ->with('message', '人员已删除');
        } catch (\Exception $e) {
            return redirect()->route('settings.users')
                ->with('error', $e->getMessage());
        }
    }

    /**
     * 获取用户的公司列表
     *
     * @return \Illuminate\Http\JsonResponse 返回公司列表的JSON响应
     */
    public function getUserCompanies()
    {
        try {
            $companies = User::getUserCompanies(Auth::id(), Auth::user()->locale);
            
            return response()->json($companies);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * 获取用户列表
     *
     * @return \Illuminate\Http\JsonResponse 返回用户列表的JSON响应
     */
    public function getUserList()
    {
        try {
            $users = User::getSimpleUserList();
            
            return response()->json($users);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}