<?php

namespace App\Http\Controllers;

use App\Models\Supplier;
use App\Models\Currency;
use App\Models\TaxType;
use App\Models\TradeTerm;
use App\Models\PurchasePricingMethod;
use App\Models\InvoiceType;
use App\Models\ReceiptPaymentTerm;
use App\Models\AccountReceivablePayableType;
use App\Models\PurchaseType;
use App\Models\ExchangeRateBasis;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class SupplierController extends Controller
{
    /**
     * 显示供应商列表
     */
    public function index(Request $request)
    {
        // 获取分页参数
        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', 10);

        // 获取排序参数
        $sortField = $request->input('sort_field');
        $sortDirection = $request->input('sort_direction', 'asc');

        // 获取搜索参数
        $search = $request->input('search');

        // 获取状态筛选参数
        $status = $request->input('status', null);

        // 限制每页数量在10-100之间
        $perPage = max(10, min(100, $perPage));

        try {
            $suppliers = Supplier::getSupplierList($page, $perPage, $search, $sortField, $sortDirection, $status);

            return response()->json([
                'status' => 'success',
                'data' => $suppliers->items(),
                'pagination' => [
                    'total' => $suppliers->total(),
                    'per_page' => $suppliers->perPage(),
                    'current_page' => $suppliers->currentPage(),
                    'last_page' => $suppliers->lastPage(),
                    'from' => $suppliers->firstItem() ?? 0,
                    'to' => $suppliers->lastItem() ?? 0,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * 获取所有供应商明细数据，用于创建/编辑表单
     */
    public function getFormData()
    {
        try {
            // 获取表单需要的下拉数据
            $data = [
                'currencies' => Currency::where('status', 'Y')->get(),
                'taxTypes' => TaxType::where('status', 'Y')->get(),
                'tradeTerms' => TradeTerm::where('status', 'Y')->get(),
                'purchasePricingMethods' => PurchasePricingMethod::where('status', 'Y')->get(),
                'invoiceTypes' => InvoiceType::where('status', 'Y')->where('type','1')->get(),
                'receiptPaymentTerms' => ReceiptPaymentTerm::where('status', 'Y')->where('type', 'P')->get(),
                'accountReceivablePayableTypes' => AccountReceivablePayableType::where('status', 'Y')->where('type', 'P')->get(),
                'purchaseTypes' => PurchaseType::where('status', 'Y')->get(),
                'exchangeRateBases' => ExchangeRateBasis::where('status', 'Y')->where('type', 'P')->get(),
            ];
            
            return response()->json([
                'status' => 'success',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 保存新供应商
     */
    public function store(Request $request)
    {
        \Log::info('SupplierController::store - 开始创建供应商', ['request' => $request->all()]);
        $validator = Validator::make($request->all(), [
            'short_name' => 'required|string|max:100',
            'full_name' => 'required|string|max:200',
            'currency_id' => 'nullable|exists:currencies,id',
            'tax_type_id' => 'nullable|exists:tax_types,id',
            'trade_term_id' => 'nullable|exists:trade_terms,id',
            'pricing_method_id' => 'nullable|exists:purchase_pricing_methods,id',
            'invoice_type_id' => 'nullable|exists:invoice_types,id',
            'payment_term_id' => 'nullable|exists:receipt_payment_terms,id',
            'account_payable_type_id' => 'nullable|exists:account_receivable_payable_types,id',
            'purchase_type_id' => 'nullable|exists:purchase_types,id',
            'domestic_foreign_type_id' => 'nullable|exists:domestic_foreign_types,id',
            'exchange_rate_basis_id' => 'nullable|exists:exchange_rate_bases,id',
            'tax_number' => 'nullable|string|max:50',
            'status' => ['required', Rule::in(['Y', 'N'])],
            'remarks' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        // 自动生成供应商编号
        $supplierCode = Supplier::generateSupplierCode();

        $data = $request->all();
        $data['code'] = $supplierCode;

        $supplier = Supplier::create($data);

        return response()->json([
            'status' => 'success',
            'message' => '供应商创建成功',
            'data' => $supplier
        ], 201);
    }

    /**
     * 显示指定供应商
     */
    public function show(string $id)
    {
        $supplier = Supplier::with([
            'currency',
            'taxType',
            'tradeTerm',
            'pricingMethod',
            'invoiceType',
            'paymentTerm',
            'accountPayableType',
            'purchaseType',
            'exchangeRateBasis'
        ])->findOrFail($id);

        return response()->json([
            'status' => 'success',
            'data' => $supplier
        ]);
    }

    /**
     * 更新供应商信息
     */
    public function update(Request $request, string $id)
    {
        \Log::info('SupplierController::update - 开始更新供应商', ['id' => $id, 'request' => $request->all()]);
        $supplier = Supplier::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'short_name' => 'required|string|max:100',
            'full_name' => 'required|string|max:200',
            'currency_id' => 'nullable|exists:currencies,id',
            'tax_type_id' => 'nullable|exists:tax_types,id',
            'trade_term_id' => 'nullable|exists:trade_terms,id',
            'pricing_method_id' => 'nullable|exists:purchase_pricing_methods,id',
            'invoice_type_id' => 'nullable|exists:invoice_types,id',
            'payment_term_id' => 'nullable|exists:receipt_payment_terms,id',
            'account_payable_type_id' => 'nullable|exists:account_receivable_payable_types,id',
            'purchase_type_id' => 'nullable|exists:purchase_types,id',
            'domestic_foreign_type_id' => 'nullable|exists:domestic_foreign_types,id',
            'exchange_rate_basis_id' => 'nullable|exists:exchange_rate_bases,id',
            'tax_number' => 'nullable|string|max:50',
            'status' => ['required', Rule::in(['Y', 'N'])],
            'remarks' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        // 供应商编号不允许修改，排除code字段
        $data = $request->except('code');

        $supplier->update($data);

        return response()->json([
            'status' => 'success',
            'message' => '供应商更新成功',
            'data' => $supplier->fresh()
        ]);
    }

    /**
     * 删除供应商（软删除）
     */
    public function destroy(string $id)
    {
        $supplier = Supplier::findOrFail($id);
        $supplier->delete();

        return response()->json([
            'status' => 'success',
            'message' => '供应商删除成功'
        ]);
    }
} 