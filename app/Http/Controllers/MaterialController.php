<?php

namespace App\Http\Controllers;

use App\Models\Material;
use Illuminate\Http\Request;

class MaterialController extends Controller
{
    /**
     * 获取产品分类
     * 
     * @param Request $request 请求对象
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductCategory(Request $request)
    {
        // 验证请求参数
        $validated = $request->validate([
            'company_code' => 'required|string|max:10',
        ]);

        try {
            $categories = Material::getProductCategory($validated['company_code']);
            
            return response()->json([
                'status' => 'success',
                'data' => $categories
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取料件列表
     *
     * @param Request $request 请求对象
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMaterialList(Request $request)
    {
        // 验证请求参数
        $validated = $request->validate([
            'company_code' => 'required|string|max:10',
            'search' => 'nullable|string|max:255',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:10|max:100',
            'sort_field' => 'nullable|string|in:category_code,category_name,figure,product_name,specification,material_code',
            'sort_direction' => 'nullable|string|in:asc,desc',
            'categories' => 'nullable|string',
        ]);

        try {
            // 处理分类参数
            $categories = null;
            if (!empty($validated['categories'])) {
                $categories = explode(',', $validated['categories']);
                $categories = array_filter($categories); // 移除空值
            }

            $materials = Material::getMaterialList(
                $validated['company_code'],
                $validated['page'] ?? 1,
                $validated['per_page'] ?? 10,
                $validated['search'] ?? null,
                $validated['sort_field'] ?? null,
                $validated['sort_direction'] ?? 'asc',
                $categories
            );

            return response()->json([
                'status' => 'success',
                'data' => $materials->items(),
                'pagination' => [
                    'total' => $materials->total(),
                    'per_page' => $materials->perPage(),
                    'current_page' => $materials->currentPage(),
                    'last_page' => $materials->lastPage(),
                    'from' => $materials->firstItem() ?? 0,
                    'to' => $materials->lastItem() ?? 0,
                ],
                'filters' => [
                    'search' => $validated['search'] ?? null,
                    'categories' => $categories,
                    'company_code' => $validated['company_code'],
                ],
                'sort' => [
                    'field' => $validated['sort_field'] ?? null,
                    'direction' => $validated['sort_direction'] ?? 'asc'
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
