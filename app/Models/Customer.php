<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 与模型关联的表
     *
     * @var string
     */
    protected $table = 'customers';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_code',
        'code',
        'short_name',
        'full_name',
        'currency_id',
        'tax_type_id',
        'trade_term_id',
        'pricing_method_id',
        'invoice_type_id',
        'payment_term_id',
        'account_receivable_type_id',
        'sales_type_id',
        'exchange_rate_basis_id',
        'tax_number',
        'status',
        'activity_level',
        'remarks',
    ];

    /**
     * 隐藏的属性
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 币种关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    /**
     * 税种关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function taxType()
    {
        return $this->belongsTo(TaxType::class);
    }

    /**
     * 交易条件关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function tradeTerm()
    {
        return $this->belongsTo(TradeTerm::class);
    }

    /**
     * 取价方式关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function pricingMethod()
    {
        return $this->belongsTo(SalesPricingMethod::class);
    }

    /**
     * 发票类型关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function invoiceType()
    {
        return $this->belongsTo(InvoiceType::class);
    }

    /**
     * 收款条件关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function paymentTerm()
    {
        return $this->belongsTo(ReceiptPaymentTerm::class);
    }

    /**
     * 应收账款类别关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function accountReceivableType()
    {
        return $this->belongsTo(AccountReceivablePayableType::class);
    }

    /**
     * 销售分类关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function salesType()
    {
        return $this->belongsTo(SalesType::class);
    }

    /**
     * 汇率计算基准关联
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function exchangeRateBasis()
    {
        return $this->belongsTo(ExchangeRateBasis::class);
    }

    /**
     * 生成客户编号
     * 
     * @return string
     */
    public static function generateCustomerCode()
    {
        $prefix = 'C';
        $latestCustomer = self::withTrashed()->orderBy('id', 'desc')->first();
        
        if (!$latestCustomer) {
            return $prefix . '00001';
        }
        
        $lastCode = $latestCustomer->code;
        if (preg_match('/^C(\d+)$/', $lastCode, $matches)) {
            $number = (int)$matches[1];
            $number++;
            return $prefix . str_pad($number, 5, '0', STR_PAD_LEFT);
        }
        
        return $prefix . '00001';
    }

    /**
     * 创建新客户
     * 
     * @param array $data 客户数据
     * @return Customer
     */
    public static function createCustomer(array $data)
    {
        // 自动生成客户编号
        $customerCode = self::generateCustomerCode();
        
        $data['code'] = $customerCode;
        $data['activity_level'] = 0; // 新客户活跃度默认为0
        
        return self::create($data);
    }

    /**
     * 获取客户列表
     *
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @param string|null $search 搜索关键词
     * @param string|null $sortField 排序字段
     * @param string $sortDirection 排序方向 (asc|desc)
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public static function getCustomerList(
        string $companyCode,
        int $page = 1,
        int $perPage = 10,
        ?string $search = null,
        ?string $sortField = null,
        string $sortDirection = 'asc',
        ?string $status = null
    ) {
        // 映射前端排序字段 => 数据库字段
        $dbFieldMap = [
            'code' => 'code',
            'short_name' => 'short_name',
            'full_name' => 'full_name',
            'currency' => 'currency_id',
            'tax_type' => 'tax_type_id',
            'trade_term' => 'trade_term_id',
            'pricing_method' => 'pricing_method_id',
            'invoice_type' => 'invoice_type_id',
            'payment_term' => 'payment_term_id',
            'account_receivable_type' => 'account_receivable_type_id',
            'sales_type' => 'sales_type_id',
            'exchange_rate_basis' => 'exchange_rate_basis_id',
        ];
        
        // 验证排序方向
        if (!in_array(strtolower($sortDirection), ['asc', 'desc'])) {
            $sortDirection = 'asc';
        }
        
        $query = Customer::query()
            ->where('company_code', $companyCode);

        // 搜索条件
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('code', 'like', "%{$search}%")
                  ->orWhere('short_name', 'like', "%{$search}%")
                  ->orWhere('full_name', 'like', "%{$search}%");
            });
        }

        if ($status) {
            $query->where('status', $status);
        }

        if ($sortField) {
            $field = $dbFieldMap[$sortField] ?? 'created_at';
            $query->orderBy($field, $sortDirection);
        } else {
            $query->orderBy('code', 'asc');
        }

        try {
            $customers = $query->paginate($perPage, ['*'], 'page', $page);

            return $customers;
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }   
    }
}
