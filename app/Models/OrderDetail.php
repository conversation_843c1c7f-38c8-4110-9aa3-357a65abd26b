<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
class OrderDetail extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 与模型关联的表
     *
     * @var string
     */
    protected $table = 'order_details';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_code',
        'order_code',
        'order_item',
        'material_code',
        'quantity',
        'production_quantity',
        'unit_price',
        'standard_unit_price',
        'tax_type_code',
        'tax_rate',
        'tax_amount',
        'total_price_tax',
        'total_price_no_tax',
        'clear_table_number',
        'customer_material_code',
        'customer_material_specification',
        'color',
        'brand',
        'description',
        'inner_packing',
        'outer_packing',
        'warning',
        'borrow',
        'expected_completion_date',
        'expected_delivery_date',
        'actual_completion_date',
        'actual_delivery_date',
        'order_price_id',
        'order_price_item',
        'create_account',
        'update_account',
        'status',
        'remark',
        'remark_2',
    ];

    /**
     * 隐藏的属性
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 应该转换为日期的属性
     *
     * @var array<int, string>
     */
    protected $dates = [
        'expected_completion_date',
        'expected_delivery_date',
        'actual_completion_date',
        'actual_delivery_date',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 获取订单明细所属的订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_code', 'order_code');
    }

    /**
     * 获取订单明细的物料信息
     */
    public function material()
    {
        return $this->belongsTo(Material::class, 'material_code', 'material_code');
    }

    /**
     * 获取订单明细的税种
     */
    public function taxType()
    {
        return $this->belongsTo(TaxType::class, 'tax_type_code', 'tax_type_code');
    }

    /**
     * 获取订单明细的颜色
     */
    public function color()
    {
        return $this->belongsTo(Colors::class, 'color_code', 'color_code');
    }

    /**
     * 获取创建用户
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'create_account', 'account');
    }

    /**
     * 获取更新用户
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'update_account', 'account');
    }
}
