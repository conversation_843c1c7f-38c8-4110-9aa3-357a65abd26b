<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Department extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'parent_id',
        'description',
        'status',
        'order',
    ];

    /**
     * 获取上级部门
     */
    public function parent()
    {
        return $this->belongsTo(Department::class, 'parent_id');
    }

    /**
     * 获取子部门
     */
    public function children()
    {
        return $this->hasMany(Department::class, 'parent_id');
    }

    /**
     * 获取部门主管（多个）
     */
    public function managers()
    {
        return $this->belongsToMany(User::class, 'department_manager', 'department_id', 'user_id')
                    ->withTimestamps();
    }

    /**
     * 获取部门员工
     */
    public function employees()
    {
        return $this->hasMany(User::class, 'department_id');
    }

    /**
     * 获取部门列表
     * 
     * @param string $search 搜索关键词
     * @param string $status 状态筛选
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public static function getDepartmentList(string $search = '', string $status = '')
    {
        $query = self::query()
            ->with(['parent', 'managers'])
            ->orderBy('order', 'asc');

        // 搜索过滤
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // 状态过滤
        if (!empty($status)) {
            $query->where('status', $status);
        }

        return $query->paginate(50)->withQueryString();
    }

    /**
     * 获取表单数据
     * 
     * @return array
     */
    public static function getFormData()
    {
        $departments = self::where('status', '正常')->get();
        $managers = User::where('status', '在职')->get();

        return [
            'departments' => $departments,
            'managers' => $managers,
        ];
    }

    /**
     * 创建部门
     * 
     * @param array $data 部门数据
     * @return Department
     */
    public static function createDepartment(array $data)
    {
        // 分离主管ID数组
        $managerIds = $data['manager_ids'] ?? [];
        unset($data['manager_ids']);

        // 创建部门
        $department = self::create($data);

        // 关联主管
        if (!empty($managerIds)) {
            $department->managers()->attach($managerIds);
        }

        return $department;
    }

    /**
     * 检查部门是否可以删除
     * 
     * @param int $departmentId 部门ID
     * @return array ['canDelete' => bool, 'message' => string]
     */
    public static function canDelete(int $departmentId)
    {
        // 检查是否有子部门
        $hasChildren = self::where('parent_id', $departmentId)->exists();
        
        if ($hasChildren) {
            return ['canDelete' => false, 'message' => '无法删除存在子部门的部门'];
        }
        
        // 检查是否有关联的员工
        $hasEmployees = User::where('department_id', $departmentId)->exists();
        
        if ($hasEmployees) {
            return ['canDelete' => false, 'message' => '无法删除存在员工的部门'];
        }

        return ['canDelete' => true, 'message' => ''];
    }

    /**
     * 删除部门
     * 
     * @param int $departmentId 部门ID
     * @return bool
     * @throws \Exception
     */
    public static function deleteDepartment(int $departmentId)
    {
        $checkResult = self::canDelete($departmentId);
        
        if (!$checkResult['canDelete']) {
            throw new \Exception($checkResult['message']);
        }

        $department = self::findOrFail($departmentId);
        
        // 删除部门前先解除与主管的关联
        $department->managers()->detach();
        
        return $department->delete();
    }
}
