<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Role extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
    ];

    /**
     * 该角色拥有的所有权限
     */
    public function permissions()
    {
        return $this->belongsToMany(Permission::class);
    }

    /**
     * 该角色的所有用户
     */
    public function users()
    {
        return $this->belongsToMany(User::class);
    }

    /**
     * 获取角色列表
     * 
     * @param string $search 搜索关键词
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public static function getRoleList(string $search = '')
    {
        $query = self::query()
            ->withCount(['users', 'permissions']);

        if (!empty($search)) {
            $query->where(function($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        return $query->orderBy('id')->paginate(20)->withQueryString();
    }

    /**
     * 获取创建表单数据
     * 
     * @return array
     */
    public static function getCreateFormData()
    {
        $permissions = Permission::select('id', 'name', 'description')->get();
        
        return [
            'permissions' => $permissions,
        ];
    }

    /**
     * 创建角色
     * 
     * @param array $data 角色数据
     * @return Role
     * @throws \Exception
     */
    public static function createRole(array $data)
    {
        DB::beginTransaction();
        
        try {
            $role = self::create([
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
            ]);

            if (!empty($data['permissions'])) {
                $role->permissions()->attach($data['permissions']);
            }

            DB::commit();
            
            return $role;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 获取角色详情及其用户
     * 
     * @param int $roleId 角色ID
     * @return array
     */
    public static function getRoleWithUsers(int $roleId)
    {
        $role = self::with('permissions')->findOrFail($roleId);
        
        // 获取有此角色的用户
        $users = User::whereHas('roles', function($query) use ($roleId) {
                    $query->where('roles.id', $roleId);
                })
                ->leftJoin('departments', 'users.department_id', '=', 'departments.id')
                ->select('users.id', 'users.name', 'users.email', 'users.employee_id', 
                         'departments.name as department', 'users.position')
                ->paginate(5);

        return [
            'role' => $role,
            'users' => $users,
        ];
    }

    /**
     * 获取编辑表单数据
     * 
     * @param int $roleId 角色ID
     * @return array
     * @throws \Exception
     */
    public static function getEditFormData(int $roleId)
    {
        $role = self::with('permissions')->findOrFail($roleId);
        
        // 禁止编辑管理员角色
        if ($role->name === 'admin') {
            throw new \Exception('管理员角色不可编辑');
        }
        
        $permissions = Permission::select('id', 'name', 'description')->get();
        
        return [
            'role' => $role,
            'permissions' => $permissions,
            'selectedPermissions' => $role->permissions->pluck('id'),
        ];
    }

    /**
     * 更新角色
     * 
     * @param int $roleId 角色ID
     * @param array $data 角色数据
     * @return Role
     * @throws \Exception
     */
    public static function updateRole(int $roleId, array $data)
    {
        $role = self::findOrFail($roleId);
        
        // 禁止更新管理员角色
        if ($role->name === 'admin') {
            throw new \Exception('管理员角色不可编辑');
        }

        DB::beginTransaction();
        
        try {
            $role->update([
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
            ]);

            $role->permissions()->sync($data['permissions'] ?? []);

            DB::commit();
            
            return $role;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 删除角色
     * 
     * @param int $roleId 角色ID
     * @throws \Exception
     */
    public static function deleteRole(int $roleId)
    {
        $role = self::findOrFail($roleId);
        
        // 禁止删除管理员角色
        if ($role->name === 'admin') {
            throw new \Exception('管理员角色不可删除');
        }
        
        // 检查是否有用户使用此角色
        if ($role->users()->count() > 0) {
            throw new \Exception('无法删除有用户使用的角色');
        }

        DB::beginTransaction();
        
        try {
            $role->permissions()->detach();
            $role->delete();
            
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 为角色分配用户
     * 
     * @param int $roleId 角色ID
     * @param array $userIds 用户ID数组
     * @throws \Exception
     */
    public static function assignUsers(int $roleId, array $userIds)
    {
        $role = self::findOrFail($roleId);
        
        $role->users()->sync($userIds);
    }
} 