<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Material extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表
     *
     * @var string
     */
    protected $table = 'materials';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_code',
        'material_code',
        'unit',
        'category_code',
        'gross_weight',
        'net_weight',
        'paint_area',
        'work_hours',
        'length',
        'width',
        'height',
        'supply_type',
        'process_type',
        'status',
        'remark'
    ];

    /**
     * 验证公司编码是否有效且用户有权限访问
     * 
     * @param string $companyCode 公司编码
     * @throws \Exception
     */
    public static function validateCompanyCode(string $companyCode)
    {
        if (empty($companyCode)) {
            throw new \Exception('Company code is required.');
        }

        $user = Auth::user();
        if (!$user || !$user->companies->contains('company_code', $companyCode)) {
            throw new \Exception('Company code is not authorized.');
        }
    }

    /**
     * 获取产品分类
     * 
     * @param string $companyCode 公司代码
     * @return \Illuminate\Database\Eloquent\Collection
     * @throws \Exception
     */
    public static function getProductCategory(string $companyCode)
    {
        self::validateCompanyCode($companyCode);

        $query = self::where('materials.company_code', '=', $companyCode)
                ->join('categories', function ($join) {
                    $join->on('categories.company_code', '=', 'materials.company_code')
                        ->on('categories.category_code', '=', 'materials.category_code')
                        ->where('categories.locale', '=', 'zh_CN');
                })
                ->select('categories.category_code as value', 'categories.category_name as label')
                ->groupBy('categories.category_code', 'categories.category_name')
                ->orderBy('categories.category_code');
    
        return $query->get();
    }

    /**
     * 获取料件列表
     * 
     * @param string $companyCode 公司代码
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @param string|null $search 搜索关键词
     * @param string|null $sortField 排序字段
     * @param string $sortDirection 排序方向 (asc|desc)
     * @param array|null $categories 产品分类筛选
     * @return \Illuminate\Pagination\LengthAwarePaginator
     * @throws \Exception
     */
    public static function getMaterialList(string $companyCode, int $page = 1, int $perPage = 10, ?string $search = null, ?string $sortField = null, string $sortDirection = 'asc', ?array $categories = null)
    {
        self::validateCompanyCode($companyCode);

        // 验证排序方向
        if (!in_array(strtolower($sortDirection), ['asc', 'desc'])) {
            $sortDirection = 'asc';
        }
        
        // 限制每页数量在10-100之间
        $perPage = max(10, min(100, $perPage));

        // 根据前端字段名映射到数据库字段名
        $dbFieldMap = [
            'category_code' => 'materials.category_code',
            'category_name' => 'categories.category_name',
            'figure'        => 'materials.figure',
            'product_name'  => 'material_translations.product_name',
            'specification' => 'material_translations.specification',
            'material_code' => 'materials.material_code',
        ];

        $query = self::where('materials.status', '=', 'Y')
            ->where('materials.company_code', '=', $companyCode)
            ->join('categories', function ($join) {
                $join->on('categories.company_code', '=', 'materials.company_code')
                    ->on('categories.category_code', '=', 'materials.category_code')
                    ->where('categories.locale', '=', 'zh_CN');
            })
            ->join('material_translations', function ($join) {
                $join->on('material_translations.company_code', '=', 'materials.company_code')
                    ->on('material_translations.material_code', '=', 'materials.material_code')
                    ->where('material_translations.locale', '=', 'zh_CN');
            })
            ->select(
                'materials.category_code',
                'categories.category_name',
                'materials.figure', 
                'material_translations.product_name', 
                'material_translations.specification',
                'materials.material_code', 
            );
            
        // 添加产品分类筛选条件
        if ($categories && is_array($categories) && count($categories) > 0) {
            $query->whereIn('materials.category_code', $categories);
        }
        
        // 添加搜索条件
        if ($search) {
            $query->where(function($query) use ($search) {
                $query->where('categories.category_name', 'like', "%{$search}%")
                    ->orWhere('materials.figure', 'like', "%{$search}%")
                    ->orWhere('material_translations.product_name', 'like', "%{$search}%")
                    ->orWhere('material_translations.specification', 'like', "%{$search}%")
                    ->orWhere('materials.material_code', 'like', "%{$search}%");
            });
        }
        
        // 添加排序
        if ($sortField && isset($dbFieldMap[$sortField])) {
            // 如果是有效的排序字段，添加排序条件
            $query->orderBy($dbFieldMap[$sortField], $sortDirection);
        } else {
            // 默认排序
            $query->orderBy('categories.category_code')
                ->orderBy('materials.figure')
                ->orderBy('materials.material_code');
        }
        
        // 使用分页
        return $query->paginate($perPage, ['*'], 'page', $page);
    }
}