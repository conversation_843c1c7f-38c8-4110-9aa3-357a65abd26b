<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class Order extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 与模型关联的表
     *
     * @var string
     */
    protected $table = 'orders';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_code',
        'order_code',
        'customer_code',
        'sales_account',
        'department_id',
        'order_date',
        'customer_order_number',
        'sales_type_code',
        'currency_code',
        'exchange_rate',
        'exchange_rate_base_code',
        'tax_type_code',
        'tax_rate',
        'trade_term_code',
        'receipt_payment_term_code',
        'invoice_type_code',
        'pricing_method_code',
        'create_account',
        'update_account',
        'status',
        'remark',
    ];

    /**
     * 隐藏的属性
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 应该转换为日期的属性
     *
     * @var array<int, string>
     */
    protected $dates = [
        'order_date',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 获取订单的客户信息
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_code', 'customer_code');
    }

    /**
     * 获取订单的销售人员
     */
    public function salesPerson()
    {
        return $this->belongsTo(User::class, 'sales_account', 'account');
    }

    /**
     * 获取订单的部门
     */
    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id', 'id');
    }

    /**
     * 获取订单的明细信息
     */
    public function details()
    {
        return $this->hasMany(OrderDetail::class, 'order_code', 'order_code');
    }

    /**
     * 获取订单的币种
     */
    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_code', 'currency_code');
    }

    /**
     * 获取订单的汇率基准
     */
    public function exchangeRateBasis()
    {
        return $this->belongsTo(ExchangeRateBasis::class, 'exchange_rate_base_code', 'exchange_rate_base_code');
    }

    /**
     * 获取订单的税种
     */
    public function taxType()
    {
        return $this->belongsTo(TaxType::class, 'tax_type_code', 'tax_type_code');
    }

    /**
     * 获取订单的交易条件
     */
    public function tradeTerm()
    {
        return $this->belongsTo(TradeTerm::class, 'trade_term_code', 'trade_term_code');
    }

    /**
     * 获取订单的收款条件
     */
    public function receiptPaymentTerm()
    {
        return $this->belongsTo(ReceiptPaymentTerm::class, 'receipt_payment_term_code', 'receipt_payment_term_code');
    }

    /**
     * 获取订单的发票类型
     */
    public function invoiceType()
    {
        return $this->belongsTo(InvoiceType::class, 'invoice_type_code', 'invoice_type_code');
    }

    /**
     * 获取订单的取价方式
     */
    public function pricingMethod()
    {
        return $this->belongsTo(SalesPricingMethod::class, 'pricing_method_code', 'sales_pricing_method_code');
    }

    /**
     * 获取订单的销售类型
     */
    public function salesType()
    {
        return $this->belongsTo(SalesType::class, 'sales_type_code', 'sales_type_code');
    }

    /**
     * 获取创建用户
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'create_account', 'account');
    }

    /**
     * 获取更新用户
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'update_account', 'account');
    }

    /**
     * 验证公司编码是否有效且用户有权限访问
     * 
     * @param string $companyCode 公司编码
     * @throws \Exception
     */
    public static function validateCompanyCode(string $companyCode)
    {
        if (empty($companyCode)) {
            throw new \Exception('Company code is required.');
        }

        $user = Auth::user();
        if (!$user || !$user->companies->contains('company_code', $companyCode)) {
            throw new \Exception('Company code is not authorized.');
        }
    }

    /**
     * 获取订单列表
     * 
     * @param string $companyCode 公司编码
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @param array $departmentId 部门ID数组
     * @param array $salesAccount 销售员账号数组
     * @param array $customerCode 客户编码数组
     * @param string|null $status 状态
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @param string|null $sortField 排序字段
     * @param string $sortDirection 排序方向
     * @return \Illuminate\Pagination\LengthAwarePaginator
     * @throws \Exception
     */
    public static function getOrderList(
        string $companyCode,
        string $startDate,
        string $endDate,
        array $departmentId,
        array $salesAccount,
        array $customerCode,
        ?string $status,
        int $page = 1, 
        int $perPage = 10, 
        ?string $sortField = 'order_date', 
        ?string $sortDirection = 'desc'
    ) {
        // 如果没有提供公司编码，使用当前用户默认公司编码
        if (empty($companyCode)) {
            $companyCode = Auth::user()->default_company_code;
        } else {
            self::validateCompanyCode($companyCode);
        }

        // 验证排序方向
        if (!in_array(strtolower($sortDirection), ['asc', 'desc'])) {
            $sortDirection = 'desc';
        }
        
        // 限制每页数量在10-100之间
        $perPage = max(10, min(100, $perPage));

        // 字段映射关系
        $dbFieldMap = [
            'order_code' => 'orders.order_code',
            'customer_code' => 'orders.customer_code',
            'customer_name' => 'customers.short_name',
            'sales_account' => 'orders.sales_account',
            'sales_name' => 'users.name',
            'department_name' => 'departments.name',
            'order_date' => 'orders.order_date',
            'status' => 'orders.status',
            'currency_code' => 'orders.currency_code',
            'exchange_rate' => 'orders.exchange_rate',
        ];

        $query = self::query()
            ->leftJoin('customers', 'orders.customer_code', '=', 'customers.code')
            ->leftJoin('users', 'orders.sales_account', '=', 'users.account')
            ->leftJoin('departments', 'orders.department_id', '=', 'departments.id')
            ->where('orders.company_code', $companyCode)
            ->whereBetween('orders.order_date', [$startDate, $endDate])
            ->select(
                'orders.order_code',
                'orders.customer_code',
                'customers.short_name as customer_name',
                'orders.sales_account',
                'users.name as sales_name',
                'departments.name as department_name',
                'orders.order_date',
                'orders.status',
                'orders.currency_code',
                'orders.exchange_rate',
                'orders.customer_order_number'
            );

        // 添加筛选条件
        if (!empty($departmentId)) {
            $query->whereIn('orders.department_id', $departmentId);
        }

        if (!empty($salesAccount)) {
            $query->whereIn('orders.sales_account', $salesAccount);
        }

        if (!empty($customerCode)) {
            $query->whereIn('orders.customer_code', $customerCode);
        }

        if (!empty($status)) {
            $query->where('orders.status', $status);
        }

        // 添加排序
        if ($sortField && isset($dbFieldMap[$sortField])) {
            $query->orderBy($dbFieldMap[$sortField], $sortDirection);
        } else {
            $query->orderBy('orders.order_date', 'desc');
        }

        return $query->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * 获取订单表单所需的下拉选项数据
     * 
     * @param string $companyCode 公司编码
     * @return array
     * @throws \Exception
     */
    public static function getFormData(string $companyCode)
    {
        if (empty($companyCode)) {
            $companyCode = Auth::user()->default_company_code;
        }

        // 获取客户列表
        $customers = Customer::where('company_code', $companyCode)
            ->where('status', 'Y')
            ->select('code as customer_code', 'short_name as customer_name')
            ->orderBy('code')
            ->get();
            
        // 获取销售员列表
        $salesPersons = [Auth::user()->only(['account', 'name'])];
            
        // 获取相关码表数据
        $departments = Department::where('id', Auth::user()->department_id)->select('id', 'name')->get();
        $currencies = Currency::select('code as currency_code', 'name as currency_name')->get();
        $exchangeRateBases = ExchangeRateBasis::where('type', 'S')->select('id as exchange_rate_base_code', 'name as exchange_rate_base_name')->get();
        $taxTypes = TaxType::select('code as tax_type_code', 'name as tax_type_name', 'rate as tax_rate','category as tax_category')->get();
        $tradeTerms = TradeTerm::select('code as trade_term_code', 'name as trade_term_name')->get();
        $paymentTerms = ReceiptPaymentTerm::where('type', 'S')->select('code as receipt_payment_term_code', 'name as receipt_payment_term_name')->get();
        $invoiceTypes = InvoiceType::where('type', '2')->select('code as invoice_type_code', 'name as invoice_type_name')->get();
        $pricingMethods = SalesPricingMethod::select('code as sales_pricing_method_code', 'name as sales_pricing_method_name')->get();
        $salesTypes = SalesType::select('code as sales_type_code', 'name as sales_type_name')->get();
        
        return [
            'customers' => $customers,
            'salesPersons' => $salesPersons,
            'departments' => $departments,
            'currencies' => $currencies,
            'exchangeRateBases' => $exchangeRateBases,
            'taxTypes' => $taxTypes,
            'tradeTerms' => $tradeTerms,
            'paymentTerms' => $paymentTerms,
            'invoiceTypes' => $invoiceTypes,
            'pricingMethods' => $pricingMethods,
            'salesTypes' => $salesTypes,
        ];
    }

    /**
     * 获取物料列表（用于订单明细选择）
     * 
     * @param string $companyCode 公司编码
     * @param string $search 搜索关键词
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array
     * @throws \Exception
     */
    public static function getMaterials(string $companyCode, string $search = '', int $page = 1, int $perPage = 20)
    {
        if (empty($companyCode)) {
            $companyCode = Auth::user()->default_company_code;
        }

        $query = Material::where('materials.company_code', $companyCode)
            ->where('materials.status', 'Y')
            ->join('material_translations', function ($join) use ($companyCode) {
                $join->on('material_translations.material_code', '=', 'materials.material_code')
                    ->where('material_translations.company_code', '=', $companyCode)
                    ->where('material_translations.locale', '=', Auth::user()->locale);
            });
            
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('materials.figure', 'like', "%$search%")
                  ->orWhere('materials.material_code', 'like', "%$search%")
                  ->orWhere('material_translations.product_name', 'like', "%$search%")
                  ->orWhere('material_translations.specification', 'like', "%$search%");
            });
        } else {
            $query->where('materials.material_code', 'like', '1%');
        }
        
        $total = $query->count();
        $materials = $query->orderBy('materials.material_code')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get(['materials.material_code','materials.unit', 'material_translations.product_name', 'material_translations.specification']);
            
        return [
            'materials' => $materials,
            'total' => $total
        ];
    }

    /**
     * 获取客户列表
     * 
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getCustomerList()
    {
        return Customer::where('status', 'Y')->get(['code', 'short_name', 'full_name']);
    }

    /**
     * 根据客户编号获取客户默认信息
     * 
     * @param string $customerCode 客户编码
     * @return array
     * @throws \Exception
     */
    public static function getCustomerDefaults(string $customerCode)
    {
        $customer = Customer::with([
            'currency',
            'taxType',
            'tradeTerm',
            'pricingMethod',
            'invoiceType',
            'paymentTerm',
            'salesType',
            'exchangeRateBasis'
        ])->where('code', $customerCode)->firstOrFail();

        return [
            'sales_type_code' => $customer->salesType ? $customer->salesType->code : null,
            'currency_code' => $customer->currency ? $customer->currency->code : null,
            'exchange_rate_base_code' => $customer->exchangeRateBasis ? $customer->exchangeRateBasis->id : null,
            'tax_type_code' => $customer->taxType ? $customer->taxType->code : null,
            'tax_rate' => $customer->taxType ? $customer->taxType->rate : 0,
            'trade_term_code' => $customer->tradeTerm ? $customer->tradeTerm->code : null,
            'receipt_payment_term_code' => $customer->paymentTerm ? $customer->paymentTerm->code : null,
            'invoice_type_code' => $customer->invoiceType ? $customer->invoiceType->code : null,
            'pricing_method_code' => $customer->pricingMethod ? $customer->pricingMethod->code : null,
            'invoice_title' => $customer->invoice_title ?? '',
            'invoice_content' => $customer->invoice_content ?? '',
            'invoice_address' => $customer->invoice_address ?? '',
            'invoice_phone' => $customer->invoice_phone ?? '',
            'invoice_bank' => $customer->invoice_bank ?? ''
        ];
    }

    /**
     * 获取订单详情
     * 
     * @param string $orderCode 订单编码
     * @return Order
     * @throws \Exception
     */
    public static function getOrderDetails(string $orderCode)
    {
        $order = self::with([
            'customer', 
            'salesPerson', 
            'department',
            'details.material'
        ])->where('order_code', $orderCode)->first();
        
        if (!$order) {
            throw new \Exception('订单不存在');
        }
        
        return $order;
    }

    /**
     * 生成订单编号
     * 
     * @param string $companyCode 公司编码
     * @return string
     */
    public static function generateOrderCode(string $companyCode): string
    {
        $today = date('ymd');
        $latestOrder = self::where('company_code', $companyCode)
            ->where('order_code', 'like', "{$companyCode}{$today}%")
            ->orderBy('order_code', 'desc')
            ->first();

        if ($latestOrder) {
            $sequence = (int)substr($latestOrder->order_code, -4) + 1;
        } else {
            $sequence = 1;
        }
        
        return $companyCode . $today . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 计算税额和价格
     * 
     * @param array $detail 明细数据
     * @param string $taxCategory 税种分类
     * @param float $defaultTaxRate 默认税率
     * @return array
     */
    private static function calculateTaxAndPrice(array $detail, string $taxCategory, float $defaultTaxRate): array
    {
        $taxRate = isset($detail['tax_rate']) ? (float)$detail['tax_rate'] : $defaultTaxRate;
        $unitPrice = (float)$detail['unit_price'];
        $quantity = (float)$detail['quantity'];
        
        // 根据税种分类计算价格
        switch ($taxCategory) {
            case 'A': // 零税
                $unitPriceNoTax = $unitPrice;
                $unitPriceTax = $unitPrice;
                $totalPriceNoTax = $unitPriceNoTax * $quantity;
                $totalPriceTax = $totalPriceNoTax;
                $taxAmount = 0;
                break;
            
            case 'B': // 含税
                $unitPriceTax = $unitPrice;
                $unitPriceNoTax = $unitPrice / (1 + $taxRate / 100);
                $totalPriceTax = $unitPriceTax * $quantity;
                $totalPriceNoTax = $unitPriceNoTax * $quantity;
                $taxAmount = $totalPriceTax - $totalPriceNoTax;
                break;
            
            case 'C': // 不含税
                $unitPriceNoTax = $unitPrice;
                $unitPriceTax = $unitPrice * (1 + $taxRate / 100);
                $totalPriceNoTax = $unitPriceNoTax * $quantity;
                $totalPriceTax = $unitPriceTax * $quantity;
                $taxAmount = $totalPriceTax - $totalPriceNoTax;
                break;
            
            default: // 默认含税
                $unitPriceTax = $unitPrice;
                $unitPriceNoTax = $unitPrice / (1 + $taxRate / 100);
                $totalPriceTax = $unitPriceTax * $quantity;
                $totalPriceNoTax = $unitPriceNoTax * $quantity;
                $taxAmount = $totalPriceTax - $totalPriceNoTax;
                break;
        }

        return [
            'tax_rate' => $taxRate,
            'total_price_no_tax' => round($totalPriceNoTax, 4),
            'total_price_tax' => round($totalPriceTax, 4),
            'tax_amount' => round($taxAmount, 4),
        ];
    }

    /**
     * 创建订单
     * 
     * @param array $data 订单数据
     * @return array
     * @throws \Exception
     */
    public static function createOrder(array $data): array
    {
        DB::beginTransaction();
        
        try {
            $companyCode = $data['company_code'];
            $orderCode = self::generateOrderCode($companyCode);

            // 创建订单主记录
            $order = new self();
            $order->company_code = $companyCode;
            $order->order_code = $orderCode;
            $order->customer_code = $data['customer_code'];
            $order->sales_account = $data['sales_account'];
            $order->department_id = $data['department_id'];
            $order->order_date = $data['order_date'];
            $order->sales_type_code = $data['sales_type_code'];
            $order->currency_code = $data['currency_code'];
            $order->exchange_rate = $data['exchange_rate'];
            $order->exchange_rate_base_code = $data['exchange_rate_base_code'];
            $order->tax_type_code = $data['tax_type_code'];
            $order->tax_rate = $data['tax_rate'] ?? 0;
            $order->trade_term_code = $data['trade_term_code'];
            $order->receipt_payment_term_code = $data['receipt_payment_term_code'];
            $order->invoice_type_code = $data['invoice_type_code'];
            $order->pricing_method_code = $data['pricing_method_code'];
            $order->customer_order_number = $data['customer_order_number'] ?? null;
            $order->create_account = $data['create_account'];
            $order->update_account = $data['update_account'];
            $order->status = 'N'; // 默认未审核状态
            $order->remark = $data['remark'] ?? null;
            $order->save();

            // 创建订单明细记录
            $detailsData = $data['details'];
            $orderItems = [];
            $taxCategory = $data['tax_category'] ?? 'B'; // 默认含税
            $defaultTaxRate = (float)($data['tax_rate'] ?? 0);

            foreach ($detailsData as $index => $detail) {
                $quantity = (float)$detail['quantity'];
                $productionQuantity = isset($detail['production_quantity']) ? (float)$detail['production_quantity'] : $quantity;
                
                // 计算税额和价格
                $priceData = self::calculateTaxAndPrice($detail, $taxCategory, $defaultTaxRate);

                $orderDetail = new OrderDetail();
                $orderDetail->company_code = $companyCode;
                $orderDetail->order_code = $orderCode;
                $orderDetail->order_item = $index + 1;
                $orderDetail->material_code = $detail['material_code'];
                $orderDetail->quantity = $quantity;
                $orderDetail->production_quantity = $productionQuantity;
                $orderDetail->unit_price = (float)$detail['unit_price'];
                $orderDetail->standard_unit_price = 0; // 默认标准单价为0
                $orderDetail->tax_type_code = $detail['tax_type_code'] ?? $order->tax_type_code;
                $orderDetail->tax_rate = $priceData['tax_rate'];
                $orderDetail->total_price_no_tax = $priceData['total_price_no_tax'];
                $orderDetail->total_price_tax = $priceData['total_price_tax'];
                $orderDetail->tax_amount = $priceData['tax_amount'];
                $orderDetail->expected_completion_date = !empty($detail['expected_completion_date']) ? $detail['expected_completion_date'] : null;
                $orderDetail->expected_delivery_date = !empty($detail['expected_delivery_date']) ? $detail['expected_delivery_date'] : null;
                $orderDetail->color = $detail['color'] ?? '';
                $orderDetail->brand = $detail['brand'] ?? '';
                $orderDetail->description = $detail['description'] ?? '';
                $orderDetail->inner_packing = $detail['inner_packing'] ?? '';
                $orderDetail->outer_packing = $detail['outer_packing'] ?? '';
                $orderDetail->warning = $detail['warning'] ?? '';
                $orderDetail->borrow = $detail['borrow'] ?? '';
                $orderDetail->clear_table_number = $detail['clear_table_number'] ?? '';
                $orderDetail->customer_material_code = $detail['customer_material_code'] ?? '';
                $orderDetail->customer_material_specification = $detail['customer_material_specification'] ?? '';
                $orderDetail->create_account = $data['create_account'];
                $orderDetail->update_account = $data['update_account'];
                $orderDetail->status = '1'; // 默认一般状态
                $orderDetail->remark = $detail['remark'] ?? '';
                $orderDetail->remark_2 = $detail['remark_2'] ?? '';
                $orderDetail->save();
                
                $orderItems[] = $orderDetail;
            }

            DB::commit();
            
            return [
                'order_code' => $orderCode,
                'details' => $orderItems
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 更新订单
     * 
     * @param string $orderCode 订单编码
     * @param array $data 订单数据
     * @return array
     * @throws \Exception
     */
    public static function updateOrder(string $orderCode, array $data): array
    {
        $order = self::where('order_code', $orderCode)->first();
        
        if (!$order) {
            throw new \Exception('订单不存在');
        }
        
        // 检查订单状态，只有未审核的订单可以修改
        if ($order->status !== 'N') {
            throw new \Exception('只有未审核的订单可以修改');
        }

        DB::beginTransaction();
        
        try {
            // 更新订单主记录
            $order->customer_code = $data['customer_code'];
            $order->sales_account = $data['sales_account'];
            $order->department_id = $data['department_id'];
            $order->order_date = $data['order_date'];
            $order->sales_type_code = $data['sales_type_code'];
            $order->currency_code = $data['currency_code'];
            $order->exchange_rate = $data['exchange_rate'];
            $order->exchange_rate_base_code = $data['exchange_rate_base_code'];
            $order->tax_type_code = $data['tax_type_code'];
            $order->tax_rate = $data['tax_rate'] ?? 0;
            $order->trade_term_code = $data['trade_term_code'];
            $order->receipt_payment_term_code = $data['receipt_payment_term_code'];
            $order->invoice_type_code = $data['invoice_type_code'];
            $order->pricing_method_code = $data['pricing_method_code'];
            $order->customer_order_number = $data['customer_order_number'] ?? null;
            $order->update_account = $data['update_account'];
            $order->remark = $data['remark'] ?? null;
            $order->save();
            
            // 删除原有的订单明细
            OrderDetail::where('order_code', $orderCode)->delete();
            
            // 创建新的订单明细
            $detailsData = $data['details'];
            $orderItems = [];
            $taxCategory = $data['tax_category'] ?? 'B'; // 默认含税
            $defaultTaxRate = (float)($data['tax_rate'] ?? 0);

            foreach ($detailsData as $index => $detail) {
                $quantity = (float)$detail['quantity'];
                $productionQuantity = isset($detail['production_quantity']) ? (float)$detail['production_quantity'] : $quantity;
                
                // 计算税额和价格
                $priceData = self::calculateTaxAndPrice($detail, $taxCategory, $defaultTaxRate);

                $orderDetail = new OrderDetail();
                $orderDetail->company_code = $order->company_code;
                $orderDetail->order_code = $orderCode;
                $orderDetail->order_item = $index + 1;
                $orderDetail->material_code = $detail['material_code'];
                $orderDetail->quantity = $quantity;
                $orderDetail->production_quantity = $productionQuantity;
                $orderDetail->unit_price = (float)$detail['unit_price'];
                $orderDetail->standard_unit_price = 0; // 默认标准单价为0
                $orderDetail->tax_type_code = $detail['tax_type_code'] ?? $order->tax_type_code;
                $orderDetail->tax_rate = $priceData['tax_rate'];
                $orderDetail->total_price_no_tax = $priceData['total_price_no_tax'];
                $orderDetail->total_price_tax = $priceData['total_price_tax'];
                $orderDetail->tax_amount = $priceData['tax_amount'];
                $orderDetail->expected_completion_date = !empty($detail['expected_completion_date']) ? $detail['expected_completion_date'] : null;
                $orderDetail->expected_delivery_date = !empty($detail['expected_delivery_date']) ? $detail['expected_delivery_date'] : null;
                $orderDetail->color = $detail['color'] ?? '';
                $orderDetail->brand = $detail['brand'] ?? '';
                $orderDetail->description = $detail['description'] ?? '';
                $orderDetail->inner_packing = $detail['inner_packing'] ?? '';
                $orderDetail->outer_packing = $detail['outer_packing'] ?? '';
                $orderDetail->warning = $detail['warning'] ?? '';
                $orderDetail->borrow = $detail['borrow'] ?? '';
                $orderDetail->clear_table_number = $detail['clear_table_number'] ?? '';
                $orderDetail->customer_material_code = $detail['customer_material_code'] ?? '';
                $orderDetail->customer_material_specification = $detail['customer_material_specification'] ?? '';
                $orderDetail->create_account = $data['update_account'];
                $orderDetail->update_account = $data['update_account'];
                $orderDetail->status = $detail['status'] ?? '1'; 
                $orderDetail->remark = $detail['remark'] ?? '';
                $orderDetail->remark_2 = $detail['remark_2'] ?? '';
                $orderDetail->save();
                
                $orderItems[] = $orderDetail;
            }

            DB::commit();
            
            return [
                'order_code' => $orderCode,
                'details' => $orderItems
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 删除订单
     * 
     * @param string $orderCode 订单编码
     * @throws \Exception
     */
    public static function deleteOrder(string $orderCode): void
    {
        $order = self::where('order_code', $orderCode)->first();
        
        if (!$order) {
            throw new \Exception('订单不存在');
        }
        
        // 检查订单状态，只有未审核的订单可以删除
        if ($order->status !== 'N') {
            throw new \Exception('只有未审核的订单可以删除');
        }
        
        DB::beginTransaction();
        
        try {
            // 删除订单明细
            OrderDetail::where('order_code', $orderCode)->delete();
            
            // 删除订单主记录
            $order->delete();
            
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 审核订单
     * 
     * @param string $orderCode 订单编码
     * @param string $updateAccount 更新账号
     * @throws \Exception
     */
    public static function approveOrder(string $orderCode, string $updateAccount): void
    {
        $order = self::where('order_code', $orderCode)->first();
        
        if (!$order) {
            throw new \Exception('订单不存在');
        }
        
        if ($order->status !== 'N') {
            throw new \Exception('只有未审核的订单可以审核');
        }
        
        $order->status = 'Y';
        $order->update_account = $updateAccount;
        $order->save();
    }

    /**
     * 作废订单
     * 
     * @param string $orderCode 订单编码
     * @param string $updateAccount 更新账号
     * @throws \Exception
     */
    public static function cancelOrder(string $orderCode, string $updateAccount): void
    {
        $order = self::where('order_code', $orderCode)->first();
        
        if (!$order) {
            throw new \Exception('订单不存在');
        }
        
        // 检查订单状态，已结案不能作废
        if ($order->status === 'C') {
            throw new \Exception('已结案的订单不能作废');
        }
        
        $order->status = 'X';
        $order->update_account = $updateAccount;
        $order->save();
    }

    /**
     * 结案订单
     * 
     * @param string $orderCode 订单编码
     * @param string $updateAccount 更新账号
     * @throws \Exception
     */
    public static function closeOrder(string $orderCode, string $updateAccount): void
    {
        $order = self::where('order_code', $orderCode)->first();
        
        if (!$order) {
            throw new \Exception('订单不存在');
        }
        
        if ($order->status !== 'Y') {
            throw new \Exception('只有已审核的订单可以结案');
        }
        
        $order->status = 'C';
        $order->update_account = $updateAccount;
        $order->save();
    }
}
