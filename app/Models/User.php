<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;
use <PERSON><PERSON>\Jetstream\HasProfilePhoto;
// use Laravel\Jetstream\HasTeams;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class User extends Authenticatable
{
    use HasApiTokens;

    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory;
    use HasProfilePhoto;
    // use HasTeams;
    use Notifiable;
    use TwoFactorAuthenticatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'account',
        'locale',
        'default_company_code',
        'name',
        'email',
        'password',
        'employee_id',
        'department_id',
        'position',
        'phone',
        'address',
        'hire_date',
        'status',
        'emergency_contact',
        'emergency_phone',
        'notes',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'hire_date' => 'date',
    ];

    /**
     * 用户拥有的所有角色
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class);
    }

    /**
     * 判断用户是否有指定角色
     *
     * @param string|array $roles 角色名称或角色名称数组
     * @return bool
     */
    public function hasRole($roles)
    {
        if (is_string($roles)) {
            return $this->roles->contains('name', $roles);
        }

        return (bool) $this->roles->pluck('name')->intersect($roles)->count();
    }

    /**
     * 判断用户是否有指定权限
     *
     * @param string|array $permissions 权限名称或权限名称数组
     * @return bool
     */
    public function hasPermission($permissions)
    {
        $allPermissions = $this->getAllPermissions();

        if (is_string($permissions)) {
            return $allPermissions->contains('name', $permissions);
        }

        return (bool) $allPermissions->pluck('name')->intersect($permissions)->count();
    }

    /**
     * 获取用户所有权限
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAllPermissions()
    {
        return $this->roles->flatMap(function ($role) {
            return $role->permissions;
        })->unique('id');
    }

    /**
     * 获取用户所属部门
     */
    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * 获取用户作为主管管理的部门
     */
    public function managedDepartments()
    {
        return $this->belongsToMany(Department::class, 'department_manager', 'user_id', 'department_id')
                    ->withTimestamps();
    }

    /**
     * 获取用户的默认公司
     */
    public function defaultCompany()
    {
        return $this->belongsTo(Company::class, 'default_company_code', 'company_code');
    }

    /**
     * 获取用户关联的公司
     */
    public function companies()
    {
        return $this->belongsToMany( Company::class, 'user_company', 'user_id',  'company_code')
            ->withTimestamps();
    }

    /**
     * 获取用户语言
     */
    public function locale()
    {
        return $this->belongsTo(Locale::class, 'locale', 'locale');
    }

    /**
     * 获取用户列表
     * 
     * @param string $search 搜索关键词
     * @param string $status 状态筛选
     * @param int $departmentId 部门筛选
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public static function getUserList(string $search = '', string $status = '', int $departmentId = null)
    {
        $query = self::query()
            ->leftJoin('departments', 'users.department_id', '=', 'departments.id')
            ->select('users.*', 'departments.name as department')
            ->with('companies');

        if (!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->where('users.name', 'like', "%{$search}%")
                    ->orWhere('users.account', 'like', "%{$search}%")
                    ->orWhere('users.employee_id', 'like', "%{$search}%")
                    ->orWhere('users.phone', 'like', "%{$search}%");
            });
        }

        if (!empty($status)) {
            $query->where('users.status', $status);
        }

        if (!empty($departmentId)) {
            $query->where('users.department_id', $departmentId);
        }

        return $query->orderBy('employee_id')->paginate(20)->withQueryString();
    }

    /**
     * 获取创建用户表单数据
     * 
     * @param string $locale 用户语言
     * @return array
     */
    public static function getCreateFormData(string $locale)
    {
        $roles = Role::all();
        $departments = self::getDepartmentsWithLevel();
        $companies = Company::where('is_active', 'Y')
                    ->join('company_translations', 'companies.company_code', '=', 'company_translations.company_code')
                    ->where('company_translations.locale', $locale)
                    ->orderBy('company_code')
                    ->get(['companies.company_code', 'company_translations.company_name']);

        return [
            'roles' => $roles,
            'departments' => $departments,
            'companies' => $companies,
        ];
    }

    /**
     * 创建用户
     * 
     * @param array $data 用户数据
     * @return User
     * @throws \Exception
     */
    public static function createUser(array $data)
    {
        // 确保默认公司包含在授权公司列表中
        if (!in_array($data['default_company_code'], $data['company_codes'])) {
            $data['company_codes'][] = $data['default_company_code'];
        }

        DB::beginTransaction();
        
        try {
            $user = self::create([
                'account' => $data['account'],
                'locale' => $data['locale'],
                'default_company_code' => $data['default_company_code'],
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => Hash::make($data['password']),
                'employee_id' => $data['employee_id'],
                'department_id' => $data['department_id'],
                'position' => $data['position'],
                'phone' => $data['phone'],
                'address' => $data['address'],
                'hire_date' => $data['hire_date'],
                'status' => $data['status'],
                'emergency_contact' => $data['emergency_contact'],
                'emergency_phone' => $data['emergency_phone'],
                'notes' => $data['notes'],
            ]);

            // 关联授权公司
            foreach ($data['company_codes'] as $companyCode) {
                $user->companies()->attach($companyCode);
            }

            // 关联角色
            if (!empty($data['roles'])) {
                $user->roles()->attach($data['roles']);
            }

            DB::commit();
            
            return $user;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 获取用户详情及相关数据
     * 
     * @param int $userId 用户ID
     * @param string $locale 用户语言
     * @return array
     */
    public static function getUserWithDetails(int $userId, string $locale)
    {
        $user = self::with(['department', 'companies' => function($query) use ($locale) {
            $query->join('company_translations', 'companies.company_code', '=', 'company_translations.company_code')
                  ->where('company_translations.locale', $locale)
                  ->select('companies.company_code', 'company_translations.company_name');
        }])->findOrFail($userId);
        
        $userRoles = $user->roles;

        // 获取所有可用公司列表
        $companies = Company::where('is_active', 'Y')
                    ->join('company_translations', 'companies.company_code', '=', 'company_translations.company_code')
                    ->where('company_translations.locale', $locale)
                    ->orderBy('company_code')
                    ->get(['companies.company_code', 'company_translations.company_name']);

        return [
            'user' => $user,
            'userRoles' => $userRoles,
            'companies' => $companies,
        ];
    }

    /**
     * 获取编辑用户表单数据
     * 
     * @param int $userId 用户ID
     * @param string $locale 用户语言
     * @return array
     */
    public static function getEditFormData(int $userId, string $locale)
    {
        $user = self::findOrFail($userId);
        $roles = Role::all();
        $userRoles = $user->roles->pluck('id')->toArray();
        $departments = self::getDepartmentsWithLevel();
        $companies = Company::where('is_active', 'Y')
                    ->join('company_translations', 'companies.company_code', '=', 'company_translations.company_code')
                    ->where('company_translations.locale', $locale)
                    ->orderBy('company_code')
                    ->get(['companies.company_code', 'company_translations.company_name']);
        $userCompanyCodes = $user->companies->pluck('company_code')->toArray();

        return [
            'user' => $user,
            'roles' => $roles,
            'userRoles' => $userRoles,
            'departments' => $departments,
            'companies' => $companies,
            'userCompanyCodes' => $userCompanyCodes,
        ];
    }

    /**
     * 更新用户
     * 
     * @param int $userId 用户ID
     * @param array $data 用户数据
     * @return User
     * @throws \Exception
     */
    public static function updateUser(int $userId, array $data)
    {
        $user = self::findOrFail($userId);
        
        // 确保默认公司包含在授权公司列表中
        if (!in_array($data['default_company_code'], $data['company_codes'])) {
            $data['company_codes'][] = $data['default_company_code'];
        }

        DB::beginTransaction();
        
        try {
            $userData = [
                'name' => $data['name'],
                'email' => $data['email'],
                'employee_id' => $data['employee_id'],
                'default_company_code' => $data['default_company_code'],
                'department_id' => $data['department_id'],
                'position' => $data['position'],
                'phone' => $data['phone'],
                'address' => $data['address'],
                'hire_date' => $data['hire_date'],
                'status' => $data['status'],
                'emergency_contact' => $data['emergency_contact'],
                'emergency_phone' => $data['emergency_phone'],
                'notes' => $data['notes'],
            ];

            // 只有在提供密码时才更新密码
            if (!empty($data['password'])) {
                $userData['password'] = Hash::make($data['password']);
            }

            $user->update($userData);

            // 更新授权公司
            $user->companies()->sync($data['company_codes']);

            // 更新角色
            if (isset($data['roles'])) {
                $user->roles()->sync($data['roles']);
            } else {
                $user->roles()->detach();
            }

            DB::commit();
            
            return $user;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 删除用户
     * 
     * @param int $userId 用户ID
     * @param int $currentUserId 当前登录用户ID
     * @throws \Exception
     */
    public static function deleteUser(int $userId, int $currentUserId)
    {
        // 确保不会删除当前登录用户
        if ($currentUserId === $userId) {
            throw new \Exception('无法删除当前登录用户');
        }

        $user = self::findOrFail($userId);

        DB::beginTransaction();
        
        try {
            // 删除用户前先删除与角色的关联
            $user->roles()->detach();
            $user->companies()->detach();
            $user->delete();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 获取用户的公司列表
     * 
     * @param int $userId 用户ID
     * @param string $locale 用户语言
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getUserCompanies(int $userId, string $locale)
    {
        $user = self::findOrFail($userId);
        
        return $user->companies()
            ->join('company_translations', 'companies.company_code', '=', 'company_translations.company_code')
            ->where('company_translations.locale', $locale)
            ->orderBy('companies.company_code')
            ->get(['companies.company_code', 'company_translations.company_name']);
    }

    /**
     * 获取简单用户列表
     * 
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getSimpleUserList()
    {
        return self::select('id', 'name', 'email', 'department', 'position')
            ->orderBy('name')
            ->get();
    }

    /**
     * 获取带层级的部门列表
     *
     * @return array 返回带层级的部门列表
     */
    public static function getDepartmentsWithLevel()
    {
        // 获取所有部门
        $allDepartments = Department::orderBy('order', 'asc')->get();

        // 构建部门树
        $departmentTree = [];
        $departmentMap = [];

        // 创建映射表
        foreach ($allDepartments as $department) {
            $departmentMap[$department->id] = [
                'id' => $department->id,
                'name' => $department->name,
                'parent_id' => $department->parent_id,
                'level' => 0,
                'children' => []
            ];
        }

        // 构建树结构
        foreach ($departmentMap as &$department) {
            if ($department['parent_id'] && isset($departmentMap[$department['parent_id']])) {
                $departmentMap[$department['parent_id']]['children'][] = &$department;
            } else {
                $departmentTree[] = &$department;
            }
        }

        // 计算层级并展平树结构
        $flatDepartments = [];
        self::flattenDepartmentTree($departmentTree, $flatDepartments);

        return $flatDepartments;
    }

    /**
     * 递归展平部门树并计算层级
     *
     * @param array $tree 部门树
     * @param array &$result 结果数组
     * @param int $level 当前层级
     */
    private static function flattenDepartmentTree($tree, &$result, $level = 0)
    {
        foreach ($tree as $department) {
            $department['level'] = $level;
            $children = $department['children'];
            unset($department['children']);

            $result[] = $department;

            if (!empty($children)) {
                self::flattenDepartmentTree($children, $result, $level + 1);
            }
        }
    }
}
