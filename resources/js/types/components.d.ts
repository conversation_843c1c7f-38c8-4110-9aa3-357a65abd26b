/**
 * Vue组件类型声明
 */

import { ComponentCustomProperties } from 'vue';
import { PageProps } from './index';

// 组件类型声明
declare module '@/Components/ApplicationMark.vue';
declare module '@/Components/Banner.vue';
declare module '@/Components/Dropdown.vue';
declare module '@/Components/DropdownLink.vue';
declare module '@/Components/NavLink.vue';
declare module '@/Components/ResponsiveNavLink.vue';

// 表单组件类型声明
declare module '@/Components/TextInput.vue' {
  import { DefineComponent } from 'vue';
  
  interface TextInputProps {
    modelValue?: string | number;
    type?: string;
    step?: string;
    min?: string | number;
    max?: string | number;
    autocomplete?: string;
    required?: boolean;
    autofocus?: boolean;
    disabled?: boolean;
    readonly?: boolean;
    class?: string;
  }
  
  const TextInput: DefineComponent<TextInputProps>;
  export default TextInput;
}

declare module '@/Components/InputLabel.vue' {
  import { DefineComponent } from 'vue';
  
  interface InputLabelProps {
    value: string;
    required?: boolean;
    class?: string;
  }
  
  const InputLabel: DefineComponent<InputLabelProps>;
  export default InputLabel;
}

declare module '@/Components/InputError.vue' {
  import { DefineComponent } from 'vue';
  
  interface InputErrorProps {
    message?: string;
    class?: string;
  }
  
  const InputError: DefineComponent<InputErrorProps>;
  export default InputError;
}

declare module '@/Components/DialogModal.vue' {
  import { DefineComponent } from 'vue';
  
  interface DialogModalProps {
    show: boolean;
    maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
    closeable?: boolean;
  }
  
  const DialogModal: DefineComponent<DialogModalProps>;
  export default DialogModal;
}

declare module '@/Components/PrimaryButton.vue' {
  import { DefineComponent } from 'vue';
  
  interface PrimaryButtonProps {
    type?: 'submit' | 'button' | 'reset';
    disabled?: boolean;
    class?: string;
  }
  
  const PrimaryButton: DefineComponent<PrimaryButtonProps>;
  export default PrimaryButton;
}

declare module '@/Components/SecondaryButton.vue' {
  import { DefineComponent } from 'vue';
  
  interface SecondaryButtonProps {
    type?: 'submit' | 'button' | 'reset';
    disabled?: boolean;
    class?: string;
  }
  
  const SecondaryButton: DefineComponent<SecondaryButtonProps>;
  export default SecondaryButton;
}

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $page: PageProps;
    $can: (permission: string | string[]) => boolean;
    $hasRole: (role: string) => boolean;
  }
} 