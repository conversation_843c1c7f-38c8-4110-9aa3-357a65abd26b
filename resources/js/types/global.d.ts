/**
 * 全局类型声明
 */

// 增加对 route 函数的声明
declare function route(name: string, params?: Record<string, any>): string;

// 添加页面 Props 类型声明
declare namespace Inertia {
  interface PageProps {
    app?: {
      name: string;
      logo: string;
    };
    auth?: {
      user: {
        id: number;
        name: string;
        email: string;
        profile_photo_url?: string;
        [key: string]: any;
      }
    };
    jetstream?: {
      managesProfilePhotos: boolean;
      hasApiFeatures: boolean;
    };
    permissions?: Record<string, Record<string, boolean>>;
    user_roles?: Array<{
      id: number;
      name: string;
    }>;
    flash?: {
      message?: string;
      error?: string;
      success?: string;
    };
    errors?: Record<string, string>;
    [key: string]: any;
  }
}

// 声明String的方法，避免String原型方法检查错误
interface String {
  includes(searchString: string, position?: number): boolean;
  startsWith(searchString: string, position?: number): boolean;
  endsWith(searchString: string, position?: number): boolean;
} 