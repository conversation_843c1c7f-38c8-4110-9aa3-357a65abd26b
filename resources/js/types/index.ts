export interface User {
    id: number;
    name: string;
    account: string;
    email?: string;
    locale?: string;
    default_company_code?: string;
    companies?: any[];
    department?: any;
    position?: string;
    [key: string]: any;
}

export interface PageProps {
    auth: {
        user: User | null;
    };
    jetstream: {
        canManageTwoFactorAuthentication: boolean;
        canUpdatePassword: boolean;
        canUpdateProfileInformation: boolean;
        hasApiFeatures: boolean;
        hasTeamFeatures: boolean;
        hasTermsAndPrivacyPolicyFeature: boolean;
        managesProfilePhotos: boolean;
    };
    app: {
        name: string;
        logo: string;
    };
    [key: string]: any;
} 