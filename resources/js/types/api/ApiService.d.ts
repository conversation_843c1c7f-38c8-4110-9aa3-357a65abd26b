import { AxiosResponse, AxiosRequestConfig } from 'axios';
import { User } from '../index';

export interface AuthUser {
  id: number;
  name: string;
  account: string;
  email?: string;
  locale?: string;
  [key: string]: any;
}

export interface LoginResponse {
  token: string;
  user: AuthUser;
}

export interface ApiService {
  get<T = any>(url: string, params?: Record<string, any>): Promise<AxiosResponse<T>>;
  post<T = any>(url: string, data?: Record<string, any>): Promise<AxiosResponse<T>>;
  put<T = any>(url: string, data?: Record<string, any>): Promise<AxiosResponse<T>>;
  delete<T = any>(url: string): Promise<AxiosResponse<T>>;
  request<T = any>(method: string, url: string, data?: Record<string, any> | null, config?: AxiosRequestConfig): Promise<AxiosResponse<T>>;
  setAuthToken(token: string): void;
  clearAuthToken(): void;
  validateToken(): Promise<boolean>;
  login(account: string, password: string, deviceName?: string | null): Promise<LoginResponse>;
  logout(): Promise<void>;
  getCurrentUser(): Promise<AxiosResponse<{user: User}>>;
  getTokenFromCurrentSession(): Promise<string | null>;
} 