/**
 * 全局类型声明
 */

// API响应格式
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 分页响应格式
export interface PaginatedResponse<T = any> {
  current_page: number;
  data: T[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: {
    url: string | null;
    label: string;
    active: boolean;
  }[];
  next_page_url: string | null;
  path: string;
  per_page: number;
  prev_page_url: string | null;
  to: number;
  total: number;
}

// 权限映射
export type PermissionsMap = Record<string, Record<string, boolean>>;

// 用户类型
export interface User {
  id: number;
  name: string;
  email: string;
  email_verified_at: string | null;
  account: string;
  avatar: string | null;
  profile_photo_url?: string;
  created_at: string;
  updated_at: string;
  department_id: number | null;
  department?: Department;
  roles?: Role[];
}

// 部门类型
export interface Department {
  id: number;
  name: string;
  parent_id: number | null;
  level: number;
  sort: number;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
  children?: Department[];
  parent?: Department;
}

// 角色类型
export interface Role {
  id: number;
  name: string;
  guard_name: string;
  created_at: string;
  updated_at: string;
  permissions?: Permission[];
}

// 权限类型
export interface Permission {
  id: number;
  name: string;
  guard_name: string;
  created_at: string;
  updated_at: string;
}

// 物料/产品类型
export interface Material {
  id: number;
  code: string;
  name: string;
  category_id: number;
  specification?: string;
  unit_id: number;
  inventory_min: number;
  inventory_max: number;
  purchase_price: number;
  purchase_tax_rate: number;
  sale_price: number;
  sale_tax_rate: number;
  status: 'active' | 'inactive';
  description?: string;
  created_at: string;
  updated_at: string;
  category?: MaterialCategory;
  unit?: Unit;
}

// 物料分类
export interface MaterialCategory {
  id: number;
  name: string;
  parent_id: number | null;
  level: number;
  sort: number;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
  children?: MaterialCategory[];
  parent?: MaterialCategory;
}

// 计量单位
export interface Unit {
  id: number;
  name: string;
  code: string;
  created_at: string;
  updated_at: string;
}

// 客户
export interface Customer {
  id: number;
  code: string;
  name: string;
  contact: string;
  phone: string;
  email: string;
  address: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

// 订单
export interface Order {
  id: number;
  order_code: string;
  customer_id: number;
  order_date: string;
  delivery_date: string | null;
  status: 'N' | 'Y' | 'C' | 'X';
  currency_code: string;
  exchange_rate: number;
  sales_account: string;
  remarks: string | null;
  created_by: number;
  created_at: string;
  updated_at: string;
  customer?: Customer;
  sales_person?: User;
  details: OrderDetail[];
}

// 订单明细
export interface OrderDetail {
  id: number;
  order_id: number;
  order_item: number;
  material_code: string;
  quantity: number;
  unit_price: number;
  tax_rate: number;
  total_price: number;
  total_price_tax: number;
  delivery_date: string | null;
  remarks: string | null;
  created_at: string;
  updated_at: string;
  material?: Material;
}

// BOM明细
export interface BomDetail {
  id: number;
  parent_id: number;
  product_code: string;
  material_code: string;
  quantity: number;
  created_at: string;
  updated_at: string;
  product?: Material;
  material?: Material;
  children?: BomDetail[];
}

// Inertia 页面属性类型定义
export type PageProps<T extends Record<string, any> = Record<string, any>> = T & {
  auth: {
    user: User;
  };
  permissions: PermissionsMap;
  user_roles: Role[];
  app: {
    name: string;
    logo: string;
  };
  jetstream: {
    managesProfilePhotos: boolean;
    hasApiFeatures: boolean;
  };
  flash?: {
    banner?: string;
    bannerStyle?: 'success' | 'danger';
    message?: string;
  };
  errorBags: Record<string, string[]>;
  errors: Record<string, string>;
};

// 声明全局类型
declare global {
  interface Window {
    Laravel: {
      permissions: PermissionsMap;
      roles: string[];
      user: User;
    };
  }

  // 路由函数声明
  function route(name: string, params?: Record<string, any>): string;
}

// 扩展 @inertiajs/vue3 模块
declare module '@inertiajs/vue3' {
  import { ComputedRef } from 'vue';
  
  // 扩展 Inertia Page 接口
  interface Page {
    props: PageProps;
    url: string;
    component: string;
    version: string;
    scrollRegions: Array<string>;
    rememberedState: Record<string, any>;
  }

  // 扩展 usePage 函数返回类型
  export function usePage<T = PageProps>(): ComputedRef<Page & { props: T }>;
}

// 声明 @inertiajs/inertia 模块
declare module '@inertiajs/inertia' {
  export const Inertia: {
    visit: (url: string, options?: any) => void;
    replace: (url: string, options?: any) => void;
    reload: (options?: any) => void;
    post: (url: string, data?: any, options?: any) => void;
    put: (url: string, data?: any, options?: any) => void;
    patch: (url: string, data?: any, options?: any) => void;
    delete: (url: string, options?: any) => void;
    // 其他 Inertia 方法...
  };
} 