const Ziggy = {"url":"http:\/\/localhost:8000","port":8000,"defaults":{},"routes":{"login":{"uri":"login","methods":["GET","HEAD"]},"login.store":{"uri":"login","methods":["POST"]},"logout":{"uri":"logout","methods":["POST"]},"user-profile-information.update":{"uri":"user\/profile-information","methods":["PUT"]},"user-password.update":{"uri":"user\/password","methods":["PUT"]},"password.confirm":{"uri":"user\/confirm-password","methods":["GET","HEAD"]},"password.confirmation":{"uri":"user\/confirmed-password-status","methods":["GET","HEAD"]},"password.confirm.store":{"uri":"user\/confirm-password","methods":["POST"]},"two-factor.login":{"uri":"two-factor-challenge","methods":["GET","HEAD"]},"two-factor.login.store":{"uri":"two-factor-challenge","methods":["POST"]},"two-factor.enable":{"uri":"user\/two-factor-authentication","methods":["POST"]},"two-factor.confirm":{"uri":"user\/confirmed-two-factor-authentication","methods":["POST"]},"two-factor.disable":{"uri":"user\/two-factor-authentication","methods":["DELETE"]},"two-factor.qr-code":{"uri":"user\/two-factor-qr-code","methods":["GET","HEAD"]},"two-factor.secret-key":{"uri":"user\/two-factor-secret-key","methods":["GET","HEAD"]},"two-factor.recovery-codes":{"uri":"user\/two-factor-recovery-codes","methods":["GET","HEAD"]},"profile.show":{"uri":"user\/profile","methods":["GET","HEAD"]},"other-browser-sessions.destroy":{"uri":"user\/other-browser-sessions","methods":["DELETE"]},"current-user-photo.destroy":{"uri":"user\/profile-photo","methods":["DELETE"]},"current-user.destroy":{"uri":"user","methods":["DELETE"]},"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"api.login":{"uri":"api\/login","methods":["POST"]},"user.":{"uri":"api\/user","methods":["GET","HEAD"]},"user.generated::v108QMeHyg9KRh0m":{"uri":"api\/user\/list","methods":["GET","HEAD"]},"user.generated::khJeoUjkNci90wTD":{"uri":"api\/user\/companies","methods":["GET","HEAD"]},"materials.":{"uri":"api\/materials","methods":["GET","HEAD"]},"materials.generated::Ns2cby8002A2CiVD":{"uri":"api\/materials\/product-categories","methods":["GET","HEAD"]},"BOM.":{"uri":"api\/BOM","methods":["GET","HEAD"]},"BOM.generated::2kcjVtJu0wp53pOF":{"uri":"api\/BOM\/product-categories","methods":["GET","HEAD"]},"BOM.generated::LTvEgycbfwovnMWz":{"uri":"api\/BOM\/customers","methods":["GET","HEAD"]},"BOM.generated::yfRl3omrx4TNZnuQ":{"uri":"api\/BOM\/tree\/{code}","methods":["GET","HEAD","POST","PUT","PATCH","DELETE","OPTIONS"],"parameters":["code"]},"sales.customers.":{"uri":"api\/sales\/customers\/form-data","methods":["GET","HEAD"]},"sales.customers.generated::0vIRTcLcKPFnvhkf":{"uri":"api\/sales\/customers\/getList","methods":["GET","HEAD"]},"sales.customers.generated::DMwUhOkCglbNtMFV":{"uri":"api\/sales\/customers\/show\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"sales.customers.generated::v7eYy9zqUQEsRNLN":{"uri":"api\/sales\/customers\/create","methods":["POST"]},"sales.customers.generated::SNVxVAdPUJGaUd5A":{"uri":"api\/sales\/customers\/update\/{id}","methods":["PUT"],"parameters":["id"]},"sales.customers.generated::zArbeBpdaOkaosDS":{"uri":"api\/sales\/customers\/{id}","methods":["DELETE"],"parameters":["id"]},"sales.orders.":{"uri":"api\/sales\/orders\/form-data","methods":["GET","HEAD"]},"sales.orders.generated::fIDJXcMuWCF0m6SU":{"uri":"api\/sales\/orders\/getList","methods":["GET","HEAD"]},"sales.orders.generated::MjRhEQGA3vY1WFfv":{"uri":"api\/sales\/orders\/materials","methods":["GET","HEAD"]},"sales.orders.generated::3WwrAZhOaj3y9ovy":{"uri":"api\/sales\/orders\/{orderCode}","methods":["GET","HEAD"],"parameters":["orderCode"]},"sales.orders.generated::FDVHYueGrBnPfnO8":{"uri":"api\/sales\/orders\/create","methods":["POST"]},"sales.orders.generated::t4Pr71LWI3ZNMADd":{"uri":"api\/sales\/orders\/update\/{orderCode}","methods":["PUT"],"parameters":["orderCode"]},"sales.orders.generated::dvd7gE8CMps0uCj0":{"uri":"api\/sales\/orders\/{orderCode}","methods":["DELETE"],"parameters":["orderCode"]},"sales.orders.generated::1RgnOOFXW5R1M2Rm":{"uri":"api\/sales\/orders\/{orderCode}\/approve","methods":["PUT"],"parameters":["orderCode"]},"sales.orders.generated::AcjsK0cb0bOSqc7q":{"uri":"api\/sales\/orders\/{orderCode}\/cancel","methods":["PUT"],"parameters":["orderCode"]},"sales.orders.generated::JUZtjWxploJKAk52":{"uri":"api\/sales\/orders\/{orderCode}\/close","methods":["PUT"],"parameters":["orderCode"]},"purchases.suppliers.":{"uri":"api\/purchases\/suppliers\/form-data","methods":["GET","HEAD"]},"purchases.suppliers.generated::KgmeYQlekeSbOJLX":{"uri":"api\/purchases\/suppliers\/getList","methods":["GET","HEAD"]},"purchases.suppliers.generated::BBAUmtG6xlDaNg1D":{"uri":"api\/purchases\/suppliers\/show\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"purchases.suppliers.generated::qGF9oR5WeQGn15ze":{"uri":"api\/purchases\/suppliers\/create","methods":["POST"]},"purchases.suppliers.generated::ubgd8dPPRBmYZQ7Z":{"uri":"api\/purchases\/suppliers\/update\/{id}","methods":["PUT"],"parameters":["id"]},"purchases.suppliers.generated::515NzoYs5f67RUAa":{"uri":"api\/purchases\/suppliers\/{id}","methods":["DELETE"],"parameters":["id"]},"logout.get":{"uri":"logout","methods":["GET","HEAD"]},"api.test":{"uri":"api-test","methods":["GET","HEAD"]},"home":{"uri":"home","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"products.bom":{"uri":"products\/bom","methods":["GET","HEAD"]},"products.bom-tree":{"uri":"products\/bom-tree\/{code?}","methods":["GET","HEAD"],"parameters":["code"]},"products.materials":{"uri":"products\/materials","methods":["GET","HEAD"]},"sales.customers":{"uri":"sales\/customers","methods":["GET","HEAD"]},"sales.price":{"uri":"sales\/price","methods":["GET","HEAD"]},"sales.orders":{"uri":"sales\/orders","methods":["GET","HEAD"]},"sales.orders.create":{"uri":"sales\/orders\/create","methods":["GET","HEAD"]},"sales.orders.edit":{"uri":"sales\/orders\/edit\/{orderCode}","methods":["GET","HEAD"],"parameters":["orderCode"]},"sales.contracts":{"uri":"sales\/contracts","methods":["GET","HEAD"]},"sales.delivery":{"uri":"sales\/delivery","methods":["GET","HEAD"]},"sales.invoices":{"uri":"sales\/invoices","methods":["GET","HEAD"]},"purchases.suppliers":{"uri":"purchases\/suppliers","methods":["GET","HEAD"]},"purchases.price":{"uri":"purchases\/price","methods":["GET","HEAD"]},"purchases.mrp":{"uri":"purchases\/mrp","methods":["GET","HEAD"]},"purchases.orders":{"uri":"purchases\/orders","methods":["GET","HEAD"]},"purchases.pricing":{"uri":"purchases\/pricing","methods":["GET","HEAD"]},"purchases.invoices":{"uri":"purchases\/invoices","methods":["GET","HEAD"]},"warehouse.purchase-in":{"uri":"warehouse\/purchase-in","methods":["GET","HEAD"]},"warehouse.other-in":{"uri":"warehouse\/other-in","methods":["GET","HEAD"]},"warehouse.material-out":{"uri":"warehouse\/material-out","methods":["GET","HEAD"]},"warehouse.shipping":{"uri":"warehouse\/shipping","methods":["GET","HEAD"]},"logistics.packing":{"uri":"logistics\/packing","methods":["GET","HEAD"]},"logistics.encasement":{"uri":"logistics\/encasement","methods":["GET","HEAD"]},"logistics.customsDeclaration":{"uri":"logistics\/customsDeclaration","methods":["GET","HEAD"]},"logistics.shipping":{"uri":"logistics\/shipping","methods":["GET","HEAD"]},"production.plans":{"uri":"production\/plans","methods":["GET","HEAD"]},"production.work-orders":{"uri":"production\/work-orders","methods":["GET","HEAD"]},"production.material-requisitions":{"uri":"production\/material-requisitions","methods":["GET","HEAD"]},"QC.inspection":{"uri":"QC\/inspection","methods":["GET","HEAD"]},"finance.cost-accounting":{"uri":"finance\/cost-accounting","methods":["GET","HEAD"]},"finance.receivables":{"uri":"finance\/receivables","methods":["GET","HEAD"]},"finance.payables":{"uri":"finance\/payables","methods":["GET","HEAD"]},"reports.annual":{"uri":"reports\/annual","methods":["GET","HEAD"]},"reports.monthly":{"uri":"reports\/monthly","methods":["GET","HEAD"]},"settings.departments":{"uri":"settings\/departments","methods":["GET","HEAD"]},"settings.departments.create":{"uri":"settings\/departments\/create","methods":["GET","HEAD"]},"settings.departments.store":{"uri":"settings\/departments","methods":["POST"]},"settings.departments.show":{"uri":"settings\/departments\/{department}","methods":["GET","HEAD"],"parameters":["department"],"bindings":{"department":"id"}},"settings.departments.edit":{"uri":"settings\/departments\/{department}\/edit","methods":["GET","HEAD"],"parameters":["department"],"bindings":{"department":"id"}},"settings.departments.update":{"uri":"settings\/departments\/{department}","methods":["PUT"],"parameters":["department"],"bindings":{"department":"id"}},"settings.departments.destroy":{"uri":"settings\/departments\/{department}","methods":["DELETE"],"parameters":["department"],"bindings":{"department":"id"}},"settings.roles":{"uri":"settings\/roles","methods":["GET","HEAD"]},"settings.roles.create":{"uri":"settings\/roles\/create","methods":["GET","HEAD"]},"settings.roles.store":{"uri":"settings\/roles","methods":["POST"]},"settings.roles.show":{"uri":"settings\/roles\/{role}","methods":["GET","HEAD"],"parameters":["role"],"bindings":{"role":"id"}},"settings.roles.edit":{"uri":"settings\/roles\/{role}\/edit","methods":["GET","HEAD"],"parameters":["role"],"bindings":{"role":"id"}},"settings.roles.update":{"uri":"settings\/roles\/{role}","methods":["PUT"],"parameters":["role"],"bindings":{"role":"id"}},"settings.roles.destroy":{"uri":"settings\/roles\/{role}","methods":["DELETE"],"parameters":["role"],"bindings":{"role":"id"}},"settings.roles.assign-users":{"uri":"settings\/roles\/{role}\/assign-users","methods":["POST"],"parameters":["role"],"bindings":{"role":"id"}},"settings.permissions":{"uri":"settings\/permissions","methods":["GET","HEAD"]},"settings.permissions.create":{"uri":"settings\/permissions\/create","methods":["GET","HEAD"]},"settings.permissions.store":{"uri":"settings\/permissions","methods":["POST"]},"settings.permissions.show":{"uri":"settings\/permissions\/{permission}","methods":["GET","HEAD"],"parameters":["permission"],"bindings":{"permission":"id"}},"settings.permissions.edit":{"uri":"settings\/permissions\/{permission}\/edit","methods":["GET","HEAD"],"parameters":["permission"],"bindings":{"permission":"id"}},"settings.permissions.update":{"uri":"settings\/permissions\/{permission}","methods":["PUT"],"parameters":["permission"],"bindings":{"permission":"id"}},"settings.permissions.destroy":{"uri":"settings\/permissions\/{permission}","methods":["DELETE"],"parameters":["permission"],"bindings":{"permission":"id"}},"settings.users":{"uri":"settings\/users","methods":["GET","HEAD"]},"settings.users.create":{"uri":"settings\/users\/create","methods":["GET","HEAD"]},"settings.users.store":{"uri":"settings\/users","methods":["POST"]},"settings.users.show":{"uri":"settings\/users\/{user}","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"settings.users.edit":{"uri":"settings\/users\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"settings.users.update":{"uri":"settings\/users\/{user}","methods":["PUT"],"parameters":["user"],"bindings":{"user":"id"}},"settings.users.destroy":{"uri":"settings\/users\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"id"}},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
