<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { Head, Link, router, usePage } from '@inertiajs/vue3';
import Banner from '@/Components/Banner.vue';
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue';

interface Props {
    title?: string;
}

const props = defineProps<Props>();

// 权限和用户相关的类型定义
interface UserRole {
    id: number;
    name: string;
}

// 注意：User接口在模板中通过page.props.auth.user直接访问

// 注意：用户认证信息在模板中直接通过page.props.auth访问

type PermissionValue = Record<string, boolean>;
type Permissions = Record<string, PermissionValue>;

// 菜单状态接口
interface ExpandedMenus {
    products: boolean;
    sales: boolean;
    purchases: boolean;
    warehouse: boolean;
    production: boolean;
    logistics: boolean;
    QC: boolean;
    finance: boolean;
    reports: boolean;
    settings: boolean;
    [key: string]: boolean;
}

// 扩展应用相关的属性类型
interface AppInfo {
    name: string;
    logo: string;
}

// 注意：Jetstream相关的属性在模板中直接通过page.props.jetstream访问

// 加载状态
const isLoading = ref(true);
const hasPermissionsLoaded = ref(false);

// 从localStorage获取已保存的菜单状态
const loadExpandedMenusFromStorage = (): ExpandedMenus => {
    try {
        const savedMenus = localStorage.getItem('expandedMenus');
        return savedMenus ? JSON.parse(savedMenus) : {
            products: false,
            sales: false,
            purchases: false,
            warehouse: false,
            production: false,
            logistics: false,
            QC: false,
            finance: false,
            reports: false,
            settings: false
        };
    } catch (e) {
        console.error('加载菜单状态错误:', e);
        return {
            products: false,
            sales: false,
            purchases: false,
            warehouse: false,
            production: false,
            logistics: false,
            QC: false,
            finance: false,
            reports: false,
            settings: false
        };
    }
};

// 菜单状态
const expandedMenus = ref<ExpandedMenus>(loadExpandedMenusFromStorage());

// 安全获取页面数据
const page = computed(() => {
    try {
        return usePage<Inertia.PageProps>();
    } catch (e) {
        console.error('获取页面数据错误:', e);
        return { props: { auth: { user: {} }, permissions: {}, user_roles: [] } } as any;
    }
});

// 注意：当前用户信息在模板中直接通过page.props.auth.user访问

// 用户角色
const userRoles = computed<UserRole[]>(() => {
    try {
        return page.value.props.user_roles || [];
    } catch (e) {
        console.error('获取用户角色数据错误:', e);
        return [];
    }
});

// 用户权限
const permissions = computed<Permissions>(() => {
    try {
        return page.value.props.permissions || {} as Permissions;
    } catch (e) {
        console.error('获取权限数据错误:', e);
        return {} as Permissions;
    }
});

// 检查是否为管理员角色
const isAdmin = computed<boolean>(() => {
    return userRoles.value.some(role => role.name === 'admin');
});

// 当前路径
const currentPath = computed<string>(() => {
    return window.location.pathname;
});

// 检查是否在权限管理页面
const isPermissionsPage = computed<boolean>(() => {
    return String(currentPath.value).includes('/permissions') ||
           String(currentPath.value).startsWith('/settings/permissions') ||
           String(currentPath.value).includes('/roles') ||
           String(currentPath.value).startsWith('/settings/roles');
});

// 监听权限数据加载
watch(() => page.value.props.permissions, (newValue) => {
    if (newValue) {
        hasPermissionsLoaded.value = true;
        console.log('权限数据已加载', newValue);

        // 将权限数据保存到sessionStorage，确保页面导航时不丢失
        try {
            sessionStorage.setItem('userPermissions', JSON.stringify(newValue));
        } catch (e) {
            console.error('保存权限数据错误:', e);
        }
    }
}, { immediate: true });

// 检查是否有特定权限
const hasPermission = (permissionName: string): boolean => {
    try {
        if (!permissionName) return false;

        // 管理员拥有所有权限
        if (isAdmin.value) {
            return true;
        }

        // 如果在权限管理页面上，确保主要导航菜单始终显示
        if (isPermissionsPage.value) {
            const permissionList = [
                'dashboard.view',
                'products.view',
                'sales.view',
                'purchases.view',
                'warehouse.view',
                'logistics.view',
                'production.view',
                'QC.view',
                'finance.view',
                'reports.view',
                'settings.view'
            ];
            if (permissionList.indexOf(permissionName) !== -1) {
                console.log('权限管理页面访问，总是允许导航项目:', permissionName);
                return true;
            }
        }

        // 分解权限名称，如 'products.view' => ['products', 'view']
        const [module, action] = permissionName.split('.');

        // 如果只传入了模块名称，如 'products'，那么检查该模块下是否有任何权限
        if (!action) {
            return !!permissions.value &&
                   !!permissions.value[module] &&
                   Object.keys(permissions.value[module]).length > 0;
        }

        // 通用的三层检查逻辑
        // 1. 检查具体权限 (例如: settings.departments.view)
        if (permissions.value?.[module]?.[action]) return true;

        // 2. 检查模块级权限，如果用户有settings.departments，那么应该有settings.departments.view等权限
        if (permissions.value?.[module]?.[permissionName.replace(`${module}.`, '')]) return true;

        // 3. 检查通配符权限 (例如: settings.* 表示有所有settings下的权限)
        if (permissions.value?.[module]?.["*"]) return true;

        return false;
    } catch (e) {
        console.error('权限检查错误:', e, '权限名称:', permissionName);
        return false;
    }
};

// 侧边栏宽度
const sidebarWidth = ref<number>(200);
const minSidebarWidth = 180;
const maxSidebarWidth = 400;
const isResizing = ref<boolean>(false);
const startX = ref<number>(0);

// 切换菜单
const toggleMenu = (menu: string): void => {
    expandedMenus.value[menu] = !expandedMenus.value[menu];
    // 保存到localStorage
    localStorage.setItem('expandedMenus', JSON.stringify(expandedMenus.value));
};

// 初始化时根据当前路由自动展开相关菜单
const autoExpandMenusByCurrentPath = (): void => {
    // 获取当前路径
    const path = window.location.pathname;

    // 根据路径自动展开对应的菜单
    if (String(path).startsWith('/products')) {
        expandedMenus.value.products = true;
    } else if (String(path).startsWith('/sales')) {
        expandedMenus.value.sales = true;
    } else if (String(path).startsWith('/purchases')) {
        expandedMenus.value.purchases = true;
    } else if (String(path).startsWith('/warehouse')) {
        expandedMenus.value.warehouse = true;
    } else if (String(path).startsWith('/production')) {
        expandedMenus.value.production = true;
    } else if (String(path).startsWith('/logistics')) {
        expandedMenus.value.logistics = true;
    } else if (String(path).startsWith('/qc') || String(path).startsWith('/QC')) {
        expandedMenus.value.QC = true;
    } else if (String(path).startsWith('/finance')) {
        expandedMenus.value.finance = true;
    } else if (String(path).startsWith('/reports')) {
        expandedMenus.value.reports = true;
    } else if (String(path).startsWith('/settings')) {
        expandedMenus.value.settings = true;
    }

    // 保存到localStorage
    localStorage.setItem('expandedMenus', JSON.stringify(expandedMenus.value));
};

// 处理页面标题
const pageTitle = computed<string>(() => {
    const app = page.value.props.app as AppInfo | undefined;
    const baseTitle = props.title || app?.name || '设备开发';
    return baseTitle;
});

// 注意：此处原有外部链接类型定义，但当前未使用

// 处理侧边栏调整大小
const startResize = (e: MouseEvent): void => {
    isResizing.value = true;
    startX.value = e.clientX;

    // 添加全局鼠标事件监听器
    document.addEventListener('mousemove', resizeSidebar);
    document.addEventListener('mouseup', stopResize);

    // 添加禁止选择文本的样式
    document.body.style.userSelect = 'none';
};

const resizeSidebar = (e: MouseEvent): void => {
    if (!isResizing.value) return;

    const delta = e.clientX - startX.value;

    // 计算新宽度
    let newWidth = sidebarWidth.value + delta;

    // 限制最小和最大宽度
    if (newWidth < minSidebarWidth) newWidth = minSidebarWidth;
    if (newWidth > maxSidebarWidth) newWidth = maxSidebarWidth;

    // 更新宽度和起始位置
    sidebarWidth.value = newWidth;
    startX.value = e.clientX;
};

const stopResize = (): void => {
    isResizing.value = false;

    // 移除全局鼠标事件监听器
    document.removeEventListener('mousemove', resizeSidebar);
    document.removeEventListener('mouseup', stopResize);

    // 移除禁止选择文本的样式
    document.body.style.userSelect = '';
};

// 登出处理
const logout = (): void => {
    router.post(route('logout'));
};

// route 函数，用于在模板中使用
declare function route(name: string, params?: Record<string, any>): string;

// 当组件挂载时自动展开菜单
onMounted(() => {
    // 自动展开当前路径对应的菜单
    autoExpandMenusByCurrentPath();

    // 延迟0.5秒后关闭加载状态
    setTimeout(() => {
        isLoading.value = false;
    }, 500);
});

// 检查当前路径是否与指定路径匹配
const isCurrentPath = (path: string): boolean => {
    // 检查自定义路径，如 'settings.departments.*'
    if (path === 'settings.departments.*') {
        // 添加更详细的调试信息
        console.log('检查部门路径匹配:', currentPath, '路径模式:', path);

        // 使用更精确的匹配方式，确保能匹配所有部门管理相关路径
        const isDepartmentPath =
            currentPath.value === '/settings/departments' ||
            currentPath.value === '/settings/departments/index' ||
            String(currentPath.value).startsWith('/settings/departments/') ||
            // 处理可能的查询参数情况
            String(currentPath.value).match(/^\/settings\/departments(\?|$)/) !== null;

        // 输出匹配结果
        console.log('部门路径匹配结果:', isDepartmentPath);
        return isDepartmentPath;
    }

    // 处理精确路由名称匹配，如 'settings.departments'、'settings.roles'、'settings.permissions'
    if (String(path).includes('.')) {
        // 这种情况下，需要转换路由为实际路径进行比较
        // 如：'settings.departments' => '/settings/departments'
        const pathSegments = path.split('.');
        const urlPath = '/' + pathSegments.join('/');

        // 检查当前路径是否与转换后的路径匹配，或者以转换后的路径开头
        // 这样可以处理像 /settings/departments/create 这样的子路径
        return currentPath.value === urlPath || String(currentPath.value).startsWith(urlPath + '/');
    }

    // 处理单一路径，如 'dashboard'
    return currentPath.value === '/' + path || String(currentPath.value).startsWith('/' + path + '/');
};

// 刷新菜单状态 - 清除localStorage中的菜单状态并重新初始化
const refreshMenuState = (): void => {
    try {
        // 清除localStorage中的菜单状态
        localStorage.removeItem('expandedMenus');

        // 重新初始化菜单状态
        expandedMenus.value = {
            products: false,
            sales: false,
            purchases: false,
            warehouse: false,
            production: false,
            logistics: false,
            QC: false,
            finance: false,
            reports: false,
            settings: false
        };

        // 根据当前路径自动展开菜单
        autoExpandMenusByCurrentPath();

        // 提示用户
        alert('菜单状态已刷新');
    } catch (e) {
        console.error('刷新菜单状态错误:', e);
    }
};
</script>

<template>
    <div>
        <Head :title="pageTitle" />

        <Banner />

        <div class="min-h-screen bg-gray-100 dark:bg-gray-900 flex flex-col">
            <!-- 顶部区域 -->
            <div class="bg-white dark:bg-gray-800 border-b border-gray-100 dark:border-gray-700 w-full px-4">
                <div class="flex justify-between items-center h-16">

                    <!-- Logo及描述文字 -->
                    <div class="flex items-center h-full">
                        <Link :href="route('dashboard')" class="flex items-center h-full">
                            <img :src="page.props.app && 'logo' in page.props.app ? page.props.app.logo : ''" alt="Logo" class="h-full w-auto mr-2">
                            <h2 class="font-semibold text-2xl text-gray-800 dark:text-white leading-tight">
                                {{ page.props.app && 'name' in page.props.app ? page.props.app.name : '数智未来' }}
                            </h2>
                        </Link>
                        <!-- 刷新菜单按钮 -->
                        <button @click="refreshMenuState" class="ml-4 px-2 py-1 bg-blue-500 text-white rounded text-xs">
                            刷新菜单
                        </button>
                    </div>

                    <!-- 用户信息和消息提醒 -->
                    <div class="flex items-center space-x-4">
                        <!-- 消息提醒图标 -->
                        <button class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                        </button>

                        <!-- 用户信息下拉菜单 -->
                        <div class="relative">
                            <Dropdown align="right" width="48">
                                <template #trigger>
                                    <button
                                        v-if="page.props.jetstream && 'managesProfilePhotos' in page.props.jetstream && page.props.jetstream.managesProfilePhotos"
                                        class="flex text-sm border-2 border-transparent rounded-full focus:outline-none focus:border-gray-300 transition"
                                    >
                                        <img
                                            class="size-8 rounded-full object-cover"
                                            :src="page.props.auth && page.props.auth.user && 'profile_photo_url' in page.props.auth.user ? page.props.auth.user.profile_photo_url : ''"
                                            :alt="page.props.auth && page.props.auth.user && 'name' in page.props.auth.user ? page.props.auth.user.name : ''"
                                        >
                                    </button>

                                    <span v-else class="inline-flex rounded-md">
                                        <button type="button" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700 active:bg-gray-50 dark:active:bg-gray-700 transition ease-in-out duration-150">
                                            {{ page.props.auth && page.props.auth.user && 'name' in page.props.auth.user ? page.props.auth.user.name : '' }}

                                            <svg class="ms-2 -me-0.5 size-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                            </svg>
                                        </button>
                                    </span>
                                </template>

                                <template #content>
                                    <!-- 账号管理 -->
                                    <div class="block px-4 py-2 text-xs text-gray-400">
                                        账号管理
                                    </div>

                                    <DropdownLink :href="route('profile.show')">
                                        个人信息
                                    </DropdownLink>

                                    <DropdownLink
                                        v-if="page.props.jetstream && 'hasApiFeatures' in page.props.jetstream && page.props.jetstream.hasApiFeatures"
                                        :href="route('api-tokens.index')"
                                    >
                                        API令牌
                                    </DropdownLink>

                                    <div class="border-t border-gray-200 dark:border-gray-600" />

                                    <!-- 退出登录 -->
                                    <form @submit.prevent="logout">
                                        <DropdownLink as="button">
                                            退出登录
                                        </DropdownLink>
                                    </form>
                                </template>
                            </Dropdown>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 内容区域（分左右两部分） -->
            <div class="flex flex-grow overflow-hidden h-[calc(100vh-4rem)]">
                <!-- 左侧目录区域（可调整宽度） -->
                <div id="sidebar" class="flex flex-col bg-gray-800 text-white relative" :style="`width: ${sidebarWidth}px; min-width: ${minSidebarWidth}px`">
                    <div class="flex-grow overflow-y-auto scrollbar-content p-4">
                        <!-- 菜单内容 -->
                        <div class="space-y-2">
                            <h3 class="text-sm font-medium text-gray-400 uppercase tracking-wider mb-3">
                                功能导航
                            </h3>

                            <Link :href="route('dashboard')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('dashboard') }" v-if="hasPermission('dashboard.view')">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                </svg>
                                <span>控制台</span>
                            </Link>

                            <!-- 产品管理（带子菜单） -->
                            <div class="space-y-1" v-if="hasPermission('products.view')">
                                <button @click="toggleMenu('products')" class="w-full flex items-center justify-between px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('products.*') }">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                        </svg>
                                        <span>产品管理</span>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform" :class="{ 'rotate-180': expandedMenus.products }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <!-- 产品管理子菜单 -->
                                <div v-show="expandedMenus.products" class="pl-7 space-y-1">
                                    <Link :href="route('products.bom')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('products.bom') }" v-if="hasPermission('products.bom')">
                                        <span>BOM查询</span>
                                    </Link>

                                    <Link :href="route('products.materials')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('products.materials') }" v-if="hasPermission('products.materials')">
                                        <span>料件查询</span>
                                    </Link>
                                </div>
                            </div>

                            <!-- 销售管理（添加销售管理子菜单） -->
                            <div class="space-y-1" v-if="hasPermission('sales.view')">
                                <button @click="toggleMenu('sales')" class="w-full flex items-center justify-between px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('sales.*') }">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <span>销售管理</span>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform" :class="{ 'rotate-180': expandedMenus.sales }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <!-- 销售管理子菜单 -->
                                <div v-show="expandedMenus.sales" class="pl-7 space-y-1">
                                    <Link :href="route('sales.customers')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('sales.customers') }" v-if="hasPermission('sales.customers')">
                                        <span>客户管理</span>
                                    </Link>

                                    <Link :href="route('sales.price')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('sales.price') }" v-if="hasPermission('sales.price')">
                                        <span>价格维护</span>
                                    </Link>

                                    <Link :href="route('sales.contracts')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('sales.contracts') }" v-if="hasPermission('sales.contracts')">
                                        <span>合同评审</span>
                                    </Link>

                                    <Link :href="route('sales.orders')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('sales.orders') }" v-if="hasPermission('sales.orders')">
                                        <span>订单管理</span>
                                    </Link>

                                    <Link :href="route('sales.tracking')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('sales.tracking') }" v-if="hasPermission('sales.tracking')">
                                        <span>订单跟踪</span>
                                    </Link>

                                    <Link :href="route('sales.delivery')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('sales.delivery') }" v-if="hasPermission('sales.delivery')">
                                        <span>出货通知</span>
                                    </Link>

                                    <Link :href="route('sales.invoices')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('sales.invoices') }" v-if="hasPermission('sales.invoices')">
                                        <span>销售开票</span>
                                    </Link>
                                </div>
                            </div>

                            <!-- 采购管理 -->
                            <div class="space-y-1" v-if="hasPermission('purchases.view')">
                                <button @click="toggleMenu('purchases')" class="w-full flex items-center justify-between px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('purchases.*') }">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                        <span>采购管理</span>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform" :class="{ 'rotate-180': expandedMenus.purchases }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <!-- 采购管理子菜单 -->
                                <div v-show="expandedMenus.purchases" class="pl-7 space-y-1">
                                    <Link :href="route('purchases.suppliers')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('purchases.suppliers') }" v-if="hasPermission('purchases.suppliers')">
                                        <span>供应商管理</span>
                                    </Link>

                                    <Link :href="route('purchases.price')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('purchases.price') }" v-if="hasPermission('purchases.price')">
                                        <span>价格维护</span>
                                    </Link>

                                    <Link :href="route('purchases.mrp')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('purchases.mrp') }" v-if="hasPermission('purchases.mrp')">
                                        <span>MRP</span>
                                    </Link>

                                    <Link :href="route('purchases.orders')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('purchases.orders') }" v-if="hasPermission('purchases.orders')">
                                        <span>采购单</span>
                                    </Link>

                                    <Link :href="route('purchases.pricing')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('purchases.pricing') }" v-if="hasPermission('purchases.pricing')">
                                        <span>核价单</span>
                                    </Link>

                                    <Link :href="route('purchases.invoices')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('purchases.invoices') }" v-if="hasPermission('purchases.invoices')">
                                        <span>采购发票</span>
                                    </Link>
                                </div>
                            </div>

                            <!-- 仓储管理 -->
                            <div class="space-y-1" v-if="hasPermission('warehouse.view')">
                                <button @click="toggleMenu('warehouse')" class="w-full flex items-center justify-between px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('warehouse.*') }">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
                                        </svg>
                                        <span>仓储管理</span>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform" :class="{ 'rotate-180': expandedMenus.warehouse }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <!-- 仓储管理子菜单 -->
                                <div v-show="expandedMenus.warehouse" class="pl-7 space-y-1">
                                    <Link :href="route('warehouse.purchase-in')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('warehouse.purchase-in') }" v-if="hasPermission('warehouse.purchase-in')">
                                        <span>采购入库</span>
                                    </Link>

                                    <Link :href="route('warehouse.other-in')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('warehouse.other-in') }" v-if="hasPermission('warehouse.other-in')">
                                        <span>其他入库</span>
                                    </Link>

                                    <Link :href="route('warehouse.material-out')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('warehouse.material-out') }" v-if="hasPermission('warehouse.material-out')">
                                        <span>发料单</span>
                                    </Link>

                                    <Link :href="route('warehouse.shipping')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('warehouse.shipping') }" v-if="hasPermission('warehouse.shipping')">
                                        <span>出货单</span>
                                    </Link>
                                </div>
                            </div>

                            <!-- 物流管理 -->
                            <div class="space-y-1" v-if="hasPermission('logistics.view')">
                                <button @click="toggleMenu('logistics')" class="w-full flex items-center justify-between px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('logistics.*') }">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 13v-1a2 2 0 00-2-2H9.5a2 2 0 00-2 2v1M18 18h2a1 1 0 001-1v-2a1 1 0 00-1-1h-2" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20H4a1 1 0 01-1-1v-5" />
                                        </svg>
                                        <span>物流管理</span>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform" :class="{ 'rotate-180': expandedMenus.logistics }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <!-- 物流管理子菜单 -->
                                <div v-show="expandedMenus.logistics" class="pl-7 space-y-1">
                                    <Link :href="route('logistics.packing')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('logistics.packing') }" v-if="hasPermission('logistics.packing')">
                                        <span>装托</span>
                                    </Link>

                                    <Link :href="route('logistics.encasement')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('logistics.encasement') }" v-if="hasPermission('logistics.encasement')">
                                        <span>装箱</span>
                                    </Link>

                                    <Link :href="route('logistics.customsDeclaration')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('logistics.customsDeclaration') }" v-if="hasPermission('logistics.customsDeclaration')">
                                        <span>报关</span>
                                    </Link>

                                    <Link :href="route('logistics.shipping')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('logistics.shipping') }" v-if="hasPermission('logistics.shipping')">
                                        <span>出海</span>
                                    </Link>
                                </div>
                            </div>

                            <!-- 生产管理 -->
                            <div class="space-y-1" v-if="hasPermission('production.view')">
                                <button @click="toggleMenu('production')" class="w-full flex items-center justify-between px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('production.*') }">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                        </svg>
                                        <span>生产管理</span>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform" :class="{ 'rotate-180': expandedMenus.production }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <!-- 生产管理子菜单 -->
                                <div v-show="expandedMenus.production" class="pl-7 space-y-1">
                                    <Link :href="route('production.plans')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('production.plans') }" v-if="hasPermission('production.plans')">
                                        <span>生产计划</span>
                                    </Link>

                                    <Link :href="route('production.work-orders')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('production.work-orders') }" v-if="hasPermission('production.work-orders')">
                                        <span>工单</span>
                                    </Link>

                                    <Link :href="route('production.material-requisitions')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('production.material-requisitions') }" v-if="hasPermission('production.material-requisitions')">
                                        <span>领料单</span>
                                    </Link>
                                </div>
                            </div>

                            <!-- 质量管理 -->
                            <div class="space-y-1" v-if="hasPermission('QC.view')">
                                <button @click="toggleMenu('QC')" class="w-full flex items-center justify-between px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('QC.*') }">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <span>质量管理</span>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform" :class="{ 'rotate-180': expandedMenus.QC }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <!-- 质量管理子菜单 -->
                                <div v-show="expandedMenus.QC" class="pl-7 space-y-1">
                                    <Link :href="route('QC.inspection')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('QC.inspection') }" v-if="hasPermission('QC.inspection')">
                                        <span>检验</span>
                                    </Link>
                                </div>
                            </div>

                            <!-- 财务管理 -->
                            <div class="space-y-1" v-if="hasPermission('finance.view')">
                                <button @click="toggleMenu('finance')" class="w-full flex items-center justify-between px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('finance.*') }">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                        <span>财务管理</span>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform" :class="{ 'rotate-180': expandedMenus.finance }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <!-- 财务管理子菜单 -->
                                <div v-show="expandedMenus.finance" class="pl-7 space-y-1">
                                    <Link :href="route('finance.cost-accounting')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('finance.cost-accounting') }" v-if="hasPermission('finance.cost-accounting')">
                                        <span>成本核算</span>
                                    </Link>

                                    <Link :href="route('finance.receivables')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('finance.receivables') }" v-if="hasPermission('finance.receivables')">
                                        <span>应收账款</span>
                                    </Link>

                                    <Link :href="route('finance.payables')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('finance.payables') }" v-if="hasPermission('finance.payables')">
                                        <span>应付账款</span>
                                    </Link>
                                </div>
                            </div>

                            <!-- BI报表 -->
                            <div class="space-y-1" v-if="hasPermission('reports.view')">
                                <button @click="toggleMenu('reports')" class="w-full flex items-center justify-between px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('reports.*') }">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                        </svg>
                                        <span>BI报表</span>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform" :class="{ 'rotate-180': expandedMenus.reports }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <!-- BI报表子菜单 -->
                                <div v-show="expandedMenus.reports" class="pl-7 space-y-1">
                                    <Link :href="route('reports.annual')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('reports.annual') }" v-if="hasPermission('reports.annual')">
                                        <span>年报</span>
                                    </Link>

                                    <Link :href="route('reports.monthly')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('reports.monthly') }" v-if="hasPermission('reports.monthly')">
                                        <span>月报</span>
                                    </Link>
                                </div>
                            </div>

                            <!-- 基础信息 -->
                            <div class="space-y-1" v-if="hasPermission('settings.view')">
                                <button @click="toggleMenu('settings')" data-menu="settings" class="w-full flex items-center justify-between px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('settings.*') }">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                        <span>基础信息</span>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform" :class="{ 'rotate-180': expandedMenus.settings }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <!-- 基础信息子菜单 -->
                                <div v-show="expandedMenus.settings" class="pl-7 space-y-1">
                                    <Link :href="route('settings.departments')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('settings.departments') }">
                                        <span>部门管理</span>
                                    </Link>

                                    <Link :href="route('settings.roles')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('settings.roles') }">
                                        <span>角色管理</span>
                                    </Link>

                                    <Link :href="route('settings.permissions')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('settings.permissions') }">
                                        <span>权限管理</span>
                                    </Link>

                                    <Link :href="route('settings.users')" class="flex items-center px-2 py-2 rounded-md hover:bg-gray-700 text-sm font-medium" :class="{ 'bg-gray-700': isCurrentPath('settings.users') }">
                                        <span>人员管理</span>
                                    </Link>
                                </div>
                            </div>

                            <!-- 这里将添加更多导航链接 -->
                        </div>
                    </div>

                    <!-- 可拖拽的右侧边缘 -->
                    <div class="absolute top-0 right-0 w-4 h-full cursor-col-resize z-10" @mousedown="startResize">
                        <div class="resize-handle absolute top-0 left-1 w-1 h-full bg-gray-600 hover:bg-blue-500 pointer-events-none"></div>
                    </div>
                </div>

                <!-- 右侧内容区域 -->
                <div class="flex-grow overflow-y-auto scrollbar-content bg-white dark:bg-gray-900" style="padding: 10px !important;">
                    <!-- 添加header插槽 -->
                    <div v-if="$slots.header" class="bg-white dark:bg-gray-800 shadow">
                        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                            <slot name="header"></slot>
                        </div>
                    </div>
                    <!-- 默认内容插槽 -->
                    <div class="app-content-slot">
                        <slot></slot>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
#sidebar {
    transition: width 0.15s ease-out;
    flex-shrink: 0; /* 防止sidebar被压缩 */
}
/* 拖拽时禁用过渡效果，使拖拽更加流畅 */
#sidebar.resizing {
    transition: none;
}
/* 增强拖拽手柄的视觉效果 */
.resize-handle {
    transition: background-color 0.2s;
}
#sidebar:hover .resize-handle {
    opacity: 1;
    width: 2px;
}
/* 针对Settings部分的特殊样式 */
.layout-main {
    min-height: calc(100vh - 65px);
}

/* 优化导航菜单显示样式 */
.bom-tree-row {
    overflow-x: auto; /* 允许内容水平滚动而不是溢出 */
}

/* 自定义滚动条样式 */
.scrollbar-content {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}
.scrollbar-content::-webkit-scrollbar {
    width: 6px;
    height: 6px; /* 添加水平滚动条高度 */
}
.scrollbar-content::-webkit-scrollbar-track {
    background: transparent;
}
.scrollbar-content::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
}
.scrollbar-content::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.7);
}
</style>

<style>
/* 去除全局滚动条 */
html, body {
    overflow: hidden;
    height: 100%;
    margin: 0;
    padding: 0;
}

/* 覆盖Tailwind的p-24相关类 */
.p-24 {
    padding: 10px !important;
}
.px-24 {
    padding-left: 10px !important;
    padding-right: 10px !important;
}
.py-24 {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
}

/* 覆盖Tailwind的p-6相关类 */
.p-6 {
    padding: 10px !important;
}
.px-6 {
    padding-left: 10px !important;
    padding-right: 10px !important;
}
.py-6 {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
}

/* 覆盖内容容器的padding */
.bg-white.dark\:bg-gray-800.overflow-hidden.shadow-xl.sm\:rounded-lg,
.bg-gray-900.dark\:bg-gray-900.overflow-hidden.shadow-xl.sm\:rounded-lg {
    padding: 10px !important;
}
</style>