<script setup lang="ts">
interface Props {
    value?: string;
    required?: boolean;
}

withDefaults(defineProps<Props>(), {
    required: false
});
</script>

<template>
    <label class="block font-medium text-sm text-gray-700 dark:text-gray-300">
        <span v-if="value">{{ value }}<span v-if="required" class="text-red-500 ml-1">*</span></span>
        <span v-else><slot /></span>
    </label>
</template>
