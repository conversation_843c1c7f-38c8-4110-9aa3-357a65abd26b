<script setup lang="ts">
import { Link } from '@inertiajs/vue3';
import { computed } from 'vue';

interface PaginationLink {
  url: string | null;
  label: string;
  active: boolean;
}

interface Props {
  links: PaginationLink[];
}

const props = defineProps<Props>();

// 计算处理后的分页链接
const processedLinks = computed(() => {
  if (!props.links || props.links.length === 0) return [];
  
  return props.links.map(link => {
    const newLink = {...link};
    // 替换标签文本为适当的图标
    if (newLink.label === '&laquo; Previous' || newLink.label === 'pagination.previous') {
      newLink.label = 'previous';
    } else if (newLink.label === 'Next &raquo;' || newLink.label === 'pagination.next') {
      newLink.label = 'next';
    }
    return newLink;
  });
});

// 提取页码链接，排除上一页和下一页按钮
const pageLinks = computed(() => {
  return processedLinks.value.filter(link => 
    link.label !== 'previous' && link.label !== 'next'
  );
});

// 获取上一页链接
const previousLink = computed(() => {
  return processedLinks.value.find(link => link.label === 'previous');
});

// 获取下一页链接
const nextLink = computed(() => {
  return processedLinks.value.find(link => link.label === 'next');
});
</script>

<template>
  <div v-if="links && links.length > 3" class="mt-4 flex justify-end">
    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
      <!-- 上一页按钮 -->
      <div v-if="previousLink">
        <Link 
          v-if="previousLink.url" 
          :href="previousLink.url"
          class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700"
        >
          <span class="sr-only">上一页</span>
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </Link>
        <span 
          v-else
          class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-700 text-sm font-medium text-gray-400 dark:text-gray-500 cursor-not-allowed"
        >
          <span class="sr-only">上一页</span>
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </span>
      </div>

      <!-- 页码按钮 -->
      <template v-for="(link, key) in pageLinks" :key="key">
        <Link 
          v-if="link.url" 
          :href="link.url"
          class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium hover:bg-gray-50 dark:hover:bg-gray-700"
          :class="{ 
            'bg-blue-600 text-white border-blue-600 hover:bg-blue-700': link.active, 
            'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300': !link.active 
          }"
          v-html="link.label"
        />
        <span 
          v-else
          class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-700 text-sm font-medium text-gray-400 dark:text-gray-500 cursor-not-allowed"
          v-html="link.label"
        />
      </template>

      <!-- 下一页按钮 -->
      <div v-if="nextLink">
        <Link 
          v-if="nextLink.url" 
          :href="nextLink.url"
          class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700"
        >
          <span class="sr-only">下一页</span>
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
        </Link>
        <span 
          v-else
          class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-700 text-sm font-medium text-gray-400 dark:text-gray-500 cursor-not-allowed"
        >
          <span class="sr-only">下一页</span>
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
        </span>
      </div>
    </nav>
  </div>
</template>

<style scoped>
/* 确保链接和禁用的分页项目有正确的圆角 */
.rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}
.rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}
</style>