<script setup lang="ts">
import { usePage } from '@inertiajs/vue3';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import { computed } from 'vue';

interface Props {
    permission: string;
    variant?: 'primary' | 'secondary' | 'danger';
    type?: 'button' | 'submit' | 'reset';
    href?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
    variant: 'primary',
    type: 'button',
    href: null
});

// 解析权限字符串，例如：'roles.edit' => {module: 'roles', action: 'edit'}
const parsedPermission = computed(() => {
    const parts = props.permission.split('.');
    if (parts.length !== 2) {
        console.error('权限格式错误，应为 "module.action" 格式');
        return { module: '', action: '' };
    }
    return {
        module: parts[0],
        action: parts[1]
    };
});

interface PageProps {
    permissions?: Record<string, Record<string, boolean>>;
}

// 判断用户是否有指定权限
const hasPermission = computed(() => {
    try {
        const page = usePage();
        const { module, action } = parsedPermission.value;
        
        // 使用可选链操作符安全访问数据
        const pageProps = page.props as PageProps;
        return pageProps?.permissions?.[module]?.[action] === true;
    } catch (error) {
        console.error('权限检查错误:', error);
        return false;
    }
});
</script>

<template>
    <PrimaryButton v-if="hasPermission" v-bind="$attrs" :type="type" :class="[
        variant === 'primary' ? 'bg-indigo-600 hover:bg-indigo-500' : 
        variant === 'secondary' ? 'bg-gray-500 hover:bg-gray-400' : 
        variant === 'danger' ? 'bg-red-600 hover:bg-red-500' : ''
    ]">
        <slot></slot>
    </PrimaryButton>
</template> 