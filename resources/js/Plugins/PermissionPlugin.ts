import { App } from 'vue';
import { hasPermission, hasRole } from '../Utils/PermissionUtil';

export const PermissionPlugin = {
    install(app: App): void {
        // 注册全局属性，可在任何组件中通过this.$can访问
        app.config.globalProperties.$can = hasPermission;
        app.config.globalProperties.$hasRole = hasRole;
        
        // 提供全局方法，可在setup中通过inject访问
        app.provide('can', hasPermission);
        app.provide('hasRole', hasRole);
    }
}; 