interface ZiggyRoute {
    uri: string;
    methods: string[];
    parameters?: string[];
    bindings?: Record<string, string>;
    wheres?: Record<string, string>;
}

interface ZiggyRoutes {
    [key: string]: <PERSON>iggyRoute;
}

interface ZiggyConfig {
    url: string;
    port: number | null;
    defaults: Record<string, any>;
    routes: ZiggyRoutes;
}

const Ziggy: ZiggyConfig = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"login":{"uri":"login","methods":["GET","HEAD"]},"login.store":{"uri":"login","methods":["POST"]},"logout":{"uri":"logout","methods":["POST"]},"user-profile-information.update":{"uri":"user\/profile-information","methods":["PUT"]},"user-password.update":{"uri":"user\/password","methods":["PUT"]},"password.confirm":{"uri":"user\/confirm-password","methods":["GET","HEAD"]},"password.confirmation":{"uri":"user\/confirmed-password-status","methods":["GET","HEAD"]},"password.confirm.store":{"uri":"user\/confirm-password","methods":["POST"]},"two-factor.login":{"uri":"two-factor-challenge","methods":["GET","HEAD"]},"two-factor.login.store":{"uri":"two-factor-challenge","methods":["POST"]},"two-factor.enable":{"uri":"user\/two-factor-authentication","methods":["POST"]},"two-factor.confirm":{"uri":"user\/confirmed-two-factor-authentication","methods":["POST"]},"two-factor.disable":{"uri":"user\/two-factor-authentication","methods":["DELETE"]},"two-factor.qr-code":{"uri":"user\/two-factor-qr-code","methods":["GET","HEAD"]},"two-factor.secret-key":{"uri":"user\/two-factor-secret-key","methods":["GET","HEAD"]},"two-factor.recovery-codes":{"uri":"user\/two-factor-recovery-codes","methods":["GET","HEAD"]},"profile.show":{"uri":"user\/profile","methods":["GET","HEAD"]},"other-browser-sessions.destroy":{"uri":"user\/other-browser-sessions","methods":["DELETE"]},"current-user-photo.destroy":{"uri":"user\/profile-photo","methods":["DELETE"]},"current-user.destroy":{"uri":"user","methods":["DELETE"]},"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"home":{"uri":"home","methods":["GET","HEAD"]},"logout.get":{"uri":"logout","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"products.bom":{"uri":"products\/bom","methods":["GET","HEAD"]},"products.materials":{"uri":"products\/materials","methods":["GET","HEAD"]},"sales.customers":{"uri":"sales\/customers","methods":["GET","HEAD"]},"sales.price":{"uri":"sales\/price","methods":["GET","HEAD"]},"sales.orders":{"uri":"sales\/orders","methods":["GET","HEAD"]},"sales.contracts":{"uri":"sales\/contracts","methods":["GET","HEAD"]},"sales.delivery":{"uri":"sales\/delivery","methods":["GET","HEAD"]},"sales.invoices":{"uri":"sales\/invoices","methods":["GET","HEAD"]},"purchases.suppliers":{"uri":"purchases\/suppliers","methods":["GET","HEAD"]},"purchases.price":{"uri":"purchases\/price","methods":["GET","HEAD"]},"purchases.mrp":{"uri":"purchases\/mrp","methods":["GET","HEAD"]},"purchases.orders":{"uri":"purchases\/orders","methods":["GET","HEAD"]},"purchases.pricing":{"uri":"purchases\/pricing","methods":["GET","HEAD"]},"purchases.invoices":{"uri":"purchases\/invoices","methods":["GET","HEAD"]},"warehouse.purchase-in":{"uri":"warehouse\/purchase-in","methods":["GET","HEAD"]},"warehouse.other-in":{"uri":"warehouse\/other-in","methods":["GET","HEAD"]},"warehouse.material-out":{"uri":"warehouse\/material-out","methods":["GET","HEAD"]},"warehouse.shipping":{"uri":"warehouse\/shipping","methods":["GET","HEAD"]},"logistics.packing":{"uri":"logistics\/packing","methods":["GET","HEAD"]},"logistics.encasement":{"uri":"logistics\/encasement","methods":["GET","HEAD"]},"logistics.customsDeclaration":{"uri":"logistics\/customsDeclaration","methods":["GET","HEAD"]},"logistics.shipping":{"uri":"logistics\/shipping","methods":["GET","HEAD"]},"production.plans":{"uri":"production\/plans","methods":["GET","HEAD"]},"production.work-orders":{"uri":"production\/work-orders","methods":["GET","HEAD"]},"production.material-requisitions":{"uri":"production\/material-requisitions","methods":["GET","HEAD"]},"QC.inspection":{"uri":"QC\/inspection","methods":["GET","HEAD"]},"finance.cost-accounting":{"uri":"finance\/cost-accounting","methods":["GET","HEAD"]},"finance.receivables":{"uri":"finance\/receivables","methods":["GET","HEAD"]},"finance.payables":{"uri":"finance\/payables","methods":["GET","HEAD"]},"reports.annual":{"uri":"reports\/annual","methods":["GET","HEAD"]},"reports.monthly":{"uri":"reports\/monthly","methods":["GET","HEAD"]},"settings.departments":{"uri":"settings\/departments","methods":["GET","HEAD"]},"settings.departments.create":{"uri":"settings\/departments\/create","methods":["GET","HEAD"]},"settings.departments.store":{"uri":"settings\/departments","methods":["POST"]},"settings.departments.show":{"uri":"settings\/departments\/{department}","methods":["GET","HEAD"],"parameters":["department"],"bindings":{"department":"id"}},"settings.departments.edit":{"uri":"settings\/departments\/{department}\/edit","methods":["GET","HEAD"],"parameters":["department"],"bindings":{"department":"id"}},"settings.departments.update":{"uri":"settings\/departments\/{department}","methods":["PUT"],"parameters":["department"],"bindings":{"department":"id"}},"settings.departments.destroy":{"uri":"settings\/departments\/{department}","methods":["DELETE"],"parameters":["department"],"bindings":{"department":"id"}},"settings.roles":{"uri":"settings\/roles","methods":["GET","HEAD"]},"settings.roles.create":{"uri":"settings\/roles\/create","methods":["GET","HEAD"]},"settings.roles.store":{"uri":"settings\/roles","methods":["POST"]},"settings.roles.show":{"uri":"settings\/roles\/{role}","methods":["GET","HEAD"],"parameters":["role"],"bindings":{"role":"id"}},"settings.roles.edit":{"uri":"settings\/roles\/{role}\/edit","methods":["GET","HEAD"],"parameters":["role"],"bindings":{"role":"id"}},"settings.roles.update":{"uri":"settings\/roles\/{role}","methods":["PUT"],"parameters":["role"],"bindings":{"role":"id"}},"settings.roles.destroy":{"uri":"settings\/roles\/{role}","methods":["DELETE"],"parameters":["role"],"bindings":{"role":"id"}},"settings.roles.assign-users":{"uri":"settings\/roles\/{role}\/assign-users","methods":["POST"],"parameters":["role"],"bindings":{"role":"id"}},"settings.permissions":{"uri":"settings\/permissions","methods":["GET","HEAD"]},"settings.permissions.create":{"uri":"settings\/permissions\/create","methods":["GET","HEAD"]},"settings.permissions.store":{"uri":"settings\/permissions","methods":["POST"]},"settings.permissions.show":{"uri":"settings\/permissions\/{permission}","methods":["GET","HEAD"],"parameters":["permission"],"bindings":{"permission":"id"}},"settings.permissions.edit":{"uri":"settings\/permissions\/{permission}\/edit","methods":["GET","HEAD"],"parameters":["permission"],"bindings":{"permission":"id"}},"settings.permissions.update":{"uri":"settings\/permissions\/{permission}","methods":["PUT"],"parameters":["permission"],"bindings":{"permission":"id"}},"settings.permissions.destroy":{"uri":"settings\/permissions\/{permission}","methods":["DELETE"],"parameters":["permission"],"bindings":{"permission":"id"}},"settings.users":{"uri":"settings\/users","methods":["GET","HEAD"]},"settings.users.create":{"uri":"settings\/users\/create","methods":["GET","HEAD"]},"settings.users.store":{"uri":"settings\/users","methods":["POST"]},"settings.users.show":{"uri":"settings\/users\/{user}","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"settings.users.edit":{"uri":"settings\/users\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"settings.users.update":{"uri":"settings\/users\/{user}","methods":["PUT"],"parameters":["user"],"bindings":{"user":"id"}},"settings.users.destroy":{"uri":"settings\/users\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"id"}},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};

declare global {
    interface Window {
        Ziggy?: ZiggyConfig;
    }
}

if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy }; 