import { ref, onMounted, onUnmounted } from 'vue';
import apiService from '@/Utils/ApiService';
import type { User } from '@/types';

interface AuthUser {
    id: number;
    name: string;
    account: string;
    email?: string;
    locale?: string;
    [key: string]: any;
}

interface AuthResponse {
    token: string;
    user: AuthUser;
}

interface UserApiResponse {
    user: AuthUser;
}

export function useAuth() {
    const isAuthenticated = ref<boolean>(false);
    const isLoading = ref<boolean>(true);
    const user = ref<AuthUser | null>(null);
    const authError = ref<string | null>(null);
    
    // 检查认证状态
    const checkAuth = async (): Promise<boolean> => {
        try {
            isLoading.value = true;
            authError.value = null;
            
            // 获取用户信息
            const response = await apiService.getCurrentUser();
            user.value = response.data.user;
            isAuthenticated.value = true;
            return true;
        } catch (error: any) {
            console.error('认证检查失败:', error);
            isAuthenticated.value = false;
            user.value = null;
            
            // 设置错误信息
            if (error.response?.status === 401) {
                authError.value = '认证已过期，请重新登录';
            } else {
                authError.value = error.response?.data?.message || '认证检查失败';
            }
            return false;
        } finally {
            isLoading.value = false;
        }
    };
    
    // 登录
    const login = async (account: string, password: string): Promise<boolean> => {
        try {
            isLoading.value = true;
            authError.value = null;
            
            const response = await apiService.login(account, password);
            user.value = response.user;
            isAuthenticated.value = true;
            return true;
        } catch (error: any) {
            console.error('登录失败:', error);
            isAuthenticated.value = false;
            user.value = null;
            
            // 设置错误信息
            authError.value = error.response?.data?.message || '登录失败，请检查账号和密码';
            return false;
        } finally {
            isLoading.value = false;
        }
    };
    
    // 登出
    const logout = async (): Promise<void> => {
        try {
            await apiService.logout();
        } catch (error) {
            console.error('登出请求失败:', error);
        } finally {
            // 即使API请求失败，也清除本地状态
            isAuthenticated.value = false;
            user.value = null;
            apiService.clearAuthToken();
        }
    };
    
    // 组件挂载时检查认证状态
    onMounted(async () => {
        await checkAuth();
    });
    
    return {
        isAuthenticated,
        isLoading,
        user,
        authError,
        login,
        logout,
        checkAuth
    };
} 