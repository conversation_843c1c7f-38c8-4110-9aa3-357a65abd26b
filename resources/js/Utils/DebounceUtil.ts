/**
 * 防抖函数工具
 * 用于限制函数的执行频率，避免频繁触发
 */

/**
 * 创建一个防抖函数
 * @param fn 需要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖处理后的函数
 */
export function debounce<T extends (...args: any[]) => void>(
    fn: T, 
    delay: number
): ((...args: Parameters<T>) => void) {
    let timer: ReturnType<typeof setTimeout> | null = null;
    
    return (...args: Parameters<T>) => {
        if (timer) clearTimeout(timer);
        timer = setTimeout(() => {
            fn(...args);
        }, delay);
    };
} 