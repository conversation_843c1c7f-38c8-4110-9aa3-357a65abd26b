import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import type { User } from '@/types';

/**
 * 用户类型定义
 */
export interface AuthUser {
  id: number;
  name: string;
  account: string;
  email?: string;
  locale?: string;
  [key: string]: any;
}

/**
 * 登录响应类型
 */
export interface LoginResponse {
  token: string;
  user: AuthUser;
}

/**
 * API服务实现类
 */
class ApiServiceImpl {
    private axios: AxiosInstance;

    constructor() {
        this.axios = axios.create({
            baseURL: '/api',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            withCredentials: true 
        });

        this.setupInterceptors();
        this.initializeAuth();
    }

    private setupInterceptors(): void {
        this.axios.interceptors.request.use(
            config => {
                const token = localStorage.getItem('auth_token');
                if (token) {
                    config.headers['Authorization'] = `Bearer ${token}`;
                }
                return config;
            },
            error => Promise.reject(error)
        );

        this.axios.interceptors.response.use(
            response => response,
            error => {
                if (error.response && error.response.status === 401) {
                    console.error('认证失败:', error.response.data.message || 'Unauthenticated');
                    localStorage.removeItem('auth_token');
                    
                    if (window.location.pathname !== '/login') {
                        window.location.href = '/login';
                    }
                }
                return Promise.reject(error);
            }
        );
    }

    private initializeAuth(): void {
        const token = localStorage.getItem('auth_token');
        if (token) {
            this.setAuthToken(token);
            this.validateToken().catch(() => {
                this.clearAuthToken();
                if (window.location.pathname !== '/login') {
                    window.location.href = '/login';
                }
            });
        }
    }

    async validateToken(): Promise<boolean> {
        try {
            await this.axios.get('/user');
            return true;
        } catch (error) {
            return false;
        }
    }

    setAuthToken(token: string): void {
        if (!token) return;
        
        localStorage.setItem('auth_token', token);
        this.axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }

    clearAuthToken(): void {
        localStorage.removeItem('auth_token');
        delete this.axios.defaults.headers.common['Authorization'];
    }

    async login(account: string, password: string, deviceName: string | null = null): Promise<LoginResponse> {
        try {
            const response: AxiosResponse<LoginResponse> = await this.axios.post('/login', {
                account,
                password,
                device_name: deviceName || navigator.userAgent
            });
            
            const { token, user } = response.data;
            this.setAuthToken(token);
            return { token, user };
        } catch (error) {
            throw error;
        }
    }

    async logout(): Promise<void> {
        try {
            await this.axios.post('/logout');
            this.clearAuthToken();
        } catch (error) {
            this.clearAuthToken();
            throw error;
        }
    }

    async getCurrentUser(): Promise<AxiosResponse<{user: User}>> {
        return this.axios.get('/user');
    }

    async getTokenFromCurrentSession(): Promise<string | null> {
        try {
            const response: AxiosResponse<{token?: string}> = await this.axios.post('/login', {
                current_session: true
            });
            
            if (response.data && response.data.token) {
                this.setAuthToken(response.data.token);
                return response.data.token;
            }
            return null;
        } catch (error) {
            console.error('获取当前会话令牌失败:', error);
            return null;
        }
    }

    async get<T = any>(url: string, params: Record<string, any> = {}): Promise<AxiosResponse<T>> {
        return this.axios.get<T>(url, { params });
    }

    async post<T = any>(url: string, data: Record<string, any> = {}): Promise<AxiosResponse<T>> {
        return this.axios.post<T>(url, data);
    }

    async put<T = any>(url: string, data: Record<string, any> = {}): Promise<AxiosResponse<T>> {
        return this.axios.put<T>(url, data);
    }

    async delete<T = any>(url: string): Promise<AxiosResponse<T>> {
        return this.axios.delete<T>(url);
    }

    async request<T = any>(
        method: string, 
        url: string, 
        data: Record<string, any> | null = null, 
        config: AxiosRequestConfig = {}
    ): Promise<AxiosResponse<T>> {
        const apiUrl = url.startsWith('/api') 
            ? url
            : url.startsWith('/') ? `/api${url}` : `/api/${url}`;
        
        return this.axios.request<T>({
            ...config,
            method,
            url: apiUrl,
            data
        });
    }
}

// 创建服务实例
const apiService = new ApiServiceImpl();

// 导出实例
export default apiService; 