import { usePage } from '@inertiajs/vue3';

type PermissionName = string;
type PermissionsMap = Record<string, Record<string, boolean>>;

/**
 * 检查当前用户是否有指定权限
 * @param {string|string[]} permission 权限名称或权限名称数组
 * @returns {boolean} 是否有权限
 */
export function hasPermission(permission: PermissionName | PermissionName[]): boolean {
    try {
        const permissions = usePage()?.props?.permissions as PermissionsMap || {};
        
        if (Array.isArray(permission)) {
            return permission.some(p => checkSinglePermission(p, permissions));
        }
        
        return checkSinglePermission(permission, permissions);
    } catch (error) {
        console.error('权限检查错误:', error);
        return false;
    }
}

/**
 * 检查单个权限
 * @param {string} permission 权限名称，格式为 "模块.操作"
 * @param {PermissionsMap} permissions 权限对象
 * @returns {boolean}
 */
function checkSinglePermission(
    permission: string, 
    permissions: PermissionsMap
): boolean {
    if (!permission || typeof permission !== 'string') return false;
    
    const parts = permission.split('.');
    if (parts.length !== 2) return false;
    
    const [module, action] = parts;
    return !!permissions?.[module]?.[action];
}

/**
 * 检查用户是否有指定角色
 * @param {string} role 角色名称
 * @returns {boolean}
 */
export function hasRole(role: string): boolean {
    // 因为我们没有在前端直接共享角色数据，这个方法可以通过检查与角色关联的权限来模拟
    // 这里仅为示例，如果需要准确的角色检查，建议在后端实现或在前端共享角色数据
    const rolePermissionMap: Record<string, string[]> = {
        'admin': ['roles.view', 'roles.edit', 'permissions.view', 'permissions.edit'],
        'manager': ['users.view', 'departments.view'],
        'user': ['dashboard']
    };
    
    const permissions = rolePermissionMap[role] || [];
    return permissions.length > 0 && permissions.every(p => hasPermission(p));
} 