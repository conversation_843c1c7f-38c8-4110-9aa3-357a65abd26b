<script setup lang="ts">
import { Link } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

interface Department {
  id: number;
  name: string;
}

interface Role {
  id: number;
  name: string;
  display_name: string;
  description?: string;
}

interface Company {
  company_code: string;
  company_name: string;
}

interface User {
  id: number;
  name: string;
  email?: string;
  employee_id: string;
  account?: string;
  department?: Department;
  position?: string;
  phone?: string;
  address?: string;
  hire_date?: string;
  status: '在职' | '离职' | '休假';
  emergency_contact?: string;
  emergency_phone?: string;
  notes?: string;
  default_company_code?: string;
  companies: Company[];
}

interface Props {
  user: User;
  userRoles: Role[];
}

const props = defineProps<Props>();

interface StatusClasses {
  [key: string]: string;
}

const statusClasses: StatusClasses = {
  '在职': 'bg-green-900 text-green-300',
  '离职': 'bg-red-900 text-red-300',
  '休假': 'bg-yellow-900 text-yellow-300',
};

// 格式化日期显示
const formatDate = (dateString?: string): string => {
  if (!dateString) return '未设置';
  
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

// 声明route函数类型
declare function route(name: string, params?: any): string;
</script>

<template>
    <AppLayout title="人员详情">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">人员详情</h2>
                <div class="flex gap-2">
                    <Link :href="route('settings.users.edit', user.id)" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                        编辑
                    </Link>
                    <Link :href="route('settings.users')" class="px-4 py-2 bg-gray-700 text-gray-200 rounded hover:bg-gray-600 transition-colors">
                        返回列表
                    </Link>
                </div>
            </div>

            <div class="bg-gray-800 overflow-hidden shadow-xl rounded-lg p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- 基本信息 -->
                    <div class="space-y-6">
                        <h3 class="text-lg font-medium text-gray-100 border-b border-gray-700 pb-2">基本信息</h3>
                        
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-y-4 gap-x-6">
                            <div>
                                <div class="text-sm text-gray-400">工号</div>
                                <div class="mt-1 text-gray-100 font-medium">{{ user.employee_id }}</div>
                            </div>
                            
                            <div>
                                <div class="text-sm text-gray-400">姓名</div>
                                <div class="mt-1 text-gray-100 font-medium">{{ user.account ? `[${user.account}] ` : '' }}{{ user.name }}</div>
                            </div>
                            
                            <div>
                                <div class="text-sm text-gray-400">部门</div>
                                <div class="mt-1 text-gray-100 font-medium">{{ user.department ? user.department.name : '-' }}</div>
                            </div>
                            
                            <div>
                                <div class="text-sm text-gray-400">职位</div>
                                <div class="mt-1 text-gray-100 font-medium">{{ user.position || '-' }}</div>
                            </div>
                            
                            <div>
                                <div class="text-sm text-gray-400">状态</div>
                                <div class="mt-1">
                                    <span :class="[statusClasses[user.status], 'px-2 py-1 text-xs rounded-full']">
                                        {{ user.status }}
                                    </span>
                                </div>
                            </div>
                            
                            <div>
                                <div class="text-sm text-gray-400">入职日期</div>
                                <div class="mt-1 text-gray-100 font-medium">{{ user.hire_date || '-' }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 联系信息 -->
                    <div class="space-y-6">
                        <h3 class="text-lg font-medium text-gray-100 border-b border-gray-700 pb-2">联系信息</h3>
                        
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-y-4 gap-x-6">
                            <div>
                                <div class="text-sm text-gray-400">电子邮箱</div>
                                <div class="mt-1 text-gray-100 font-medium">{{ user.email }}</div>
                            </div>
                            
                            <div>
                                <div class="text-sm text-gray-400">联系电话</div>
                                <div class="mt-1 text-gray-100 font-medium">{{ user.phone || '-' }}</div>
                            </div>
                            
                            <div class="sm:col-span-2">
                                <div class="text-sm text-gray-400">地址</div>
                                <div class="mt-1 text-gray-100 font-medium">{{ user.address || '-' }}</div>
                            </div>
                            
                            <div>
                                <div class="text-sm text-gray-400">紧急联系人</div>
                                <div class="mt-1 text-gray-100 font-medium">{{ user.emergency_contact || '-' }}</div>
                            </div>
                            
                            <div>
                                <div class="text-sm text-gray-400">紧急联系电话</div>
                                <div class="mt-1 text-gray-100 font-medium">{{ user.emergency_phone || '-' }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 公司信息 -->
                    <div class="md:col-span-2 space-y-6">
                        <h3 class="text-lg font-medium text-gray-100 border-b border-gray-700 pb-2">公司信息</h3>
                        
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-y-4 gap-x-6">
                            <div>
                                <div class="text-sm text-gray-400">默认公司</div>
                                <div class="mt-1 text-gray-100 font-medium">
                                    {{ user.companies.find(c => c.company_code === user.default_company_code)?.company_name || '-' }}
                                </div>
                            </div>
                        </div>

                        <div>
                            <div class="text-sm text-gray-400 mb-2">授权公司</div>
                            <div class="flex flex-wrap gap-2">
                                <span 
                                    v-for="company in user.companies" 
                                    :key="company.company_code"
                                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-900 text-blue-300"
                                >
                                    {{ company.company_name }}
                                </span>
                                <span v-if="!user.companies.length" class="text-gray-400">未分配公司</span>
                            </div>
                        </div>
                    </div>

                    <!-- 角色与权限 -->
                    <div class="md:col-span-2 space-y-6">
                        <h3 class="text-lg font-medium text-gray-100 border-b border-gray-700 pb-2">角色与权限</h3>
                        
                        <div>
                            <div class="text-sm text-gray-400 mb-2">分配的角色</div>
                            <div class="flex flex-wrap gap-2">
                                <span 
                                    v-for="role in userRoles" 
                                    :key="role.id"
                                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-900 text-blue-300"
                                >
                                    {{ role.display_name || role.name }}
                                </span>
                                <span v-if="!userRoles.length" class="text-gray-400">未分配角色</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 备注 -->
                    <div class="md:col-span-2 space-y-6" v-if="user.notes">
                        <h3 class="text-lg font-medium text-gray-100 border-b border-gray-700 pb-2">备注信息</h3>
                        
                        <div class="bg-gray-700 p-4 rounded-md">
                            <p class="text-gray-300 whitespace-pre-line">{{ user.notes }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>