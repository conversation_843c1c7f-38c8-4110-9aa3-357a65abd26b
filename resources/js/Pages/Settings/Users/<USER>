<script setup lang="ts">
import { ref, watch } from 'vue';
import { Link, router } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import Pagination from '@/Components/Pagination.vue';
import SearchFilter from '@/Components/SearchFilter.vue';
import debounce from 'lodash/debounce';

interface User {
    id: number;
    name: string;
    account: string;
    employee_id: string;
    department: string;
    position: string;
    status: '在职' | '离职' | '休假';
    phone: string;
    email?: string;
}

interface StatusOption {
    value: string;
    label: string;
}

interface StatusClasses {
    [key: string]: string;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginatedUsers {
    data: User[];
    links: PaginationLink[];
    [key: string]: any;
}

interface Filters {
    search?: string;
    status?: string;
    department?: string;
    [key: string]: any;
}

interface Props {
    users: PaginatedUsers;
    filters: Filters;
}

const props = defineProps<Props>();

const search = ref(props.filters.search || '');
const status = ref(props.filters.status || '');
const department = ref(props.filters.department || '');

const statusOptions: StatusOption[] = [
    { value: '在职', label: '在职' },
    { value: '离职', label: '离职' },
    { value: '休假', label: '休假' },
];

const debouncedSearch = debounce(() => {
    router.get(
        route('settings.users'),
        { search: search.value, status: status.value, department: department.value },
        { preserveState: true, preserveScroll: true }
    );
}, 300);

watch([search, status, department], () => {
    debouncedSearch();
});

function confirmDelete(user: User): void {
    if (confirm(`确定要删除"${user.name}"吗？此操作无法撤销。`)) {
        router.delete(route('settings.users.destroy', user.id));
    }
}

const statusClasses: StatusClasses = {
    '在职': 'bg-green-900 text-green-300',
    '离职': 'bg-red-900 text-red-300',
    '休假': 'bg-yellow-900 text-yellow-300',
};

// 声明route函数类型
declare function route(name: string, params?: any): string;
</script>

<template>
    <AppLayout title="人员管理">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">人员管理</h2>
                <Link :href="route('settings.users.create')" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors flex items-center">
                    <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    添加人员
                </Link>
            </div>

            <div class="bg-gray-800 overflow-hidden rounded-lg shadow-lg">
                <div class="p-6">
                    <!-- 搜索和筛选区域 -->
                    <div class="flex flex-wrap gap-4 mb-6">
                        <SearchFilter v-model="search" class="w-64" placeholder="搜索人员..." />
                        
                        <div class="w-48">
                            <select v-model="status" class="w-full rounded-md border-gray-600 bg-gray-700 text-gray-300 focus:border-blue-500 focus:ring-blue-500 shadow-sm">
                                <option value="">所有状态</option>
                                <option v-for="option in statusOptions" :key="option.value" :value="option.value">
                                    {{ option.label }}
                                </option>
                            </select>
                        </div>
                        
                        <div class="w-48">
                            <input 
                                type="text" 
                                v-model="department" 
                                placeholder="部门" 
                                class="w-full rounded-md border-gray-600 bg-gray-700 text-gray-300 focus:border-blue-500 focus:ring-blue-500 shadow-sm"
                            />
                        </div>
                    </div>

                    <!-- 用户表格 -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-700">
                            <thead class="bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                        工号
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                        账号
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                        姓名
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                        部门
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                        职位
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                        状态
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                        联系电话
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                        操作
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-gray-800 divide-y divide-gray-700">
                                <tr v-for="user in users.data" :key="user.id" class="hover:bg-gray-700">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                                        {{ user.employee_id }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        {{ user.account }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        {{ user.name }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        {{ user.department }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        {{ user.position }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span :class="[statusClasses[user.status], 'px-2 py-1 text-xs rounded-full']">
                                            {{ user.status }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        {{ user.phone }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <Link :href="route('settings.users.show', user.id)" class="px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                                查看
                                            </Link>
                                            <Link :href="route('settings.users.edit', user.id)" class="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                                                编辑
                                            </Link>
                                            <button @click="confirmDelete(user)" class="px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                                                删除
                                            </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="mt-6">
                        <Pagination :links="users.links" />
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template> 