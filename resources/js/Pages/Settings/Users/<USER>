<script setup lang="ts">
import { ref } from 'vue';
import { useForm, Link } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import SelectInput from '@/Components/SelectInput.vue';
import Checkbox from '@/Components/Checkbox.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';

interface Department {
  id: number;
  name: string;
  parent_id?: number | null;
  level: number;
}

interface Role {
  id: number;
  name: string;
  display_name: string;
  description?: string;
}

interface Company {
  company_code: string;
  company_name: string;
}

interface User {
  id: number;
  name: string;
  email: string;
  employee_id: string;
  account?: string;
  department_id: number | string;
  position?: string;
  phone?: string;
  address?: string;
  hire_date?: string;
  status: '在职' | '离职' | '休假';
  emergency_contact?: string;
  emergency_phone?: string;
  notes?: string;
  locale?: string;
  default_company_code?: string;
}

interface Props {
  user: User;
  roles: Role[];
  userRoles: string[];
  departments: Department[];
  companies: Company[];
  userCompanyCodes: string[];
}

const props = defineProps<Props>();

interface UserForm {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  employee_id: string;
  account: string;
  department_id: string;
  position: string;
  phone: string;
  address: string;
  hire_date: string;
  status: '在职' | '离职' | '休假';
  emergency_contact: string;
  emergency_phone: string;
  notes: string;
  roles: string[];
  locale: string;
  default_company_code: string;
  company_codes: string[];
}

const form = useForm<UserForm>({
  name: props.user.name,
  email: props.user.email,
  password: '',
  password_confirmation: '',
  employee_id: props.user.employee_id,
  account: props.user.account || '',
  department_id: props.user.department_id ? String(props.user.department_id) : '',
  position: props.user.position || '',
  phone: props.user.phone || '',
  address: props.user.address || '',
  hire_date: props.user.hire_date || '',
  status: props.user.status,
  emergency_contact: props.user.emergency_contact || '',
  emergency_phone: props.user.emergency_phone || '',
  notes: props.user.notes || '',
  roles: props.userRoles || [],
  locale: props.user.locale || 'zh-CN',
  default_company_code: props.user.default_company_code || '',
  company_codes: props.userCompanyCodes || [],
});

interface StatusOption {
  value: '在职' | '离职' | '休假';
  label: string;
}

const statusOptions: StatusOption[] = [
  { value: '在职', label: '在职' },
  { value: '离职', label: '离职' },
  { value: '休假', label: '休假' },
];

function submit(): void {
  form.put(route('settings.users.update', props.user.id), {
    preserveScroll: true,
  });
}

// 声明route函数类型
declare function route(name: string, params?: any): string;
</script>

<template>
  <AppLayout title="编辑人员">
    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">编辑人员</h2>
        <Link :href="route('settings.users')" class="px-4 py-2 bg-gray-700 text-gray-200 rounded hover:bg-gray-600 transition-colors">
          返回列表
        </Link>
      </div>

      <div class="bg-gray-800 overflow-hidden shadow-xl rounded-lg p-6">
        <form @submit.prevent="submit" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 基本信息 -->
            <div class="space-y-6">
              <h3 class="text-lg font-medium text-gray-100 border-b border-gray-700 pb-2">基本信息</h3>

              <div>
                <InputLabel for="name" value="姓名" class="text-gray-200" required />
                <TextInput
                  id="name"
                  type="text"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600"
                  v-model="form.name"
                  autofocus
                />
                <InputError class="mt-2" :message="form.errors.name" />
              </div>

              <div>
                <InputLabel for="account" value="账号" class="text-gray-200" required />
                <TextInput
                  id="account"
                  type="text"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600 cursor-not-allowed opacity-75"
                  v-model="form.account"
                  readonly
                />
                <p class="text-xs text-gray-400 mt-1">账号信息创建后不可修改</p>
                <InputError class="mt-2" :message="form.errors.account" />
              </div>

              <div>
                <InputLabel for="email" value="电子邮箱" class="text-gray-200" />
                <TextInput
                  id="email"
                  type="email"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600"
                  v-model="form.email"
                />
                <InputError class="mt-2" :message="form.errors.email" />
              </div>

              <div>
                <InputLabel for="password" value="密码 (不修改请留空)" class="text-gray-200" />
                <TextInput
                  id="password"
                  type="password"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600"
                  v-model="form.password"
                />
                <InputError class="mt-2" :message="form.errors.password" />
              </div>

              <div>
                <InputLabel for="password_confirmation" value="确认密码" class="text-gray-200" />
                <TextInput
                  id="password_confirmation"
                  type="password"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600"
                  v-model="form.password_confirmation"
                />
                <InputError class="mt-2" :message="form.errors.password_confirmation" />
              </div>

              <div>
                <InputLabel for="employee_id" value="工号" class="text-gray-200" required />
                <TextInput
                  id="employee_id"
                  type="text"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600"
                  v-model="form.employee_id"
                />
                <InputError class="mt-2" :message="form.errors.employee_id" />
              </div>

              <div>
                <InputLabel for="department_id" value="部门" class="text-gray-200" required />
                <SelectInput
                  id="department_id"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600"
                  v-model="form.department_id"
                >
                  <option value="">选择部门</option>
                  <option v-for="dept in departments" :key="dept.id" :value="String(dept.id)">
                    {{ dept.level > 0 ? '　'.repeat(dept.level) + '└─ ' + dept.name : dept.name }}
                  </option>
                </SelectInput>
                <InputError class="mt-2" :message="form.errors.department_id" />
              </div>

              <div>
                <InputLabel for="position" value="职位" class="text-gray-200" />
                <TextInput
                  id="position"
                  type="text"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600"
                  v-model="form.position"
                />
                <InputError class="mt-2" :message="form.errors.position" />
              </div>
            </div>

            <!-- 联系信息和其他 -->
            <div class="space-y-6">
              <h3 class="text-lg font-medium text-gray-100 border-b border-gray-700 pb-2">联系信息与其他</h3>

              <div>
                <InputLabel for="phone" value="联系电话" class="text-gray-200" />
                <TextInput
                  id="phone"
                  type="text"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600"
                  v-model="form.phone"
                />
                <InputError class="mt-2" :message="form.errors.phone" />
              </div>

              <div>
                <InputLabel for="address" value="地址" class="text-gray-200" />
                <TextInput
                  id="address"
                  type="text"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600"
                  v-model="form.address"
                />
                <InputError class="mt-2" :message="form.errors.address" />
              </div>

              <div>
                <InputLabel for="hire_date" value="入职日期" class="text-gray-200" />
                <TextInput
                  id="hire_date"
                  type="date"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600"
                  v-model="form.hire_date"
                />
                <InputError class="mt-2" :message="form.errors.hire_date" />
              </div>

              <!-- 默认公司 -->
              <div>
                <InputLabel for="default_company_code" value="默认公司" class="text-gray-200" />
                <select
                  id="default_company_code"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                  v-model="form.default_company_code"
                >
                  <option value="">请选择默认公司</option>
                  <option v-for="company in companies" :key="company.company_code" :value="company.company_code">
                    {{ company.company_name }}
                  </option>
                </select>
                <InputError class="mt-2" :message="form.errors.default_company_code" />
              </div>

              <!-- 授权公司 -->
              <div>
                <InputLabel for="company_codes" value="授权公司" class="text-gray-200" />
                <div class="mt-2 space-y-2 max-h-48 overflow-y-auto bg-gray-700 p-3 rounded-md">
                  <label v-for="company in companies" :key="company.company_code" class="flex items-center">
                    <input
                      type="checkbox"
                      :value="company.company_code"
                      v-model="form.company_codes"
                      class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                    >
                    <span class="ml-2 text-sm text-gray-300">{{ company.company_name }}</span>
                  </label>
                </div>
                <InputError class="mt-2" :message="form.errors.company_codes" />
              </div>

              <div>
                <InputLabel for="status" value="在职状态" class="text-gray-200" />
                <SelectInput
                  id="status"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600"
                  v-model="form.status"
                >
                  <option v-for="option in statusOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </option>
                </SelectInput>
                <InputError class="mt-2" :message="form.errors.status" />
              </div>

              <div>
                <InputLabel for="emergency_contact" value="紧急联系人" class="text-gray-200" />
                <TextInput
                  id="emergency_contact"
                  type="text"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600"
                  v-model="form.emergency_contact"
                />
                <InputError class="mt-2" :message="form.errors.emergency_contact" />
              </div>

              <div>
                <InputLabel for="emergency_phone" value="紧急联系电话" class="text-gray-200" />
                <TextInput
                  id="emergency_phone"
                  type="text"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600"
                  v-model="form.emergency_phone"
                />
                <InputError class="mt-2" :message="form.errors.emergency_phone" />
              </div>

              <div>
                <InputLabel for="notes" value="备注" class="text-gray-200" />
                <textarea
                  id="notes"
                  v-model="form.notes"
                  class="mt-1 block w-full bg-gray-700 text-white border-gray-600 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                  rows="3"
                ></textarea>
                <InputError class="mt-2" :message="form.errors.notes" />
              </div>
            </div>
          </div>

          <!-- 角色选择 -->
          <div class="mt-8">
            <h3 class="text-lg font-medium text-gray-100 border-b border-gray-700 pb-2 mb-4">角色分配</h3>

            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              <div v-for="role in roles" :key="role.id" class="flex items-center space-x-2 p-3 bg-gray-700 rounded-lg">
                <Checkbox
                  :id="`role-${role.id}`"
                  :value="String(role.id)"
                  v-model:checked="form.roles"
                  class="focus:ring-indigo-500 text-indigo-600 border-gray-600"
                />
                <div>
                  <label :for="`role-${role.id}`" class="text-sm text-gray-200 cursor-pointer">
                    {{ role.display_name }}
                  </label>
                  <p class="text-xs text-gray-400">{{ role.name }}</p>
                </div>
              </div>
            </div>

            <InputError class="mt-2" :message="form.errors.roles" />
          </div>

          <!-- 表单按钮 -->
          <div class="flex justify-end">
            <div class="flex space-x-2">
              <Link :href="route('settings.users')" class="px-4 py-2 bg-gray-700 text-gray-200 rounded hover:bg-gray-600 transition-colors">
                取消
              </Link>
              <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing" class="bg-blue-600 hover:bg-blue-700">
                保存更改
              </PrimaryButton>
            </div>
          </div>
        </form>
      </div>
    </div>
  </AppLayout>
</template>