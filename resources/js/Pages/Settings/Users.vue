<script setup lang="ts">
import UsersIndex from '@/Pages/Settings/Users/<USER>';

interface Props {
  users: {
    data: any[];
    links: any[];
    [key: string]: any;
  };
  filters: {
    search?: string;
    role?: string;
    status?: string;
    [key: string]: any;
  };
}

// 定义接收的属性
const props = defineProps<Props>();
</script>

<template>
  <UsersIndex :users="users" :filters="filters" />
</template> 