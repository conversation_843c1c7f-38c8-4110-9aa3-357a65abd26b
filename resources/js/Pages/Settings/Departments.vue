<script setup lang="ts">
import DepartmentsIndex from '@/Pages/Settings/Departments/Index.vue';

interface Props {
  departments: {
    data: any[];
    links: any[];
    [key: string]: any;
  };
  filters: {
    search?: string;
    status?: string;
    [key: string]: any;
  };
}

// 定义接收的属性
const props = defineProps<Props>();
</script>

<template>
  <DepartmentsIndex :departments="departments" :filters="filters" />
</template> 