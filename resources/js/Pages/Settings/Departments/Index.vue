<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue';
import { Link, router } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import Pagination from '@/Components/Pagination.vue';
import SearchFilter from '@/Components/SearchFilter.vue';
import debounce from 'lodash/debounce';

interface Manager {
    id: number;
    name: string;
    email?: string;
}

interface Department {
    id: number;
    name: string;
    parent_id?: number | null;
    description?: string;
    status: string;
    order?: number;
    parent?: Department;
    managers?: Manager[];
    children?: Department[];
    level?: number;
}

interface Filters {
    search?: string;
    status?: string;
    [key: string]: any;
}

interface PaginatedDepartments {
    data: Department[];
    links: any[];
    [key: string]: any;
}

interface Props {
    departments: PaginatedDepartments;
    filters: Filters;
}

interface StatusOption {
    value: string;
    label: string;
}

interface StatusClasses {
    [key: string]: string;
}

const props = defineProps<Props>();

// 添加调试信息
console.log('接收到的部门数据:', props.departments);

const search = ref(props.filters?.search || '');
const status = ref(props.filters?.status || '');

// 将部门数据转换为树状结构
const departmentTree = computed<Department[]>(() => {
    if (!props.departments?.data) return [];
    
    const departments = [...props.departments.data];
    const departmentMap: Record<number, Department> = {};
    const result: Department[] = [];
    
    // 首先创建一个映射表
    departments.forEach(dept => {
        // 克隆部门对象并添加 children 数组
        departmentMap[dept.id] = { ...dept, children: [], level: 0 };
    });
    
    // 构建树状结构
    departments.forEach(dept => {
        const currentDept = departmentMap[dept.id];
        
        if (dept.parent_id && departmentMap[dept.parent_id]) {
            // 如果有父部门，将当前部门添加到父部门的 children 中
            const parentDept = departmentMap[dept.parent_id];
            currentDept.level = (parentDept.level || 0) + 1;
            parentDept.children?.push(currentDept);
        } else {
            // 如果没有父部门，则为顶级部门
            result.push(currentDept);
        }
    });
    
    return result;
});

// 展平树状结构用于展示
const flattenedDepartments = computed<Department[]>(() => {
    const result: Department[] = [];
    
    function flatten(departments: Department[], result: Department[]): void {
        departments.forEach(dept => {
            result.push(dept);
            if (dept.children && dept.children.length > 0) {
                flatten(dept.children, result);
            }
        });
    }
    
    flatten(departmentTree.value, result);
    return result;
});

const statusOptions: StatusOption[] = [
    { value: '正常', label: '正常' },
    { value: '停用', label: '停用' },
];

// 优化搜索函数
const debouncedSearch = debounce(() => {
    router.get(
        route('settings.departments'),
        { search: search.value, status: status.value },
        { preserveState: true, preserveScroll: true }
    );
}, 300);

watch([search, status], () => {
    debouncedSearch();
});

// 删除确认函数
function confirmDelete(department: Department): void {
    if (confirm(`确定要删除部门"${department.name}"吗？此操作无法撤销。`)) {
        router.delete(route('settings.departments.destroy', department.id), {
            onSuccess: () => {
                // 可以添加成功提示
                console.log('部门删除成功');
            },
            onError: (errors) => {
                // 处理错误
                console.error('删除部门时出错:', errors);
                alert('删除部门失败，请重试');
            }
        });
    }
}

const statusClasses: StatusClasses = {
    '正常': 'bg-green-100 text-green-800',
    '停用': 'bg-red-100 text-red-800',
};

// 获取缩进样式
function getIndentStyle(level: number = 0): { paddingLeft: string } {
    return {
        paddingLeft: `${level * 20}px`
    };
}

// 声明route函数类型
declare function route(name: string, params?: any): string;
</script>

<template>
    <AppLayout title="部门管理">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">部门管理</h2>
                <Link :href="route('settings.departments.create')" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors font-medium flex items-center">
                    <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    添加部门
                </Link>
            </div>

            <!-- 搜索和过滤 - 可选，可以根据需要保留或移除 -->
            <div class="flex flex-wrap items-center justify-between mb-6" v-if="false">
                <div class="w-full md:w-auto flex flex-wrap mb-4 md:mb-0">
                    <SearchFilter
                        v-model="search"
                        class="w-full md:w-auto mr-4 mb-4 md:mb-0"
                        placeholder="搜索部门名称、代码、描述..."
                    />
                    <select
                        v-model="status"
                        class="border-gray-700 bg-gray-800 text-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                    >
                        <option value="">所有状态</option>
                        <option v-for="option in statusOptions" :key="option.value" :value="option.value">
                            {{ option.label }}
                        </option>
                    </select>
                </div>
            </div>

            <!-- 部门表格 -->
            <div class="overflow-x-auto">
                <table class="min-w-full bg-gray-800 rounded-md">
                    <thead class="bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                部门名称
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                上级部门
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                部门主管
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                状态
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-700">
                        <tr v-for="department in flattenedDepartments" :key="department.id" class="bg-gray-800">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-white" :style="getIndentStyle(department.level)">
                                    <span v-if="department.level && department.level > 0" class="text-gray-500 mr-2">└─</span>
                                    {{ department.name }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-300">
                                    {{ department.parent ? department.parent.name : '无' }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-300">
                                    <template v-if="department.managers && department.managers.length > 0">
                                        <span v-for="(manager, index) in department.managers" :key="manager.id">
                                            {{ manager.name }}
                                            <span v-if="index < department.managers.length - 1" class="text-gray-500 mx-1">|</span>
                                        </span>
                                    </template>
                                    <template v-else>
                                        <span class="text-gray-500">未指定</span>
                                    </template>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                                      :class="department.status === '正常' ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'">
                                    {{ department.status }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <Link :href="route('settings.departments.show', department.id)" class="px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                    查看
                                </Link>
                                <Link :href="route('settings.departments.edit', department.id)" class="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                                    编辑
                                </Link>
                                <button @click="confirmDelete(department)" class="px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                                    删除
                                </button>
                            </td>
                        </tr>
                        <tr v-if="!flattenedDepartments.length">
                            <td colspan="5" class="px-6 py-4 text-center text-gray-400">
                                暂无部门数据
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="mt-6">
                <Pagination :links="departments?.links || []" />
            </div>
        </div>
    </AppLayout>
</template>

<style>
/* 分页样式调整，使其适应深色主题 */
.pagination-link {
    @apply bg-gray-800 text-gray-300 border-gray-700;
}
.pagination-link.active {
    @apply bg-blue-600 text-white border-blue-700;
}
</style>