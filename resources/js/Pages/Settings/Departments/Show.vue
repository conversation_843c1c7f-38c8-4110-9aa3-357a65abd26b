<script setup lang="ts">
import { Link } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import Pagination from '@/Components/Pagination.vue';

interface Manager {
    id: number;
    name: string;
    email: string;
    phone?: string;
}

interface DepartmentChild {
    id: number;
    name: string;
}

interface Department {
    id: number;
    name: string;
    parent_id?: number | null;
    description?: string;
    status: string;
    order: number | string;
    created_at: string;
    parent?: {
        id: number;
        name: string;
    };
    managers?: Manager[];
    children?: DepartmentChild[];
}

interface User {
    id: number;
    name: string;
    employee_id?: string;
    position?: string;
    email: string;
    phone?: string;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginatedUsers {
    data: User[];
    links: PaginationLink[];
    [key: string]: any;
}

interface Props {
    department: Department;
    users: PaginatedUsers;
}

const props = defineProps<Props>();

interface StatusClasses {
    [key: string]: string;
}

const statusClasses: StatusClasses = {
    '正常': 'bg-green-900 text-green-300',
    '停用': 'bg-red-900 text-red-300',
};

// 声明route函数类型
declare function route(name: string, params?: any): string;
</script>

<template>
    <AppLayout :title="`部门详情 - ${department.name}`">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">部门详情 - {{ department.name }}</h2>
                <div class="flex space-x-4">
                    <Link :href="route('settings.departments.edit', department.id)" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                        编辑
                    </Link>
                    <Link :href="route('settings.departments')" class="px-4 py-2 bg-gray-700 text-gray-200 rounded hover:bg-gray-600 transition-colors">
                        返回列表
                    </Link>
                </div>
            </div>

            <!-- 部门基本信息 -->
            <div class="bg-gray-800 overflow-hidden rounded-lg mb-8">
                <div class="p-6">
                    <h3 class="text-lg font-semibold mb-4 text-white">基本信息</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <p class="text-sm text-gray-400">部门名称</p>
                            <p class="text-base text-white">{{ department.name }}</p>
                        </div>

                        <div>
                            <p class="text-sm text-gray-400">上级部门</p>
                            <p class="text-base text-white">{{ department.parent ? department.parent.name : '无' }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-400">状态</p>
                            <span :class="['px-2 py-1 text-xs font-semibold rounded-full', statusClasses[department.status]]">
                                {{ department.status }}
                            </span>
                        </div>
                        <div>
                            <p class="text-sm text-gray-400">排序顺序</p>
                            <p class="text-base text-white">{{ department.order }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-400">创建时间</p>
                            <p class="text-base text-white">{{ new Date(department.created_at).toLocaleString() }}</p>
                        </div>
                    </div>

                    <div class="mt-6">
                        <p class="text-sm text-gray-400">部门描述</p>
                        <p class="text-base text-white whitespace-pre-line">{{ department.description || '无' }}</p>
                    </div>
                </div>
            </div>

            <!-- 部门主管信息 -->
            <div v-if="department.managers && department.managers.length > 0" class="bg-gray-800 overflow-hidden rounded-lg mb-8">
                <div class="p-6">
                    <h3 class="text-lg font-semibold mb-4 text-white">部门主管</h3>
                    <div class="grid grid-cols-1 gap-6">
                        <div v-for="manager in department.managers" :key="manager.id" class="bg-gray-700 p-4 rounded-lg">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <p class="text-sm text-gray-400">姓名</p>
                                    <p class="text-base text-white">{{ manager.name }}</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-400">邮箱</p>
                                    <p class="text-base text-white">{{ manager.email }}</p>
                                </div>
                                <div v-if="manager.phone">
                                    <p class="text-sm text-gray-400">电话</p>
                                    <p class="text-base text-white">{{ manager.phone }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 子部门列表 -->
            <div v-if="department.children && department.children.length > 0" class="bg-gray-800 overflow-hidden rounded-lg mb-8">
                <div class="p-6">
                    <h3 class="text-lg font-semibold mb-4 text-white">子部门</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div v-for="child in department.children" :key="child.id" class="p-4 border border-gray-700 rounded-lg">
                            <Link :href="route('settings.departments.show', child.id)" class="text-blue-400 hover:text-blue-300">
                                {{ child.name }}
                            </Link>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 部门人员列表 -->
            <div class="bg-gray-800 overflow-hidden rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold mb-4 text-white">部门人员</h3>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-gray-800 rounded-md">
                            <thead class="bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                        姓名
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                        工号
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                        职位
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                        邮箱
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                        电话
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                        操作
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-gray-800 divide-y divide-gray-700">
                                <tr v-for="user in users?.data || []" :key="user.id" class="bg-gray-800">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                                        {{ user.name }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        {{ user.employee_id }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        {{ user.position || '未设置' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        {{ user.email }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        {{ user.phone || '未设置' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <Link :href="route('settings.users.show', user.id)" class="text-blue-400 hover:text-blue-300">
                                            查看
                                        </Link>
                                    </td>
                                </tr>
                                <tr v-if="!users?.data || users.data.length === 0">
                                    <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-400">
                                        暂无人员数据
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-6">
                        <Pagination :links="users?.links || []" />
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style>
/* 分页样式调整，使其适应深色主题 */
.pagination-link {
    background-color: #1f2937; /* bg-gray-800 */
    color: #d1d5db;            /* text-gray-300 */
    border-color: #374151;     /* border-gray-700 */
}
.pagination-link.active {
    background-color: #2563eb; /* bg-blue-600 */
    color: #fff;               /* text-white */
    border-color: #1d4ed8;     /* border-blue-700 */
}
</style>