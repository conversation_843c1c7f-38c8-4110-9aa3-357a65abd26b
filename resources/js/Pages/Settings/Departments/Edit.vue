<script setup lang="ts">
import { useForm } from '@inertiajs/vue3';
import { Link } from '@inertiajs/vue3';
import { ref, watch } from 'vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';

interface Department {
    id: number;
    name: string;
    parent_id?: number | null;
    description?: string;
    status: string;
    order: string | number;
    parent?: {
        id: number;
        name: string;
    };
    managers?: Array<{
        id: number;
        name: string;
    }>;
}

interface Manager {
    id: number;
    name: string;
    [key: string]: any;
}

interface Props {
    department: Department;
    departments: Department[];
    managers: Manager[];
    managerIds: number[];
}

interface StatusOption {
    value: string;
    label: string;
}

interface DepartmentForm {
    name: string;
    parent_id: number | string;
    manager_ids: number[];
    description: string;
    status: string;
    order: string;
}

const props = defineProps<Props>();

const selectedManagers = ref<number[]>(props.managerIds || []);

const form = useForm<DepartmentForm>({
    name: props.department.name,
    parent_id: props.department.parent_id || '',
    manager_ids: selectedManagers.value,
    description: props.department.description || '',
    status: props.department.status,
    order: String(props.department.order),
});

// 监听selectedManagers变化，同步到form.manager_ids
watch(selectedManagers, (newValue) => {
    form.manager_ids = newValue;
}, { deep: true });

function submit(): void {
    form.put(route('settings.departments.update', props.department.id), {
        preserveScroll: true,
    });
}

const statusOptions: StatusOption[] = [
    { value: '正常', label: '正常' },
    { value: '停用', label: '停用' },
];

// 声明route函数类型
declare function route(name: string, params?: any): string;
</script>

<template>
    <AppLayout title="编辑部门">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">编辑部门 - {{ department.name }}</h2>
                <Link :href="route('settings.departments')" class="px-4 py-2 bg-gray-700 text-gray-200 rounded hover:bg-gray-600 transition-colors">
                    返回列表
                </Link>
            </div>

            <div class="bg-gray-800 overflow-hidden rounded-lg">
                <div class="p-6">
                    <form @submit.prevent="submit">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 部门名称 -->
                            <div>
                                <InputLabel for="name" value="部门名称" class="text-gray-300" />
                                <TextInput
                                    id="name"
                                    v-model="form.name"
                                    type="text"
                                    class="mt-1 block w-full bg-gray-700 border-gray-600 text-white focus:border-blue-500 focus:ring-blue-500"
                                    required
                                    autofocus
                                />
                                <InputError class="mt-2" :message="form.errors.name" />
                            </div>

                            <!-- 上级部门 -->
                            <div>
                                <InputLabel for="parent_id" value="上级部门" class="text-gray-300" />
                                <select
                                    id="parent_id"
                                    v-model="form.parent_id"
                                    class="mt-1 block w-full border-gray-600 bg-gray-700 text-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                >
                                    <option value="">无上级部门</option>
                                    <option v-for="dept in departments" :key="dept.id" :value="dept.id">
                                        {{ dept.name }}
                                    </option>
                                </select>
                                <InputError class="mt-2" :message="form.errors.parent_id" />
                            </div>

                            <!-- 部门主管（多选） -->
                            <div class="md:col-span-2">
                                <InputLabel value="部门主管（可多选）" class="text-gray-300" />
                                <div class="mt-2 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-60 overflow-y-auto p-2 border border-gray-600 rounded-md">
                                    <div v-for="manager in managers" :key="manager.id" class="flex items-center">
                                        <input
                                            type="checkbox"
                                            :id="`manager-${manager.id}`"
                                            :value="manager.id"
                                            v-model="selectedManagers"
                                            class="rounded border-gray-600 text-blue-600 shadow-sm focus:ring-blue-500"
                                        />
                                        <label :for="`manager-${manager.id}`" class="ml-2 text-sm text-gray-300">
                                            {{ manager.name }}
                                        </label>
                                    </div>
                                </div>
                                <InputError class="mt-2" :message="form.errors['manager_ids']" />
                            </div>

                            <!-- 部门状态 -->
                            <div>
                                <InputLabel for="status" value="部门状态" class="text-gray-300" />
                                <select
                                    id="status"
                                    v-model="form.status"
                                    class="mt-1 block w-full border-gray-600 bg-gray-700 text-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                >
                                    <option v-for="option in statusOptions" :key="option.value" :value="option.value">
                                        {{ option.label }}
                                    </option>
                                </select>
                                <InputError class="mt-2" :message="form.errors.status" />
                            </div>

                            <!-- 排序顺序 -->
                            <div>
                                <InputLabel for="order" value="排序顺序" class="text-gray-300" />
                                <TextInput
                                    id="order"
                                    v-model="form.order"
                                    type="number"
                                    class="mt-1 block w-full bg-gray-700 border-gray-600 text-white focus:border-blue-500 focus:ring-blue-500"
                                />
                                <InputError class="mt-2" :message="form.errors.order" />
                            </div>
                        </div>

                        <!-- 部门描述 -->
                        <div class="mt-6">
                            <InputLabel for="description" value="部门描述" class="text-gray-300" />
                            <textarea
                                id="description"
                                v-model="form.description"
                                class="mt-1 block w-full border-gray-600 bg-gray-700 text-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                rows="3"
                            ></textarea>
                            <InputError class="mt-2" :message="form.errors.description" />
                        </div>

                        <!-- 表单按钮 -->
                        <div class="flex items-center justify-end mt-8 space-x-4">
                            <Link :href="route('settings.departments')" class="px-4 py-2 bg-gray-700 text-gray-200 rounded hover:bg-gray-600 transition-colors">
                                取消
                            </Link>

                            <button
                                type="submit"
                                class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                                :class="{ 'opacity-50': form.processing }"
                                :disabled="form.processing"
                            >
                                保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template>