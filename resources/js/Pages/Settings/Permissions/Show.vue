<script setup lang="ts">
import { Link } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

interface Role {
    id: number;
    name: string;
    display_name: string;
    description?: string;
}

interface Permission {
    id: number;
    name: string;
    description?: string;
    created_at?: string;
    updated_at?: string;
    roles?: Role[];
}

interface Props {
    permission: Permission;
}

const props = defineProps<Props>();

// 声明route函数类型
declare function route(name: string, params?: any): string;
</script>

<template>
    <AppLayout title="查看权限">
        <template #default>
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">查看权限</h2>
                    <div class="flex space-x-2">
                        <Link :href="route('settings.permissions.edit', permission?.id)" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                            编辑
                        </Link>
                        <Link :href="route('settings.permissions')" class="px-4 py-2 bg-gray-700 text-gray-200 rounded hover:bg-gray-600 transition-colors">
                            返回
                        </Link>
                    </div>
                </div>

                <!-- 权限基本信息 -->
                <div class="bg-gray-800 overflow-hidden rounded-lg shadow-lg mb-8">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4 text-gray-100">基本信息</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <p class="text-sm text-gray-400">权限名称</p>
                                <p class="text-base text-gray-100">{{ permission?.name }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-400">创建时间</p>
                                <p class="text-base text-gray-100">{{ permission?.created_at ? new Date(permission.created_at).toLocaleString() : '' }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-400">最后更新</p>
                                <p class="text-base text-gray-100">{{ permission?.updated_at ? new Date(permission.updated_at).toLocaleString() : '' }}</p>
                            </div>
                        </div>

                        <div class="mt-6">
                            <p class="text-sm text-gray-400">权限描述</p>
                            <p class="text-base text-gray-100 whitespace-pre-line">{{ permission?.description || '无' }}</p>
                        </div>
                    </div>
                </div>

                <!-- 关联角色列表 -->
                <div class="bg-gray-800 overflow-hidden rounded-lg shadow-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4 text-gray-100">关联角色</h3>
                        
                        <div v-if="permission?.roles && permission.roles.length > 0" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div v-for="role in permission.roles" :key="role.id" class="p-4 border border-gray-700 bg-gray-700 rounded-lg">
                                <Link :href="route('settings.roles.show', role.id)" class="text-blue-400 hover:text-blue-300 font-medium">
                                    {{ role.display_name }}
                                </Link>
                                <p class="text-xs text-gray-400 mt-1">{{ role.name }}</p>
                                <p v-if="role.description" class="text-xs text-gray-500 mt-1 line-clamp-2">
                                    {{ role.description }}
                                </p>
                            </div>
                        </div>
                        <div v-else class="py-4 text-center text-gray-400">
                            该权限没有关联任何角色
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </AppLayout>
</template>