<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { Link, router, usePage } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import Pagination from '@/Components/Pagination.vue';
import SearchFilter from '@/Components/SearchFilter.vue';
import debounce from 'lodash/debounce';

interface Permission {
    id: number;
    name: string;
    description?: string;
    roles_count: number;
    created_at?: string;
    updated_at?: string;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginatedPermissions {
    data: Permission[];
    links: PaginationLink[];
    [key: string]: any;
}

interface Filters {
    search?: string;
    [key: string]: any;
}

interface Props {
    permissions: PaginatedPermissions;
    filters: Filters;
}

const props = defineProps<Props>();

const search = ref(props.filters?.search || '');

// 确保页面权限数据不会丢失
onMounted(() => {
    // 尝试从会话存储中恢复权限数据
    try {
        const savedPermissions = sessionStorage.getItem('userPermissions');
        if (savedPermissions) {
            console.log('从会话存储中恢复权限数据');
            // 不需要手动设置，因为AppLayout组件会处理
        }
    } catch (e) {
        console.error('恢复权限数据失败:', e);
    }
});

const debouncedSearch = debounce(() => {
    router.get(
        route('settings.permissions'),
        { search: search.value },
        { preserveState: true, preserveScroll: true }
    );
}, 300);

watch(search, () => {
    debouncedSearch();
});

function confirmDelete(permission: Permission): void {
    if (confirm(`确定要删除权限"${permission.name}"吗？此操作无法撤销。`)) {
        router.delete(route('settings.permissions.destroy', permission.id));
    }
}

// 声明route函数类型
declare function route(name: string, params?: any): string;
</script>

<template>
    <AppLayout title="权限管理">
        <template #default>
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">权限管理</h2>
                    <Link :href="route('settings.permissions.create')" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors font-medium flex items-center">
                        <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        添加权限
                    </Link>
                </div>

                <div class="bg-gray-800 overflow-hidden rounded-lg shadow-lg">
                    <div class="p-6">
                        <!-- 搜索和过滤 -->
                        <div class="flex flex-wrap items-center justify-between mb-6">
                            <div class="w-full md:w-auto flex flex-wrap mb-4 md:mb-0">
                                <SearchFilter
                                    v-model="search"
                                    class="w-full md:w-auto mr-4 mb-4 md:mb-0"
                                    placeholder="搜索权限名称、描述..."
                                />
                            </div>
                        </div>

                        <!-- 权限表格 -->
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-700">
                                <thead class="bg-gray-700">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                            权限名称
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                            角色数量
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                            描述
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                            操作
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-gray-800 divide-y divide-gray-700">
                                    <tr v-for="permission in permissions?.data || []" :key="permission.id">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-100">
                                                {{ permission.name }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-300">
                                                <span class="px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-900 text-blue-200">
                                                    {{ permission.roles_count }}
                                                </span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-300 truncate max-w-xs">
                                                {{ permission.description || '无' }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <Link :href="route('settings.permissions.show', permission.id)" class="px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                                查看
                                            </Link>
                                            <Link :href="route('settings.permissions.edit', permission.id)" class="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                                                编辑
                                            </Link>
                                            <button @click="confirmDelete(permission)" class="px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                                                删除
                                            </button>
                                        </td>
                                    </tr>
                                    <tr v-if="permissions?.data?.length === 0">
                                        <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-400">
                                            暂无权限数据
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="mt-6">
                            <Pagination :links="permissions?.links || []" />
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </AppLayout>
</template> 