<script setup lang="ts">
import { useForm } from '@inertiajs/vue3';
import { Link } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import Checkbox from '@/Components/Checkbox.vue';

interface Role {
    id: number;
    name: string;
    display_name: string;
    description?: string;
}

interface PermissionForm {
    name: string;
    description: string;
    roles: string[];
}

interface Props {
    roles: Role[];
}

const props = defineProps<Props>();

const form = useForm<PermissionForm>({
    name: '',
    description: '',
    roles: [],
});

const submit = (): void => {
    form.post(route('settings.permissions.store'), {
        onSuccess: () => {
            form.reset();
        },
    });
};

const searchTerm = ref('');
const filteredRoles = computed(() => {
    if (!searchTerm.value) return props.roles;
    
    const search = searchTerm.value.toLowerCase();
    return props.roles.filter(role => 
        role.name.toLowerCase().includes(search) || 
        role.display_name.toLowerCase().includes(search)
    );
});

function toggleAllRoles(): void {
    if (form.roles.length === props.roles.length) {
        form.roles = [];
    } else {
        form.roles = props.roles.map(r => r.id.toString());
    }
}

// 声明route函数类型
declare function route(name: string, params?: any): string;
</script>

<template>
    <AppLayout title="创建权限">
        <template #default>
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">创建新权限</h2>
                    <Link :href="route('settings.permissions')" class="px-4 py-2 bg-gray-700 text-gray-200 rounded hover:bg-gray-600 transition-colors">
                        返回
                    </Link>
                </div>

                <div class="bg-gray-800 overflow-hidden rounded-lg shadow-lg">
                    <div class="p-6">
                        <form @submit.prevent="submit">
                            <!-- 权限名称 -->
                            <div>
                                <InputLabel for="name" value="权限名称" class="text-gray-200" />
                                <TextInput
                                    id="name"
                                    v-model="form.name"
                                    type="text"
                                    class="mt-1 block w-full bg-gray-700 text-white border-gray-600"
                                    required
                                    autofocus
                                />
                                <p class="text-xs text-gray-400 mt-1">
                                    唯一标识，如 users.create, posts.edit
                                </p>
                                <InputError class="mt-2" :message="form.errors.name" />
                            </div>

                            <!-- 权限描述 -->
                            <div class="mt-6">
                                <InputLabel for="description" value="权限描述" class="text-gray-200" />
                                <textarea
                                    id="description"
                                    v-model="form.description"
                                    class="mt-1 block w-full bg-gray-700 text-white border-gray-600 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    rows="3"
                                ></textarea>
                                <InputError class="mt-2" :message="form.errors.description" />
                            </div>

                            <!-- 角色选择 -->
                            <div class="mt-6">
                                <div class="flex justify-between items-center mb-2">
                                    <InputLabel value="关联角色" class="text-gray-200" />
                                    <div class="flex space-x-4 items-center">
                                        <button 
                                            type="button" 
                                            @click="toggleAllRoles"
                                            class="text-xs text-blue-400 hover:text-blue-300 hover:underline"
                                        >
                                            {{ form.roles.length === roles.length ? '取消全选' : '全选' }}
                                        </button>
                                        <div class="relative">
                                            <TextInput
                                                v-model="searchTerm"
                                                type="text"
                                                class="text-sm bg-gray-700 text-white border-gray-600"
                                                placeholder="搜索角色..."
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-2 bg-gray-700 rounded-md p-4 max-h-64 overflow-y-auto">
                                    <div v-if="!filteredRoles.length" class="text-center py-4 text-gray-400">
                                        没有匹配的角色
                                    </div>
                                    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        <div v-for="role in filteredRoles" :key="role.id" class="flex items-start space-x-3 p-3 rounded-md hover:bg-gray-600">
                                            <Checkbox 
                                                :id="`role-${role.id}`" 
                                                :name="`role-${role.id}`" 
                                                v-model:checked="form.roles" 
                                                :value="role.id.toString()"
                                            />
                                            <div>
                                                <label :for="`role-${role.id}`" class="text-sm font-medium text-gray-300 cursor-pointer">
                                                    {{ role.display_name }}
                                                </label>
                                                <p class="text-xs text-gray-400">{{ role.name }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <InputError class="mt-2" :message="form.errors.roles" />
                            </div>

                            <!-- 表单按钮 -->
                            <div class="flex items-center justify-end mt-8 space-x-4">
                                <Link :href="route('settings.permissions')" class="px-4 py-2 bg-gray-700 text-gray-200 rounded hover:bg-gray-600 transition-colors">
                                    取消
                                </Link>

                                <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing" class="bg-blue-600 hover:bg-blue-700">
                                    保存
                                </PrimaryButton>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </template>
    </AppLayout>
</template> 