<script setup lang="ts">
import { useForm } from '@inertiajs/vue3';
import { Link } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import AppLayout from '@/Layouts/AppLayout.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import Checkbox from '@/Components/Checkbox.vue';

interface Permission {
    id: number;
    name: string;
    description?: string;
}

interface CategoryChild {
    name: string;
    permissions: Permission[];
}

interface PermissionCategory {
    name: string;
    children: Record<string, CategoryChild>;
    expanded: boolean;
    permissions: Permission[];
}

interface SearchResultCategory {
    name: string;
    permissions: Permission[];
}

type TreeCategory = PermissionCategory | SearchResultCategory;

// 添加条目类型定义
type PermissionEntry = [string, TreeCategory];

interface RoleForm {
    name: string;
    description: string;
    permissions: string[];
}

interface Props {
    permissions: Permission[];
}

const props = defineProps<Props>();

const form = useForm<RoleForm>({
    name: '',
    description: '',
    permissions: [],
});

const submit = (): void => {
    form.post(route('settings.roles.store'), {
        onSuccess: () => {
            form.reset();
        },
    });
};

const searchTerm = ref<string>('');
const filteredPermissions = computed(() => {
    if (!searchTerm.value) return props.permissions;
    
    const search = searchTerm.value.toLowerCase();
    return props.permissions.filter(permission => 
        permission.name.toLowerCase().includes(search) || 
        permission.description?.toLowerCase().includes(search)
    );
});

// 分组权限为树形结构
const permissionTree = computed<Record<string, TreeCategory>>(() => {
    const tree: Record<string, PermissionCategory> = {};
    
    if (searchTerm.value) {
        // 如果有搜索条件，则不分组，直接按搜索结果显示
        return {
            '搜索结果': {
                name: '搜索结果',
                permissions: filteredPermissions.value
            }
        };
    }
    
    // 将权限按前缀分组
    filteredPermissions.value.forEach(permission => {
        const nameParts = permission.name.split('.');
        const prefix = nameParts[0];  // 第一级分类，如dashboard, products, sales等
        
        if (!tree[prefix]) {
            tree[prefix] = {
                name: getDisplayName(prefix),
                children: {},
                expanded: true,
                permissions: []
            };
        }
        
        if (nameParts.length > 1) {
            // 如果有二级分类，如settings.roles, settings.permissions
            const subPrefix = nameParts[1];
            if (!tree[prefix].children[subPrefix]) {
                tree[prefix].children[subPrefix] = {
                    name: getDisplayName(subPrefix),
                    permissions: []
                };
            }
            tree[prefix].children[subPrefix].permissions.push(permission);
        } else {
            // 如果只有一级分类
            tree[prefix].permissions.push(permission);
        }
    });
    
    return tree;
});

// 创建一个计算属性，用于模板循环，解决类型问题
const permissionEntries = computed<PermissionEntry[]>(() => {
    return Object.entries(permissionTree.value);
});

// 计算类别是否为权限类别（带子节点的类别）
const categoryList = computed(() => {
    return permissionEntries.value
        .filter(([key, value]) => key !== '搜索结果' && isPermissionCategory(value))
        .map(([key, value]) => ({
            key,
            category: value as PermissionCategory
        }));
});

// 为分类提供友好的显示名称
function getDisplayName(prefix: string): string {
    const displayNames: Record<string, string> = {
        'dashboard': '控制台',
        'products': '产品管理',
        'sales': '销售管理',
        'purchases': '采购管理',
        'warehouse': '仓库管理',
        'logistics': '物流管理',
        'production': '生产管理',
        'QC': '质量管理',
        'finance': '财务管理',
        'reports': 'BI报表',
        'settings': '系统设置',
        'roles': '角色管理',
        'permissions': '权限管理',
        'users': '用户管理',
        'departments': '部门管理',
        'view': '查看',
        'create': '创建',
        'edit': '编辑',
        'delete': '删除',
        'access': '访问',
        'index': '列表',
        'show': '详情'
    };
    
    return displayNames[prefix] || prefix.charAt(0).toUpperCase() + prefix.slice(1);
}

// 检测对象是否为PermissionCategory类型
function isPermissionCategory(category: TreeCategory): category is PermissionCategory {
    return category && 'children' in category && 'expanded' in category;
}

// 展开/折叠分类
const toggleCategory = (category: PermissionCategory): void => {
    category.expanded = !category.expanded;
};

// 检查分类中是否所有权限都被选中
const isCategoryAllSelected = (permissions: Permission[]): boolean => {
    return permissions.every(permission => form.permissions.includes(permission.id.toString()));
};

// 检查分类中是否有部分权限被选中
const isCategoryPartiallySelected = (permissions: Permission[]): boolean => {
    return permissions.some(permission => form.permissions.includes(permission.id.toString())) && 
           !permissions.every(permission => form.permissions.includes(permission.id.toString()));
};

// 选择/取消选择整个分类的所有权限
const toggleCategoryPermissions = (permissions: Permission[]): void => {
    if (isCategoryAllSelected(permissions)) {
        // 如果全选了，则取消全选
        permissions.forEach(permission => {
            const index = form.permissions.indexOf(permission.id.toString());
            if (index !== -1) {
                form.permissions.splice(index, 1);
            }
        });
    } else {
        // 否则全选
        permissions.forEach(permission => {
            if (!form.permissions.includes(permission.id.toString())) {
                form.permissions.push(permission.id.toString());
            }
        });
    }
};

function toggleAllPermissions(): void {
    if (form.permissions.length === props.permissions.length) {
        form.permissions = [];
    } else {
        form.permissions = props.permissions.map(p => p.id.toString());
    }
}

// 声明route函数类型
declare function route(name: string, params?: any): string;
</script>

<template>
    <AppLayout title="添加角色">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">添加角色</h2>
                <Link :href="route('settings.roles')" class="px-4 py-2 bg-gray-700 text-gray-200 rounded hover:bg-gray-600 transition-colors">
                    返回列表
                </Link>
            </div>

            <div class="bg-gray-800 overflow-hidden rounded-lg">
                <div class="p-6">
                    <form @submit.prevent="submit">
                        <!-- 角色名称 -->
                        <div>
                            <InputLabel for="name" value="角色名称" class="text-gray-300" />
                            <TextInput
                                id="name"
                                v-model="form.name"
                                type="text"
                                class="mt-1 block w-full bg-gray-700 border-gray-600 text-white focus:border-blue-500 focus:ring-blue-500"
                                required
                                autofocus
                            />
                            <p class="text-xs text-gray-400 mt-1">
                                系统中显示的角色名称，如：系统管理员、销售经理、仓库管理员
                            </p>
                            <InputError class="mt-2" :message="form.errors.name" />
                        </div>

                        <!-- 角色描述 -->
                        <div class="mt-6">
                            <InputLabel for="description" value="角色描述" class="text-gray-300" />
                            <textarea
                                id="description"
                                v-model="form.description"
                                class="mt-1 block w-full border-gray-600 bg-gray-700 text-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md shadow-sm"
                                rows="3"
                            ></textarea>
                            <InputError class="mt-2" :message="form.errors.description" />
                        </div>

                        <!-- 权限选择 -->
                        <div class="mt-6">
                            <div class="flex justify-between items-center mb-2">
                                <InputLabel value="角色权限" class="text-gray-300" />
                                <div class="flex space-x-4 items-center">
                                    <button 
                                        type="button" 
                                        @click="toggleAllPermissions"
                                        class="text-xs text-blue-400 hover:text-blue-300 hover:underline"
                                    >
                                        {{ form.permissions.length === permissions.length ? '取消全选' : '全选' }}
                                    </button>
                                    <div class="relative">
                                        <TextInput
                                            v-model="searchTerm"
                                            type="text"
                                            class="text-sm bg-gray-700 border-gray-600 text-white focus:border-blue-500 focus:ring-blue-500"
                                            placeholder="搜索权限..."
                                        />
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 树形结构的权限列表 -->
                            <div class="mt-2 bg-gray-700 rounded-md p-4 overflow-y-auto" style="max-height: calc(100vh - 400px); min-height: 300px;">
                                <div v-if="permissionEntries.length === 0" class="text-center py-4 text-gray-400">
                                    没有匹配的权限
                                </div>
                                <div v-else class="space-y-4">
                                    <!-- 搜索结果 -->
                                    <div v-if="'搜索结果' in permissionTree" class="border border-gray-600 rounded-md overflow-hidden">
                                        <div class="p-3 bg-gray-700">
                                            <span class="text-gray-200 font-medium">搜索结果</span>
                                        </div>
                                        <div class="p-3 divide-y divide-gray-700">
                                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                                <div v-for="permission in permissionTree['搜索结果'].permissions" :key="permission.id" class="p-2 rounded-md border border-gray-600 bg-gray-700">
                                                    <div class="flex items-start">
                                                        <Checkbox 
                                                            :id="`perm-${permission.id}`" 
                                                            v-model:checked="form.permissions" 
                                                            :value="permission.id.toString()"
                                                            class="mt-0.5"
                                                        />
                                                        <label :for="`perm-${permission.id}`" class="ml-2 cursor-pointer">
                                                            <p class="text-sm font-medium text-gray-200">{{ permission.name }}</p>
                                                            <p v-if="permission.description" class="text-xs text-gray-400 mt-1">
                                                                {{ permission.description }}
                                                            </p>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 分类显示 -->
                                    <template v-for="item in categoryList" :key="item.key">
                                        <div class="border border-gray-600 rounded-md overflow-hidden">
                                            <!-- 分类标题 -->
                                            <div 
                                                class="flex items-center justify-between p-3 bg-gray-700 cursor-pointer"
                                                @click="toggleCategory(item.category)"
                                            >
                                                <div class="flex items-center">
                                                    <Checkbox 
                                                        v-if="item.category.permissions.length > 0 || Object.keys(item.category.children).some(key => item.category.children[key].permissions.length > 0)"
                                                        :id="`cat-${item.key}`"
                                                        :indeterminate="
                                                            isCategoryPartiallySelected([
                                                                ...item.category.permissions,
                                                                ...Object.values(item.category.children).flatMap(child => child.permissions)
                                                            ])
                                                        "
                                                        :checked="
                                                            isCategoryAllSelected([
                                                                ...item.category.permissions,
                                                                ...Object.values(item.category.children).flatMap(child => child.permissions)
                                                            ])
                                                        "
                                                        @update:checked="
                                                            toggleCategoryPermissions([
                                                                ...item.category.permissions,
                                                                ...Object.values(item.category.children).flatMap(child => child.permissions)
                                                            ])
                                                        "
                                                        class="mr-2"
                                                    />
                                                    <span class="text-gray-200 font-medium">{{ item.category.name }}</span>
                                                </div>
                                                <svg 
                                                    :class="{ 'transform rotate-180': !item.category.expanded }"
                                                    class="w-5 h-5 text-gray-400 transition-transform" 
                                                    xmlns="http://www.w3.org/2000/svg" 
                                                    viewBox="0 0 20 20" 
                                                    fill="currentColor"
                                                >
                                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            
                                            <!-- 分类内容 -->
                                            <div v-if="item.category.expanded" class="divide-y divide-gray-700">
                                                <!-- 一级分类直接权限 -->
                                                <div v-if="item.category.permissions.length > 0" class="p-3">
                                                    <div class="text-xs text-gray-400 mb-2">{{ item.category.name }}通用权限</div>
                                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                                        <div 
                                                            v-for="permission in item.category.permissions" 
                                                            :key="permission.id" 
                                                            class="p-2 rounded-md border border-gray-600 bg-gray-700"
                                                        >
                                                            <div class="flex items-start">
                                                                <Checkbox 
                                                                    :id="`perm-${permission.id}`" 
                                                                    v-model:checked="form.permissions" 
                                                                    :value="permission.id.toString()"
                                                                    class="mt-0.5"
                                                                />
                                                                <label :for="`perm-${permission.id}`" class="ml-2 cursor-pointer">
                                                                    <p class="text-sm font-medium text-gray-200">{{ permission.name }}</p>
                                                                    <p v-if="permission.description" class="text-xs text-gray-400 mt-1">
                                                                        {{ permission.description }}
                                                                    </p>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <!-- 二级分类及其权限 -->
                                                <div 
                                                    v-for="(subCategory, subPrefix) in item.category.children" 
                                                    :key="subPrefix" 
                                                    class="p-3"
                                                >
                                                    <div class="flex items-center mb-2">
                                                        <Checkbox 
                                                            v-if="subCategory.permissions.length > 0"
                                                            :id="`subcat-${item.key}-${subPrefix}`"
                                                            :indeterminate="isCategoryPartiallySelected(subCategory.permissions)"
                                                            :checked="isCategoryAllSelected(subCategory.permissions)"
                                                            @update:checked="toggleCategoryPermissions(subCategory.permissions)"
                                                            class="mr-2"
                                                        />
                                                        <span class="text-sm text-gray-300 font-medium">{{ subCategory.name }}</span>
                                                    </div>
                                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 pl-3">
                                                        <div 
                                                            v-for="permission in subCategory.permissions" 
                                                            :key="permission.id" 
                                                            class="p-2 rounded-md border border-gray-600 bg-gray-700"
                                                        >
                                                            <div class="flex items-start">
                                                                <Checkbox 
                                                                    :id="`perm-${permission.id}`" 
                                                                    v-model:checked="form.permissions" 
                                                                    :value="permission.id.toString()"
                                                                    class="mt-0.5"
                                                                />
                                                                <label :for="`perm-${permission.id}`" class="ml-2 cursor-pointer">
                                                                    <p class="text-sm font-medium text-gray-200">{{ permission.name }}</p>
                                                                    <p v-if="permission.description" class="text-xs text-gray-400 mt-1">
                                                                        {{ permission.description }}
                                                                    </p>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6 flex justify-end">
                            <PrimaryButton
                                class="ml-4"
                                :class="{ 'opacity-25': form.processing }"
                                :disabled="form.processing"
                            >
                                保存
                            </PrimaryButton>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style scoped>
/* 添加复选框三态样式 */
:indeterminate {
    background-color: rgba(59, 130, 246, 0.5);
}
</style>