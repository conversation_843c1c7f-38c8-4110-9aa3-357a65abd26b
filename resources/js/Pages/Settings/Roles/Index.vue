<script setup lang="ts">
import { ref, watch } from 'vue';
import { Link, router } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import Pagination from '@/Components/Pagination.vue';
import SearchFilter from '@/Components/SearchFilter.vue';
import debounce from 'lodash/debounce';

interface Role {
    id: number;
    name: string;
    description?: string;
    permissions?: number[];
    created_at?: string;
    updated_at?: string;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginatedRoles {
    data: Role[];
    links: PaginationLink[];
    [key: string]: any;
}

interface Filters {
    search?: string;
    [key: string]: any;
}

interface Props {
    roles: PaginatedRoles;
    filters: Filters;
}

const props = defineProps<Props>();

const search = ref(props.filters?.search || '');

const debouncedSearch = debounce(() => {
    router.get(
        route('settings.roles'),
        { search: search.value },
        { preserveState: true, preserveScroll: true }
    );
}, 300);

watch(search, () => {
    debouncedSearch();
});

function confirmDelete(role: Role): void {
    if (confirm(`确定要删除角色"${role.name}"吗？此操作无法撤销。`)) {
        router.delete(route('settings.roles.destroy', role.id));
    }
}

// 声明route函数类型
declare function route(name: string, params?: any): string;
</script>

<template>
    <AppLayout title="角色管理">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">角色管理</h2>
                <Link :href="route('settings.roles.create')" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors font-medium flex items-center">
                    <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    添加角色
                </Link>
            </div>

            <div class="bg-gray-800 overflow-hidden rounded-lg shadow-lg">
                <div class="p-6">
                    <!-- 搜索和过滤 -->
                    <div class="flex flex-wrap items-center justify-between mb-6">
                        <div class="w-full md:w-auto flex flex-wrap mb-4 md:mb-0">
                            <SearchFilter
                                v-model="search"
                                class="w-full md:w-auto mr-4 mb-4 md:mb-0"
                                placeholder="搜索角色名称或描述..."
                            />
                        </div>
                    </div>

                    <!-- 角色表格 -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-700">
                            <thead class="bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                        角色名称
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                        描述
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                                        操作
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-gray-800 divide-y divide-gray-700">
                                <tr v-for="role in roles.data" :key="role.id">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-100">
                                            {{ role.name }}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-300">
                                            {{ role.description }}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <Link :href="route('settings.roles.show', role.id)" class="px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                            查看
                                        </Link>
                                        <Link v-if="role.name !== 'admin'" :href="route('settings.roles.edit', role.id)" class="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                                            编辑
                                        </Link>
                                        <button v-if="role.name !== 'admin'" @click="confirmDelete(role)" class="px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                                            删除
                                        </button>
                                        <span v-if="role.name === 'admin'" class="inline-flex items-center px-2 py-1 bg-gray-600 text-gray-300 rounded">
                                            系统角色
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="mt-6">
                        <Pagination :links="roles.links" />
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>