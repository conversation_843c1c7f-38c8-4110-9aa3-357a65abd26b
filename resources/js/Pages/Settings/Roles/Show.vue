<script setup lang="ts">
import { ref, computed } from 'vue';
import { Link, useForm } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import Pagination from '@/Components/Pagination.vue';
import InputLabel from '@/Components/InputLabel.vue';
import Checkbox from '@/Components/Checkbox.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import DialogModal from '@/Components/DialogModal.vue';

interface Permission {
    id: number;
    name: string;
    description?: string;
}

interface Role {
    id: number;
    name: string;
    description?: string;
    created_at: string;
    updated_at: string;
    permissions: Permission[];
}

interface User {
    id: number;
    name: string;
    email: string;
    employee_id?: string;
    department?: string;
    position?: string;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginatedUsers {
    data: User[];
    links: PaginationLink[];
    [key: string]: any;
}

interface CategoryChild {
    name: string;
    permissions: Permission[];
}

interface PermissionCategory {
    name: string;
    children: Record<string, CategoryChild>;
    expanded: boolean;
    permissions: Permission[];
}

interface UsersForm {
    users: number[];
}

interface Props {
    role: Role;
    users: PaginatedUsers;
}

const props = defineProps<Props>();

// 用户分配模态框状态
const showAssignModal = ref<boolean>(false);
const availableUsers = ref<User[]>([]);
const selectedUsers = ref<number[]>([]);
const form = useForm<UsersForm>({
    users: [],
});

// 加载可用用户
async function loadAvailableUsers(): Promise<void> {
    const response = await fetch('/api/users');
    const data = await response.json();
    availableUsers.value = data;
    
    // 预选已有此角色的用户
    selectedUsers.value = props.users.data.map(user => user.id);
    form.users = selectedUsers.value;
}

// 打开用户分配模态框
function openAssignModal(): void {
    loadAvailableUsers();
    showAssignModal.value = true;
}

// 关闭用户分配模态框
function closeAssignModal(): void {
    showAssignModal.value = false;
}

// 提交用户分配
function submitAssignUsers(): void {
    form.post(route('settings.roles.assign-users', props.role.id), {
        onSuccess: () => closeAssignModal(),
    });
}

// 全选/取消全选
function toggleAllUsers(): void {
    if (selectedUsers.value.length === availableUsers.value.length) {
        selectedUsers.value = [];
    } else {
        selectedUsers.value = availableUsers.value.map(user => user.id);
    }
    form.users = selectedUsers.value;
}

// 移除用户从角色
function removeUserFromRole(userId: number): void {
    if (confirm(`确定要将此用户从「${props.role.name}」角色中移除吗？`)) {
        // 创建一个表单来提交
        const removeForm = useForm<UsersForm>({
            users: props.users.data.filter(u => u.id !== userId).map(u => u.id)
        });
        
        // 发送请求更新角色的用户列表，排除要移除的用户
        removeForm.post(route('settings.roles.assign-users', props.role.id));
    }
}

// 将权限按前缀分组为树形结构
const permissionTree = computed<Record<string, PermissionCategory>>(() => {
    const tree: Record<string, PermissionCategory> = {};
    
    // 将权限按前缀分组
    if (props.role.permissions && props.role.permissions.length > 0) {
        props.role.permissions.forEach(permission => {
            const nameParts = permission.name.split('.');
            const prefix = nameParts[0];  // 第一级分类，如dashboard, products, sales等
            
            if (!tree[prefix]) {
                tree[prefix] = {
                    name: getDisplayName(prefix),
                    children: {},
                    expanded: true,
                    permissions: []
                };
            }
            
            if (nameParts.length > 1) {
                // 如果有二级分类，如settings.roles, settings.permissions
                const subPrefix = nameParts[1];
                if (!tree[prefix].children[subPrefix]) {
                    tree[prefix].children[subPrefix] = {
                        name: getDisplayName(subPrefix),
                        permissions: []
                    };
                }
                tree[prefix].children[subPrefix].permissions.push(permission);
            } else {
                // 如果只有一级分类
                tree[prefix].permissions.push(permission);
            }
        });
    }
    
    return tree;
});

// 为分类提供友好的显示名称
function getDisplayName(prefix: string): string {
    const displayNames: Record<string, string> = {
        'dashboard': '控制台',
        'products': '产品管理',
        'sales': '销售管理',
        'purchases': '采购管理',
        'warehouse': '仓库管理',
        'logistics': '物流管理',
        'production': '生产管理',
        'QC': '质量管理',
        'finance': '财务管理',
        'reports': 'BI报表',
        'settings': '系统设置',
        'roles': '角色管理',
        'permissions': '权限管理',
        'users': '用户管理',
        'departments': '部门管理',
        'view': '查看',
        'create': '创建',
        'edit': '编辑',
        'delete': '删除',
        'access': '访问',
        'index': '列表',
        'show': '详情'
    };
    
    return displayNames[prefix] || prefix.charAt(0).toUpperCase() + prefix.slice(1);
}

// 切换分类展开/折叠
const toggleCategory = (category: PermissionCategory): void => {
    category.expanded = !category.expanded;
};

// 声明route函数类型
declare function route(name: string, params?: any): string;
</script>

<template>
    <AppLayout :title="`角色详情 - ${role.name}`">
        <div class="pbg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">角色详情 - {{ role.name }}</h2>
                <div class="flex space-x-4">
                    <Link v-if="role.name !== 'admin'" :href="route('settings.roles.edit', role.id)" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                        编辑
                    </Link>
                    <Link :href="route('settings.roles')" class="px-4 py-2 bg-gray-700 text-gray-200 rounded hover:bg-gray-600 transition-colors">
                        返回列表
                    </Link>
                </div>
            </div>

            <!-- 角色基本信息 -->
            <div class="bg-gray-800 overflow-hidden rounded-lg shadow-lg mb-8">
                <div class="p-6">
                    <h3 class="text-lg font-semibold mb-4 text-gray-100">基本信息</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <p class="text-sm text-gray-400">角色名称</p>
                            <p class="text-base text-gray-100">{{ role.name }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-400">创建时间</p>
                            <p class="text-base text-gray-100">{{ new Date(role.created_at).toLocaleString() }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-400">最后更新</p>
                            <p class="text-base text-gray-100">{{ new Date(role.updated_at).toLocaleString() }}</p>
                        </div>
                    </div>

                    <div class="mt-6">
                        <p class="text-sm text-gray-400">角色描述</p>
                        <p class="text-base text-gray-100 whitespace-pre-line">{{ role.description || '无' }}</p>
                    </div>
                </div>
            </div>

            <!-- 角色权限列表 - 树形结构 -->
            <div class="bg-gray-800 overflow-hidden rounded-lg shadow-lg mb-8">
                <div class="p-6">
                    <h3 class="text-lg font-semibold mb-4 text-gray-100">权限列表</h3>
                    
                    <div v-if="role.permissions && role.permissions.length > 0" class="space-y-4 overflow-y-auto" style="max-height: calc(100vh - 500px); min-height: 200px;">
                        <!-- 遍历树的一级分类 -->
                        <div v-for="(category, prefix) in permissionTree" :key="prefix" class="border border-gray-700 rounded-md overflow-hidden">
                            <!-- 分类标题 -->
                            <div 
                                class="flex items-center justify-between p-3 bg-gray-700 cursor-pointer"
                                @click="toggleCategory(category)"
                            >
                                <span class="text-gray-200 font-medium">{{ category.name }}</span>
                                <svg 
                                    :class="{ 'transform rotate-180': !category.expanded }"
                                    class="w-5 h-5 text-gray-400 transition-transform" 
                                    xmlns="http://www.w3.org/2000/svg" 
                                    viewBox="0 0 20 20" 
                                    fill="currentColor"
                                >
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            
                            <!-- 分类内容 -->
                            <div v-if="category.expanded" class="divide-y divide-gray-700">
                                <!-- 一级分类直接权限 -->
                                <div v-if="category.permissions.length > 0" class="p-3">
                                    <div class="text-xs text-gray-400 mb-2">{{ category.name }}通用权限</div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                        <div 
                                            v-for="permission in category.permissions" 
                                            :key="permission.id" 
                                            class="p-2 rounded-md border border-gray-600 bg-gray-700"
                                        >
                                            <div>
                                                <p class="text-sm font-medium text-gray-200">{{ permission.name }}</p>
                                                <p v-if="permission.description" class="text-xs text-gray-500 mt-1">
                                                    {{ permission.description }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 二级分类及其权限 -->
                                <div v-for="(subCategory, subPrefix) in category.children" :key="subPrefix" class="p-3" v-if="Object.keys(category.children).length > 0">
                                    <div class="text-sm text-gray-300 font-medium mb-2">{{ subCategory.name }}</div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 pl-3">
                                        <div 
                                            v-for="permission in subCategory.permissions" 
                                            :key="permission.id" 
                                            class="p-2 rounded-md border border-gray-600 bg-gray-700"
                                        >
                                            <div>
                                                <p class="text-sm font-medium text-gray-200">{{ permission.name }}</p>
                                                <p v-if="permission.description" class="text-xs text-gray-500 mt-1">
                                                    {{ permission.description }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div v-else class="text-center py-10 text-gray-400">
                        此角色没有分配任何权限
                    </div>
                </div>
            </div>

            <!-- 角色用户列表 -->
            <div class="bg-gray-800 overflow-hidden rounded-lg shadow-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-100">角色用户</h3>
                        <button 
                            @click="openAssignModal"
                            class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors flex items-center"
                        >
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            分配用户
                        </button>
                    </div>
                    
                    <div v-if="users.data.length > 0">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-700">
                                <thead class="bg-gray-700">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                            员工编号
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                            姓名
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                            部门/职位
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                            Email
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                            操作
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-gray-800 divide-y divide-gray-700">
                                    <tr v-for="user in users.data" :key="user.id">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                            {{ user.employee_id || '-' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-100">
                                            {{ user.name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                            {{ user.department ? user.department : '-' }} / {{ user.position || '-' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                            {{ user.email }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                            <button 
                                                @click="removeUserFromRole(user.id)"
                                                class="text-red-400 hover:text-red-300 hover:underline"
                                            >
                                                移除
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <div class="mt-6">
                            <Pagination :links="users.links" />
                        </div>
                    </div>
                    
                    <div v-else class="text-center py-10 text-gray-400">
                        此角色暂无用户
                    </div>
                </div>
            </div>

            <!-- 用户分配模态框 -->
            <DialogModal :show="showAssignModal" @close="closeAssignModal">
                <template #title>
                    分配用户到角色 <span class="font-semibold">{{ role.name }}</span>
                </template>

                <template #content>
                    <div v-if="availableUsers.length > 0" class="max-h-96 overflow-y-auto px-1">
                        <div class="mb-4 flex justify-end">
                            <button 
                                type="button"
                                @click="toggleAllUsers"
                                class="text-xs text-blue-400 hover:text-blue-300 hover:underline"
                            >
                                {{ selectedUsers.length === availableUsers.length ? '取消全选' : '全选' }}
                            </button>
                        </div>

                        <div class="space-y-2">
                            <div v-for="user in availableUsers" :key="user.id" class="flex items-center px-3 py-2 rounded hover:bg-gray-700">
                                <Checkbox 
                                    :id="`user-${user.id}`"
                                    :value="user.id.toString()"
                                    v-model="selectedUsers"
                                    @update:modelValue="form.users = selectedUsers"
                                    class="h-4 w-4"
                                />
                                <label :for="`user-${user.id}`" class="ml-3 block cursor-pointer">
                                    <div class="text-sm font-medium text-gray-200">{{ user.name }}</div>
                                    <div class="text-xs text-gray-400">{{ user.email }}</div>
                                    <div class="text-xs text-gray-400">{{ user.department || '' }} {{ user.position || '' }}</div>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div v-else class="text-center py-10 text-gray-400">
                        加载用户中...
                    </div>
                </template>

                <template #footer>
                    <SecondaryButton @click="closeAssignModal">
                        取消
                    </SecondaryButton>

                    <PrimaryButton
                        class="ml-3"
                        :class="{ 'opacity-25': form.processing }"
                        :disabled="form.processing"
                        @click="submitAssignUsers"
                    >
                        保存
                    </PrimaryButton>
                </template>
            </DialogModal>
        </div>
    </AppLayout>
</template> 