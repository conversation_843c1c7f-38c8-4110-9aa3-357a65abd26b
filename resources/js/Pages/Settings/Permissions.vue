<script setup lang="ts">
import PermissionsIndex from '@/Pages/Settings/Permissions/Index.vue';

interface Props {
  permissions: {
    data: any[];
    links: any[];
    [key: string]: any;
  };
  filters: {
    search?: string;
    [key: string]: any;
  };
}

// 定义接收的属性
const props = defineProps<Props>();
</script>

<template>
  <PermissionsIndex :permissions="permissions" :filters="filters" />
</template> 