<script setup lang="ts">
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref } from 'vue';

interface Stat {
    name: string;
    value: string;
}

interface Activity {
    user: string;
    action: string;
    time: string;
}

const stats = ref<Stat[]>([
    { name: '待处理订单', value: '24' },
    { name: '今日出货', value: '12' },
    { name: '库存预警', value: '5' },
    { name: '待审核单据', value: '18' },
]);

const recentActivities = ref<Activity[]>([
    { user: '张三', action: '创建了新订单 #12345', time: '10分钟前' },
    { user: '李四', action: '完成了入库单 #IN2023-001', time: '30分钟前' },
    { user: '王五', action: '修改了采购单 #PO-2023-089', time: '1小时前' },
    { user: '赵六', action: '审核通过了发票 #INV-2023-102', time: '2小时前' },
]);
</script>

<template>
    <AppLayout title="控制台">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div v-for="(item, index) in stats" :key="index" class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg p-6">
                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ item.name }}</div>
                <div class="mt-2 text-3xl font-semibold text-gray-900 dark:text-gray-100">{{ item.value }}</div>
            </div>
        </div>

        <!-- 系统概况 -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg mb-6">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">系统概况</h3>
                <p class="text-gray-600 dark:text-gray-400">
                    欢迎使用T-Soft系统。本系统提供BOM管理、销售管理、采购管理、库存管理、装箱管理、财务管理以及BI数据分析等功能，
                    帮助企业更有效地管理资源和业务流程。
                </p>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">最近活动</h3>
                <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                    <li v-for="(activity, index) in recentActivities" :key="index" class="py-3">
                        <div class="flex items-center">
                            <span class="text-sm font-medium text-gray-900 dark:text-gray-200">{{ activity.user }}</span>
                            <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">{{ activity.action }}</span>
                            <span class="ml-auto text-xs text-gray-500 dark:text-gray-500">{{ activity.time }}</span>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </AppLayout>
</template>
