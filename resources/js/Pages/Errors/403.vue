<script setup lang="ts">
import { Head, <PERSON> } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';

interface Props {
    message?: string;
}

withDefaults(defineProps<Props>(), {
    message: '您没有权限访问此页面'
});

// 声明route函数类型
declare function route(name: string, params?: any): string;

// 返回上一页函数
const goBack = () => {
    window.history.back();
};
</script>

<template>
    <Head title="权限拒绝" />

    <AppLayout>
        <div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gray-100 dark:bg-gray-900">
            <div class="w-full sm:max-w-2xl mt-6 px-6 py-12 bg-white dark:bg-gray-800 shadow-md overflow-hidden sm:rounded-lg">
                <div class="flex flex-col items-center">
                    <div class="text-6xl text-red-500 mb-4">403</div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">权限拒绝</h1>
                    <div class="text-gray-600 dark:text-gray-400 text-center mb-8">
                        {{ message }}
                    </div>
                    
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-red-500 mb-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0H9m3-4V8a3 3 0 00-6 0v3m6 0H3m15 0h3m-9 8v-5a3 3 0 016 0v5m-6 0h6" />
                    </svg>
                    
                    <div class="flex space-x-4">
                        <Link :href="route('dashboard')" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-md transition-colors">
                            返回首页
                        </Link>
                        <button @click="goBack" class="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-6 py-3 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                            返回上一页
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template> 