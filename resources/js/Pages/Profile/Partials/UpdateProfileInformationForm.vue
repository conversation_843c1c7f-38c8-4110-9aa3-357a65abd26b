<script setup lang="ts">
import { ref } from 'vue';
import { Link, router, useForm, usePage } from '@inertiajs/vue3';
import ActionMessage from '@/Components/ActionMessage.vue';
import FormSection from '@/Components/FormSection.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

// 导入 route 函数类型
declare function route(name: string, params?: any): string;

interface User {
    name: string;
    email: string;
    locale?: string;
    profile_photo_url?: string;
    profile_photo_path?: string;
    email_verified_at: string | null;
}

interface LocaleItem {
    locale: string;
    locale_name: string;
}

interface ProfileInformationProps {
    user: User;
    locales: LocaleItem[];
}

const props = defineProps<ProfileInformationProps>();

interface UpdateProfileForm {
    _method: string;
    name: string;
    email: string;
    photo: File | null;
    locale: string;
}

const form = useForm<UpdateProfileForm>({
    _method: 'PUT',
    name: props.user.name,
    email: props.user.email,
    photo: null,
    locale: props.user.locale || 'zh_CN',
});

// 定义页面属性类型
interface PageProps {
    jetstream: {
        managesProfilePhotos: boolean;
        hasEmailVerification: boolean;
    };
    auth: {
        user: User;
    };
    [key: string]: any;
}

const page = usePage<PageProps>();
const verificationLinkSent = ref<boolean>(false);
const photoPreview = ref<string | null>(null);
const photoInput = ref<HTMLInputElement | null>(null);

const updateProfileInformation = (): void => {
    if (photoInput.value) {
        const files = photoInput.value.files;
        if (files && files.length > 0) {
            form.photo = files[0];
        }
    }

    form.post(route('user-profile-information.update'), {
        errorBag: 'updateProfileInformation',
        preserveScroll: true,
        onSuccess: () => clearPhotoFileInput(),
    });
};

const sendEmailVerification = (): void => {
    verificationLinkSent.value = true;
};

const selectNewPhoto = (): void => {
    if (photoInput.value) {
        photoInput.value.click();
    }
};

const updatePhotoPreview = (): void => {
    if (!photoInput.value || !photoInput.value.files || photoInput.value.files.length === 0) {
        return;
    }

    const photo = photoInput.value.files[0];
    const reader = new FileReader();

    reader.onload = (e) => {
        if (e.target && typeof e.target.result === 'string') {
            photoPreview.value = e.target.result;
        }
    };

    reader.readAsDataURL(photo);
};

const deletePhoto = (): void => {
    router.delete(route('current-user-photo.destroy'), {
        preserveScroll: true,
        onSuccess: () => {
            photoPreview.value = null;
            clearPhotoFileInput();
        },
    });
};

const clearPhotoFileInput = (): void => {
    if (photoInput.value && photoInput.value.value) {
        photoInput.value.value = '';
    }
};
</script>

<template>
    <FormSection @submitted="updateProfileInformation">
        <template #title>
            个人信息
        </template>

        <template #description>
            更新您的账户个人信息和电子邮箱地址。
        </template>

        <template #form>
            <!-- Profile Photo -->
            <div v-if="page.props.jetstream.managesProfilePhotos" class="col-span-6 sm:col-span-4">
                <!-- Profile Photo File Input -->
                <input
                    id="photo"
                    ref="photoInput"
                    type="file"
                    class="hidden"
                    @change="updatePhotoPreview"
                >

                <InputLabel for="photo" value="头像" />

                <!-- Current Profile Photo -->
                <div v-show="! photoPreview" class="mt-2">
                    <img :src="user.profile_photo_url" :alt="user.name" class="rounded-full size-20 object-cover">
                </div>

                <!-- New Profile Photo Preview -->
                <div v-show="photoPreview" class="mt-2">
                    <span
                        class="block rounded-full size-20 bg-cover bg-no-repeat bg-center"
                        :style="photoPreview ? `background-image: url('${photoPreview}');` : ''"
                    />
                </div>

                <SecondaryButton class="mt-2 me-2" type="button" @click.prevent="selectNewPhoto">
                    选择新头像
                </SecondaryButton>

                <SecondaryButton
                    v-if="user.profile_photo_path"
                    type="button"
                    class="mt-2"
                    @click.prevent="deletePhoto"
                >
                    移除头像
                </SecondaryButton>

                <InputError :message="form.errors.photo" class="mt-2" />
            </div>

            <!-- Name -->
            <div class="col-span-6 sm:col-span-4">
                <InputLabel for="name" value="姓名" />
                <TextInput
                    id="name"
                    v-model="form.name"
                    type="text"
                    class="mt-1 block w-full"
                    required
                    readonly
                    autocomplete="name"
                />
                <p class="text-xs text-gray-400 mt-1">账号姓名不可修改</p>
                <InputError :message="form.errors.name" class="mt-2" />
            </div>

            <!-- Language -->
            <div class="col-span-6 sm:col-span-4">
                <InputLabel for="locale" value="默认语言" />
                <select
                    id="locale"
                    v-model="form.locale"
                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                >
                    <option v-for="locale in locales" :key="locale.locale" :value="locale.locale">
                        {{ locale.locale_name }}
                    </option>
                </select>
                <InputError :message="form.errors.locale" class="mt-2" />
            </div>

            <!-- Email -->
            <div class="col-span-6 sm:col-span-4">
                <InputLabel for="email" value="电子邮箱" />
                <TextInput
                    id="email"
                    v-model="form.email"
                    type="email"
                    class="mt-1 block w-full"
                    required
                    autocomplete="username"
                />
                <InputError :message="form.errors.email" class="mt-2" />

                <div v-if="page.props.jetstream.hasEmailVerification && user.email_verified_at === null">
                    <p class="text-sm mt-2 dark:text-white">
                        您的电子邮箱地址尚未验证。

                        <Link
                            :href="route('verification.send')"
                            method="post"
                            as="button"
                            class="underline text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
                            @click.prevent="sendEmailVerification"
                        >
                            点击此处重新发送验证邮件。
                        </Link>
                    </p>

                    <div v-show="verificationLinkSent" class="mt-2 font-medium text-sm text-green-600 dark:text-green-400">
                        新的验证链接已发送至您的电子邮箱地址。
                    </div>
                </div>
            </div>
        </template>

        <template #actions>
            <ActionMessage :on="form.recentlySuccessful" class="me-3">
                已保存。
            </ActionMessage>

            <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                保存
            </PrimaryButton>
        </template>
    </FormSection>
</template>
