<script setup lang="ts">
import { usePage } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import LogoutOtherBrowserSessionsForm from '@/Pages/Profile/Partials/LogoutOtherBrowserSessionsForm.vue';
import SectionBorder from '@/Components/SectionBorder.vue';
import UpdatePasswordForm from '@/Pages/Profile/Partials/UpdatePasswordForm.vue';
import UpdateProfileInformationForm from '@/Pages/Profile/Partials/UpdateProfileInformationForm.vue';

interface LocaleItem {
  locale: string;
  locale_name: string;
}

interface ProfileProps {
  confirmsTwoFactorAuthentication: boolean;
  sessions: Array<{
    agent: {
      is_desktop: boolean;
      platform: string;
      browser: string;
    };
    ip_address: string;
    is_current_device: boolean;
    last_active: string;
  }>;
  locales: LocaleItem[];
}

// 定义页面属性类型
interface PageProps {
  auth: {
    user: any;
  };
  jetstream: {
    canUpdateProfileInformation: boolean;
    canUpdatePassword: boolean;
    managesProfilePhotos: boolean;
    hasEmailVerification: boolean;
  };
  [key: string]: any;
}

defineProps<ProfileProps>();

// 获取页面属性，包含 $page 全局属性
const page = usePage<PageProps>();
</script>

<template>
    <AppLayout title="个人资料">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                个人资料
            </h2>
        </template>

        <div>
            <div class="max-w-7xl mx-auto py-10 sm:px-6 lg:px-8">
                <div v-if="page.props.jetstream.canUpdateProfileInformation && page.props.auth.user">
                    <UpdateProfileInformationForm :user="page.props.auth.user" :locales="locales" />

                    <SectionBorder />
                </div>

                <div v-if="page.props.jetstream.canUpdatePassword">
                    <UpdatePasswordForm class="mt-10 sm:mt-0" />

                    <SectionBorder />
                </div>

                <LogoutOtherBrowserSessionsForm :sessions="sessions" class="mt-10 sm:mt-0" />
            </div>
        </div>
    </AppLayout>
</template>
