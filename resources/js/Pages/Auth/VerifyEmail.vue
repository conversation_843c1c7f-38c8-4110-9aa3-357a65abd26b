<script setup lang="ts">
import { computed } from 'vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticationCard from '@/Components/AuthenticationCard.vue';
import AuthenticationCardLogo from '@/Components/AuthenticationCardLogo.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';

interface Props {
    status?: string;
}

const props = defineProps<Props>();

const form = useForm({});

const submit = (): void => {
    form.post(route('verification.send'));
};

const verificationLinkSent = computed<boolean>(() => props.status === 'verification-link-sent');

// 声明route函数类型
declare function route(name: string, params?: any): string;
</script>

<template>
    <Head title="邮箱验证" />

    <AuthenticationCard>
        <template #logo>
            <AuthenticationCardLogo />
        </template>

        <div class="mb-4 text-sm text-gray-600 dark:text-gray-400">
            在继续之前，请点击我们刚刚发送到您邮箱的链接来验证您的电子邮箱地址。如果您没有收到邮件，我们很乐意再次发送一封给您。
        </div>

        <div v-if="verificationLinkSent" class="mb-4 font-medium text-sm text-green-600 dark:text-green-400">
            新的验证链接已发送至您在个人资料设置中提供的电子邮箱地址。
        </div>

        <form @submit.prevent="submit">
            <div class="mt-4 flex items-center justify-between">
                <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    重新发送验证邮件
                </PrimaryButton>

                <div>
                    <Link
                        :href="route('profile.show')"
                        class="underline text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
                    >
                        编辑个人资料</Link>

                    <Link
                        :href="route('logout')"
                        method="post"
                        as="button"
                        class="underline text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 ms-2"
                    >
                        退出登录
                    </Link>
                </div>
            </div>
        </form>
    </AuthenticationCard>
</template>
