<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Head, Link, useForm, usePage } from '@inertiajs/vue3';
import AuthenticationCard from '@/Components/AuthenticationCard.vue';
import AuthenticationCardLogo from '@/Components/AuthenticationCardLogo.vue';
import Checkbox from '@/Components/Checkbox.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import axios from 'axios';
import apiService from '@/Utils/ApiService';

// 为apiService添加正确的类型定义
interface ApiServiceType {
    login(account: string, password: string, deviceName?: string | null): Promise<LoginResponse>;
    getCurrentUser(): Promise<any>;
    getTokenFromCurrentSession(): Promise<string | null>;
    setAuthToken(token: string): void;
    clearAuthToken(): void;
}

// 将apiService断言为正确的类型
const typedApiService = apiService as unknown as ApiServiceType;

interface Props {
    canResetPassword?: boolean;
    status?: string;
}

defineProps<Props>();

const isLoading = ref<boolean>(false);
const csrfError = ref<string | null>(null);
const apiError = ref<string | null>(null);

// 获取页面属性
const page = usePage();

interface LoginForm {
    account: string;
    password: string;
    remember: boolean;
}

const form = useForm<LoginForm>({
    account: '',
    password: '',
    remember: true,
});

// 获取CSRF令牌
const getCsrfToken = async (): Promise<boolean> => {
    try {
        await axios.get('/sanctum/csrf-cookie', {
            withCredentials: true
        });
        console.log('CSRF令牌已刷新');
        return true;
    } catch (error) {
        console.error('获取CSRF令牌失败:', error);
        csrfError.value = '无法获取安全令牌，请刷新页面重试';
        return false;
    }
};

interface LoginResponse {
    token: string;
    user: {
        id: number;
        name: string;
        email: string;
        [key: string]: any;
    };
}

// Inertia表单错误类型
interface InertiaErrorBag {
    [key: string]: string | undefined;
}

interface InertiaErrorResponse {
    response?: {
        status?: number;
    };
}

const submit = async (): Promise<void> => {
    try {
        isLoading.value = true;
        csrfError.value = null;
        apiError.value = null;

        // 获取CSRF令牌
        await getCsrfToken();

        // 尝试使用API Service进行登录
        try {
            const { token, user } = await typedApiService.login(form.account, form.password);
            console.log('API登录成功，已获取token');
            
            // 使用Inertia提交表单以继续常规网页流程
            form.transform(data => ({
                ...data,
                remember: form.remember ? 'on' : '',
            })).post(route('login'), {
                onSuccess: () => {
                    console.log('网页登录成功');
                },
                onError: (errors: InertiaErrorBag & InertiaErrorResponse) => {
                    console.error('网页登录错误:', errors);
                    // 检查是否是CSRF错误 (419)
                    if (errors.response && errors.response.status === 419) {
                        csrfError.value = 'CSRF令牌验证失败，请刷新页面重试';
                    }
                },
                onFinish: () => {
                    isLoading.value = false;
                    form.reset('password');
                }
            });
        } catch (error: any) {
            console.error('API登录失败:', error);
            apiError.value = error.response?.data?.message || '登录失败，请检查账号和密码';
            isLoading.value = false;
        }
    } catch (e) {
        console.error('登录过程发生错误:', e);
        isLoading.value = false;
        csrfError.value = '提交表单时发生错误，请刷新页面重试';
    }
};

// 组件挂载时获取CSRF令牌
onMounted(async () => {
    console.log('登录页面已加载');
    await getCsrfToken();
});

// 声明route函数类型
declare function route(name: string, params?: any): string;
</script>

<template>
    <Head title="登录" />

    <AuthenticationCard>
        <template #logo>
            <div class="mb-4 flex justify-center items-center">
                <img :src="page.props.app.logo" alt="Logo" class="h-24 w-auto">
                <span class="text-2xl font-semibold text-gray-800 dark:text-gray-200">
                    {{ page.props.app.name }}
                </span>
            </div>
        </template>

        <div v-if="status" class="mb-4 font-medium text-sm text-green-600 dark:text-green-400">
            {{ status }}
        </div>

        <form @submit.prevent="submit">
            <div>
                <InputLabel for="account" value="账号" />
                <TextInput
                    id="account"
                    v-model="form.account"
                    type="text"
                    class="mt-1 block w-full"
                    required
                    autofocus
                    autocomplete="username"
                />
                <InputError class="mt-2" :message="form.errors.account" />
            </div>

            <div class="mt-4">
                <InputLabel for="password" value="密码" />
                <TextInput
                    id="password"
                    v-model="form.password"
                    type="password"
                    class="mt-1 block w-full"
                    required
                    autocomplete="current-password"
                />
                <InputError class="mt-2" :message="form.errors.password" />
            </div>

            <div class="block mt-4">
                <label class="flex items-center">
                    <Checkbox v-model:checked="form.remember" name="remember" />
                    <span class="ms-2 text-sm text-gray-600 dark:text-gray-400">记住我</span>
                </label>
            </div>

            <div v-if="csrfError" class="mt-4 text-sm text-red-600">
                {{ csrfError }}
            </div>

            <div v-if="apiError" class="mt-4 text-sm text-red-600">
                {{ apiError }}
            </div>

            <div class="flex items-center justify-end mt-4">
                <PrimaryButton class="ms-4" :class="{ 'opacity-25': isLoading || form.processing }" :disabled="isLoading || form.processing">
                    <span v-if="isLoading">登录中...</span>
                    <span v-else>登录</span>
                </PrimaryButton>
            </div>
        </form>
    </AuthenticationCard>
</template>
