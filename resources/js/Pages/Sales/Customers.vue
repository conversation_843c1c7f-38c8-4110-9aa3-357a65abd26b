<script setup lang="ts">
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref, onMounted, computed, watch, reactive, onBeforeUnmount } from 'vue';
import apiService from '@/Utils/ApiService';
import { useToast } from 'primevue/usetoast'; //用于显示提示信息
import Dialog from 'primevue/dialog';
import { usePage } from '@inertiajs/vue3';
import { PageProps, User } from '@/types';
import { debounce } from '@/Utils/DebounceUtil';

// 定义类型
interface Pagination {
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
    from: number;
    to: number;
}

interface Customer {
    id: number;
    code: string;
    short_name: string;
    full_name: string;
    currency_id: number | null;
    tax_type_id: number | null;
    trade_term_id: number | null;
    pricing_method_id: number | null;
    invoice_type_id: number | null;
    payment_term_id: number | null;
    account_receivable_type_id: number | null;
    sales_type_id: number | null;
    exchange_rate_basis_id: number | null;
    tax_number: string;
    status: string;
    remarks: string;
    [key: string]: any; // 索引签名，允许动态属性访问
}

interface FormData {
    id?: number;
    short_name: string;
    full_name: string;
    currency_id: number | null;
    tax_type_id: number | null;
    trade_term_id: number | null;
    pricing_method_id: number | null;
    invoice_type_id: number | null;
    payment_term_id: number | null;
    account_receivable_type_id: number | null;
    sales_type_id: number | null;
    exchange_rate_basis_id: number | null;
    tax_number: string;
    status: string;
    remarks: string;
    [key: string]: any; // 索引签名，允许动态属性访问
}

interface SelectOption {
    id: number;
    name: string;
}

interface StatusOption {
    label: string;
    value: string;
}

// API错误接口定义
interface ApiError {
    response?: {
        data?: {
            message?: string;
            errors?: Record<string, string[]>;
        }
    };
    message?: string;
}

const toast = useToast();

// 分页相关
const pagination = ref<Pagination>({
    total: 0,
    per_page: 20,
    current_page: 1,
    last_page: 0,
    from: 0,
    to: 0
});

// 搜索相关
const search = ref<string>('');
// 使用导入的debounce函数
const searchDebounced = debounce(() => {
    pagination.value.current_page = 1; // 搜索时重置到第一页
    loadCustomers();
}, 500);

// 监听搜索框变化
watch(search, () => {
    searchDebounced();
});

// 获取当前用户信息
const page = computed(() => {
    try {
        return usePage<PageProps>();
    } catch (e) {
        console.error('获取页面数据错误:', e);
        return { props: { auth: { user: {} } } } as any;
    }
});
const user = computed(() => {
    try {
        return page.value.props.auth.user;
    } catch (e) {
        console.error('获取用户数据错误:', e);
        return {} as User;
    }
});

// 处理搜索按钮点击
const handleSearch = (): void => {
    pagination.value.current_page = 1;
    loadCustomers();
};

// 计算属性：表单是否只读
const isFormReadOnly = computed<boolean>(() => {
    const result = formMode.value === 'view';
    console.log('计算isFormReadOnly：', result, 'formMode值：', formMode.value);
    return result;
});

// 计算属性：页码列表
const pageNumbers = computed<(number | string)[]>(() => {
    const current = pagination.value.current_page;
    const last = pagination.value.last_page;
    const delta = 2; // 当前页前后显示的页数
    const pages: (number | string)[] = [];

    // 始终显示第一页
    pages.push(1);

    // 计算当前页附近要显示的页码
    for (let i = Math.max(2, current - delta); i <= Math.min(last - 1, current + delta); i++) {
        pages.push(i);
    }

    // 如果有必要，添加省略号
    if (current - delta > 2) pages.splice(1, 0, '...');
    if (current + delta < last - 1) pages.push('...');

    // 如果最后一页不是第一页，则添加最后一页
    if (last > 1) pages.push(last);

    return pages;
});

// 定义缺失的变量
const customers = ref<Customer[]>([]);
const statusFilter = ref<string>('Y'); // 默认选中"有效"
const loading = ref<boolean>(false);
const error = ref<string | null>(null);
const debugInfo = ref<string | null>(null);

// 公司相关
interface Company {
    company_code: string;
    company_name: string;
}
const companies = ref<Company[]>([]); // 存储用户可访问的公司列表
const selectedCompany = ref<string>(''); // 当前选中的公司
const showCompanyDropdown = ref<boolean>(false); // 控制公司下拉列表显示

// 监听选中公司变化
watch(selectedCompany, (newVal, oldVal) => {
    if (newVal !== oldVal) {
        console.log('选中公司变化，重新获取数据:', newVal);
        pagination.value.current_page = 1; // 重置到第一页
        loadCustomers(); // 重新获取数据
    }
});

// 获取用户可访问的公司列表
const fetchUserCompanies = async (): Promise<void> => {
    try {
        const response = await apiService.get('user/companies');
        if (response.data && response.data.length > 0) {
            companies.value = response.data;
            
            // 设置默认公司为用户默认公司
            if (user.value?.default_company_code && !selectedCompany.value) {
                selectedCompany.value = user.value.default_company_code;
            } else if (companies.value.length > 0 && !selectedCompany.value) {
                // 如果没有默认公司，则使用第一个公司
                selectedCompany.value = companies.value[0].company_code;
            }
        }
    } catch (err) {
        console.error('获取用户公司列表失败:', err);
    }
};

// 添加排序相关变量
const sortField = ref<string>('');
const sortDirection = ref<'asc' | 'desc'>('asc');

// 状态选项
// 使用在下拉选择框中
const statusOptions: StatusOption[] = [
    { label: '有效', value: 'Y' },
    { label: '无效', value: 'N' }
];

// 表单验证错误
const formErrors = reactive<Record<string, string[]>>({});

// 表单相关
const formVisible = ref<boolean>(false);
const formMode = ref<'create' | 'edit' | 'view'>('create'); // 'create', 'edit' 或 'view'
const formLoading = ref<boolean>(false);
const formData = reactive<FormData>({
    short_name: '',
    full_name: '',
    currency_id: null,
    tax_type_id: null,
    trade_term_id: null,
    pricing_method_id: null,
    invoice_type_id: null,
    payment_term_id: null,
    account_receivable_type_id: null,
    sales_type_id: null,
    exchange_rate_basis_id: null,
    tax_number: '',
    status: 'Y',
    remarks: ''
});

// 下拉选项数据
const currencies = ref<SelectOption[]>([]);
const taxTypes = ref<SelectOption[]>([]);
const tradeTerms = ref<SelectOption[]>([]);
const pricingMethods = ref<SelectOption[]>([]);
const invoiceTypes = ref<SelectOption[]>([]);
const paymentTerms = ref<SelectOption[]>([]);
const accountReceivableTypes = ref<SelectOption[]>([]);
const salesTypes = ref<SelectOption[]>([]);
const exchangeRateBases = ref<SelectOption[]>([]);

// 加载客户列表数据
const loadCustomers = async (): Promise<void> => {
    loading.value = true;

    try {
        // 准备请求参数
        const params: Record<string, any> = {
            page: pagination.value.current_page,
            per_page: pagination.value.per_page,
            _t: new Date().getTime() // 防止缓存
        };

        // 添加搜索参数
        if (search.value) {
            params.search = search.value;
        }

        // 添加状态筛选参数
        if (statusFilter.value) {
            params.status = statusFilter.value;
        }
        
        // 添加公司筛选参数
        if (selectedCompany.value) {
            params.company_code = selectedCompany.value;
        }

        // 添加排序参数
        if (sortField.value) {
            params.sort_field = sortField.value;
            params.sort_direction = sortDirection.value;
        }

        console.log('请求参数:', params);
        // 使用apiService发送请求
        const response = await apiService.get('/sales/customers/getList', params);
        console.log('API返回数据:', response.data);

        if (response.data && response.data.status === 'success') {
            // 更新分页信息
            pagination.value = response.data.pagination;

            customers.value = response.data.data;

            // 成功获取数据，清除错误信息
            error.value = null;
            loading.value = false;

            return;
        } else {
            error.value = '获取数据失败: ' + (response.data?.message || '未知错误');
            debugInfo.value = JSON.stringify(response.data || {});
            loading.value = false;
        }

    } catch (error: unknown) {
        const apiError = error as ApiError;
        console.error('加载客户数据失败:', apiError);
        toast.add({ 
            severity: 'error', 
            summary: '错误', 
            detail: apiError.response?.data?.message || '加载客户数据失败', 
            life: 3000 
        });
    } finally {
        loading.value = false;
    }
};

// 加载表单所需的下拉数据
const loadFormData = async (): Promise<void> => {
    try {
        const response = await apiService.get('/sales/customers/form-data');
        console.log('API返回数据:', response.data);
        if(response.data && response.data.status === 'success'){
            const data = response.data.data;

            currencies.value = data.currencies || [];
            taxTypes.value = data.taxTypes || [];
            tradeTerms.value = data.tradeTerms || [];
            pricingMethods.value = data.purchasePricingMethods || [];
            invoiceTypes.value = data.invoiceTypes || [];
            paymentTerms.value = data.receiptPaymentTerms || [];
            accountReceivableTypes.value = data.accountReceivablePayableTypes || [];
            salesTypes.value = data.salesTypes || [];
            exchangeRateBases.value = data.exchangeRateBases || [];

            console.log('加载的下拉框数据:', {
                currencies: currencies.value,
                taxTypes: taxTypes.value,
                tradeTerms: tradeTerms.value,
                pricingMethods: pricingMethods.value,
                invoiceTypes: invoiceTypes.value,
                paymentTerms: paymentTerms.value,
                accountReceivableTypes: accountReceivableTypes.value,
                salesTypes: salesTypes.value,
                exchangeRateBases: exchangeRateBases.value
            });
        } else {
            console.error('加载表单数据失败: API返回非成功状态');
            toast.add({ severity: 'error', summary: '错误', detail: '加载表单数据失败', life: 3000 });
        }
    } catch (error: unknown) {
        const apiError = error as ApiError;
        console.error('加载表单数据失败:', apiError);
        toast.add({ 
            severity: 'error', 
            summary: '错误', 
            detail: apiError.response?.data?.message || '加载表单数据失败', 
            life: 3000 
        });
    }
};

// 获取单个客户详情
const getCustomer = async (id: number): Promise<Customer | null> => {
    try {
        const response = await apiService.get(`/sales/customers/show/${id}`);
        console.log('获取客户详情响应:', response.data);

        if (response.data && response.data.status === 'success') {
            return response.data.data;
        } else {
            console.error('获取客户详情失败: API返回非成功状态');
            toast.add({ severity: 'error', summary: '错误', detail: '获取客户详情失败', life: 3000 });
            return null;
        }
    } catch (error: unknown) {
        const apiError = error as ApiError;
        console.error('获取客户详情失败:', apiError);
        toast.add({ 
            severity: 'error', 
            summary: '错误', 
            detail: apiError.response?.data?.message || '获取客户详情失败', 
            life: 3000 
        });
        return null;
    }
};

// 添加排序处理函数
const handleSort = (field: string) => {
    if (sortField.value === field) {
        // 如果已经按照这个字段排序，切换排序方向
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
    } else {
        // 否则，设置新的排序字段，默认升序
        sortField.value = field;
        sortDirection.value = 'asc';
    }

    // 重置到第一页并重新获取数据
    pagination.value.current_page = 1;
    loadCustomers();
};

// 切换页码
const goToPage = (page: number | string) => {
    if (page === '...' || page === pagination.value.current_page) return;
    pagination.value.current_page = typeof page === 'string' ? parseInt(page) : page;
    loadCustomers();
};

// 下一页
const nextPage = () => {
    if (pagination.value.current_page < pagination.value.last_page) {
        pagination.value.current_page++;
        loadCustomers();
    }
};

// 上一页
const prevPage = () => {
    if (pagination.value.current_page > 1) {
        pagination.value.current_page--;
        loadCustomers();
    }
};

// 切换每页显示数量
const changePerPage = (perPage: number) => {
    pagination.value.per_page = perPage;
    pagination.value.current_page = 1; // 重置到第一页
    loadCustomers();
};

// 显示创建表单
const showCreateForm = () => {
    formMode.value = 'create';
    resetForm();
    formVisible.value = true;
};

// 显示查看表单
const showViewForm = async (id: number) => {
    console.log('显示查看表单，设置formMode为view');
    formMode.value = 'view';
    formLoading.value = true;
    formVisible.value = true;
    console.log('当前formMode值：', formMode.value);

    try {
        const customer = await getCustomer(id);
        console.log('查看客户数据:', customer);

        if (customer) {
            // 重置表单，确保清除之前的数据
            resetForm();

            // 处理各种字段类型
            Object.keys(formData).forEach(key => {
                if (customer[key] !== undefined && customer[key] !== null) {
                    formData[key] = customer[key];
                } else if (key.endsWith('_id')) {
                    formData[key] = null; // 外键字段设置为null
                } else {
                    formData[key] = ''; // 其他字段设置为空字符串
                }
            });

            // 确保客户ID存在
            if (!formData.id && customer.id) {
                formData.id = customer.id;
                console.log('设置客户ID:', formData.id);
            }
        }
    } catch (error: unknown) {
        const apiError = error as ApiError;
        console.error('加载客户数据失败:', apiError);
        toast.add({ 
            severity: 'error', 
            summary: '错误', 
            detail: apiError.response?.data?.message || '加载客户数据失败', 
            life: 3000 
        });
    } finally {
        formLoading.value = false;
    }
};

// 显示编辑表单
const showEditForm = async (id: number) => {
    formMode.value = 'edit';
    formLoading.value = true;
    formVisible.value = true;

    try {
        const customer = await getCustomer(id);
        console.log('编辑客户数据:', customer);

        if (customer) {
            // 重置表单，确保清除之前的数据
            resetForm();

            // 处理各种字段类型
            Object.keys(formData).forEach(key => {
                if (customer[key] !== undefined && customer[key] !== null) {
                    formData[key] = customer[key];
                } else if (key.endsWith('_id')) {
                    formData[key] = null; // 外键字段设置为null
                } else {
                    formData[key] = ''; // 其他字段设置为空字符串
                }
            });

            // 确保客户ID存在
            if (!formData.id && customer.id) {
                formData.id = customer.id;
                console.log('设置客户ID:', formData.id);
            }
        }
    } catch (error: unknown) {
        const apiError = error as ApiError;
        console.error('加载客户数据失败:', apiError);
        toast.add({ 
            severity: 'error', 
            summary: '错误', 
            detail: apiError.response?.data?.message || '加载客户数据失败', 
            life: 3000 
        });
    } finally {
        formLoading.value = false;
    }
};

// 重置表单
const resetForm = () => {
    // 重置所有字段
    Object.keys(formData).forEach(key => {
        // 对于外键字段，设置为null而不是空字符串
        if (key.endsWith('_id')) {
            formData[key] = null;
        } else {
            formData[key] = '';
        }
    });

    // 设置默认状态为有效
    formData.status = 'Y';

    // 清除验证错误
    Object.keys(formErrors).forEach(key => {
        delete formErrors[key];
    });
};

// 保存表单
const saveForm = async () => {
    formLoading.value = true;

    try {
        // 打印表单数据，便于调试
        console.log('提交的表单数据:', formData);

        // 只在编辑模式下检查id是否存在
        if (formMode.value === 'edit' && !formData.id) {
            console.error('编辑模式下，formData.id不存在');
            toast.add({
                severity: 'error',
                summary: '错误',
                detail: '客户ID不存在，无法更新',
                life: 3000
            });
            formLoading.value = false;
            return;
        }

        const url = formMode.value === 'create'
            ? '/sales/customers/create'
            : `/sales/customers/update/${formData.id}`;

        const method = formMode.value === 'create' ? 'post' : 'put';

        console.log(`发送${method.toUpperCase()}请求到: ${url}`);
        const response = await apiService[method](url, formData);
        console.log('API保存响应:', response.data);

        if (response.data.status === 'success') {
            toast.add({
                severity: 'success',
                summary: '成功',
                detail: formMode.value === 'create' ? '客户创建成功' : '客户更新成功',
                life: 3000
            });
            formVisible.value = false;
            loadCustomers(); // 重新加载列表
        } else {
            // 如果响应不成功但没有抛出异常
            console.error('保存失败:', response.data.message || '未知错误');
            toast.add({
                severity: 'error',
                summary: '错误',
                detail: response.data.message || '保存失败',
                life: 3000
            });
        }
    } catch (error: unknown) {
        console.error('保存表单异常:', error);

        // 将error作为ApiError类型来处理
        const apiError = error as ApiError;
        
        if (apiError.response?.data?.errors) {
            const errors = apiError.response.data.errors;
            console.log('表单验证错误:', errors);
            Object.keys(errors).forEach(key => {
                formErrors[key] = errors[key];
            });
        }

        toast.add({
            severity: 'error',
            summary: '错误',
            detail: apiError.response?.data?.message || '保存失败',
            life: 3000
        });
    } finally {
        formLoading.value = false;
    }
};

// 删除客户
const deleteCustomer = async (id: number) => {
    if (!confirm('确定要删除这个客户吗？')) {
        return;
    }

    try {
        const response = await apiService.delete(`/sales/customers/${id}`);
        if (response.data.status === 'success') {
            toast.add({
                severity: 'success',
                summary: '成功',
                detail: '客户删除成功',
                life: 3000
            });
            loadCustomers(); // 重新加载列表
        }
    } catch (error: unknown) {
        const apiError = error as ApiError;
        toast.add({
            severity: 'error',
            summary: '错误',
            detail: apiError.response?.data?.message || '删除失败',
            life: 3000
        });
    }
};

// 点击页面其他地方关闭下拉列表
const handleClickOutside = (event: MouseEvent): void => {
    const companyDropdown = document.getElementById('company-dropdown');
    if (companyDropdown && !companyDropdown.contains(event.target as Node)) {
        showCompanyDropdown.value = false;
    }
};

// 组件挂载时加载数据
onMounted(() => {
    console.log('客户管理组件已挂载，获取数据');
    loadCustomers();//加载客户列表数据
    loadFormData();//加载表单所需的下拉数据
    fetchUserCompanies(); // 获取用户可访问的公司列表
    
    // 添加点击外部关闭下拉列表的事件监听
    document.addEventListener('click', handleClickOutside);
});

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside);
});

</script>

<template>
    <AppLayout title="客户管理">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">客户管理</h2>
                <button
                    @click="showCreateForm"    
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-300 flex items-center"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    新增客户
                </button>
            </div>

            <!-- 搜索栏 -->
            <div class="mb-4 flex flex-wrap gap-2">
                <div class="flex-grow flex space-x-2">
                    <!-- 公司下拉列表 -->
                    <div class="relative" id="company-dropdown">
                        <button 
                            @click="showCompanyDropdown = !showCompanyDropdown" 
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 flex justify-between items-center"
                            style="min-width: 150px"
                        >
                            <span>{{ companies.find(c => c.company_code === selectedCompany)?.company_name || '选择公司' }}</span>
                            <svg class="w-4 h-4 ml-2" :class="{'transform rotate-180': showCompanyDropdown}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        
                        <!-- 下拉内容 -->
                        <div v-if="showCompanyDropdown" class="fixed z-50 mt-1 bg-white rounded-md shadow-lg dark:bg-gray-700" 
                             style="min-width: 150px; top: auto; left: auto;">
                            <div class="p-2 border-b border-gray-200 dark:border-gray-600">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">选择公司</span>
                            </div>
                            <div class="overflow-y-auto p-2" style="max-height: 300px; min-height: 100px;">
                                <div 
                                    v-for="company in companies" 
                                    :key="company.company_code"
                                    class="flex items-center p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer"
                                    @click="selectedCompany = company.company_code; showCompanyDropdown = false;"
                                >
                                    <span class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
                                        {{ company.company_name }}
                                    </span>
                                </div>
                                <!-- 无数据显示 -->
                                <div v-if="companies.length === 0" class="p-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                                    暂无数据
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <input
                        type="text"
                        v-model="search"
                        placeholder="搜索客户..."
                        class="rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 flex-grow"
                    >
                    <select
                        v-model="statusFilter"
                        class="rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        @change="handleSearch"
                    >
                        <option value="">全部状态</option>
                        <option value="Y">有效</option>
                        <option value="N">无效</option>
                    </select>
                    <button
                        class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md"
                        @click="handleSearch"
                    >
                        搜索
                    </button>
                </div>

                <!-- 每页显示数量 -->
                <div class="flex items-center ml-auto pl-2">
                    <span class="text-sm text-gray-700 dark:text-gray-300">每页显示:</span>
                    <select
                        v-model="pagination.per_page"
                        @change="changePerPage(pagination.per_page)"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2 ml-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    >
                        <option value="20">20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>
            </div>

            <!-- 客户列表 -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" @click="handleSort('code')">
                                编号 <span v-if="sortField === 'code'">↓</span>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" @click="handleSort('short_name')">
                                客户简称 <span v-if="sortField === 'short_name'">↓</span>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" @click="handleSort('full_name')">
                                客户全称 <span v-if="sortField === 'full_name'">↓</span>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" @click="handleSort('status')">
                                状态 <span v-if="sortField === 'status'">↓</span>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <tr v-if="loading" class="text-center">
                            <td colspan="7" class="px-6 py-4">
                                <div class="flex justify-center">
                                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 dark:border-gray-100"></div>
                                </div>
                            </td>
                        </tr>
                        <tr v-else-if="!pagination.total" class="text-center">
                            <td colspan="7" class="px-6 py-4 text-gray-500 dark:text-gray-400">
                                没有找到客户数据
                            </td>
                        </tr>
                        <tr v-for="customer in customers" :key="customer.id">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ customer.code }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ customer.short_name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ customer.full_name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                                <span :class="customer.status === 'Y' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'" class="px-2 py-1 text-xs rounded-full">
                                    {{ customer.status === 'Y' ? '有效' : '无效' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button
                                    class="px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                                    @click="showViewForm(customer.id)"
                                >
                                    查看
                                </button>
                                <button
                                    class="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                                    @click="showEditForm(customer.id)"
                                >
                                    编辑
                                </button>
                                <button
                                    class="px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                                    @click="deleteCustomer(customer.id)"
                                >
                                    删除
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页控制 -->
            <div v-if="pagination.total > 0" class="flex items-center justify-between mt-4">
                    <div class="text-sm text-gray-700 dark:text-gray-300">
                        显示 {{ pagination.from }} 到 {{ pagination.to }} 条，共 {{ pagination.total }} 条记录
                    </div>
                    <div class="flex items-center space-x-2">
                        <!-- 上一页按钮 -->
                        <button
                            @click="prevPage"
                            :disabled="pagination.current_page === 1"
                            :class="[
                                'px-3 py-1 rounded-md',
                                pagination.current_page === 1
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500'
                                    : 'bg-white text-blue-600 hover:bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:hover:bg-gray-700'
                            ]"
                        >
                            上一页
                        </button>

                        <!-- 页码按钮 -->
                        <button
                            v-for="page in pageNumbers"
                            :key="page"
                            @click="goToPage(page)"
                            :class="[
                                'px-3 py-1 rounded-md',
                                page === '...'
                                    ? 'bg-white text-gray-600 cursor-default dark:bg-gray-800 dark:text-gray-400'
                                    : page === pagination.current_page
                                        ? 'bg-blue-600 text-white dark:bg-blue-700'
                                        : 'bg-white text-blue-600 hover:bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:hover:bg-gray-700'
                            ]"
                        >
                            {{ page }}
                        </button>

                        <!-- 下一页按钮 -->
                        <button
                            @click="nextPage"
                            :disabled="pagination.current_page === pagination.last_page"
                            :class="[
                                'px-3 py-1 rounded-md',
                                pagination.current_page === pagination.last_page
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500'
                                    : 'bg-white text-blue-600 hover:bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:hover:bg-gray-700'
                            ]"
                        >
                            下一页
                        </button>
                    </div>
                </div>
        </div>

        <!-- 客户表单模态窗口 -->
        <Dialog
            v-model:visible="formVisible"
            :modal="true"
            :style="{width: '70vw', maxWidth: '1000px'}"
            :header="formMode === 'create' ? '新增客户' : (formMode === 'edit' ? '编辑客户' : '查看客户详情')"
            class="custom-dialog rounded-lg shadow-xl dark:bg-gray-800"
            :contentClass="'bg-white dark:bg-gray-800 p-6 rounded-lg'"
            :headerClass="'text-xl font-semibold text-gray-800 dark:text-gray-200 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4'"
            :showHeader="true"
            :showFooter="formMode !== 'view'"
        >
            <div class="p-4" :class="{'pointer-events-none': formMode === 'view', 'view-mode-form': formMode === 'view'}" :style="formMode === 'view' ? 'user-select: none; pointer-events: none;' : ''">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="mb-4">
                        <label for="short_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">客户简称</label>
                        <input
                            type="text"
                            id="short_name"
                            v-model="formData.short_name"
                            :readonly="isFormReadOnly"
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            :class="{'border-red-500 dark:border-red-700': formErrors.short_name, 'form-readonly': isFormReadOnly}"
                        />
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400" v-if="formErrors.short_name">
                            {{ formErrors.short_name[0] }}
                        </p>
                    </div>

                    <div class="mb-4">
                        <label for="full_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">客户全称</label>
                        <input
                            type="text"
                            id="full_name"
                            v-model="formData.full_name"
                            :readonly="isFormReadOnly"
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            :class="{'border-red-500 dark:border-red-700': formErrors.full_name, 'form-readonly': isFormReadOnly}"
                        />
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400" v-if="formErrors.full_name">
                            {{ formErrors.full_name[0] }}
                        </p>
                    </div>

                    <div class="mb-4">
                        <label for="currency_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">交易币种</label>
                        <select
                            id="currency_id"
                            v-model="formData.currency_id"
                            :disabled="isFormReadOnly"
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            :class="{'form-readonly': isFormReadOnly}"
                        >
                            <option value="">选择币种</option>
                            <option v-for="item in currencies" :key="item.id" :value="item.id">{{ item.name }}</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="tax_type_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">税种</label>
                        <select
                            id="tax_type_id"
                            v-model="formData.tax_type_id"
                            :disabled="isFormReadOnly"
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            :class="{'form-readonly': isFormReadOnly}"
                        >
                            <option value="">选择税种</option>
                            <option v-for="item in taxTypes" :key="item.id" :value="item.id">{{ item.name }}</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="trade_term_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">交易条件</label>
                        <select
                            id="trade_term_id"
                            v-model="formData.trade_term_id"
                            :disabled="isFormReadOnly"
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            :class="{'form-readonly': isFormReadOnly}"
                        >
                            <option value="">选择交易条件</option>
                            <option v-for="item in tradeTerms" :key="item.id" :value="item.id">{{ item.name }}</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="pricing_method_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">取价方式</label>
                        <select
                            id="pricing_method_id"
                            v-model="formData.pricing_method_id"
                            :disabled="isFormReadOnly"
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            :class="{'form-readonly': isFormReadOnly}"
                        >
                            <option value="">选择取价方式</option>
                            <option v-for="item in pricingMethods" :key="item.id" :value="item.id">{{ item.name }}</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="invoice_type_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">发票类型</label>
                        <select
                            id="invoice_type_id"
                            v-model="formData.invoice_type_id"
                            :disabled="isFormReadOnly"
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            :class="{'form-readonly': isFormReadOnly}"
                        >
                            <option value="">选择发票类型</option>
                            <option v-for="item in invoiceTypes" :key="item.id" :value="item.id">{{ item.name }}</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="payment_term_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">收款条件</label>
                        <select
                            id="payment_term_id"
                            v-model="formData.payment_term_id"
                            :disabled="isFormReadOnly"
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            :class="{'form-readonly': isFormReadOnly}"
                        >
                            <option value="">选择收款条件</option>
                            <option v-for="item in paymentTerms" :key="item.id" :value="item.id">{{ item.name }}</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="account_receivable_type_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">应收账款类别</label>
                        <select
                            id="account_receivable_type_id"
                            v-model="formData.account_receivable_type_id"
                            :disabled="isFormReadOnly"
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            :class="{'form-readonly': isFormReadOnly}"
                        >
                            <option value="">选择应收账款类别</option>
                            <option v-for="item in accountReceivableTypes" :key="item.id" :value="item.id">{{ item.name }}</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="sales_type_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">销售分类</label>
                        <select
                            id="sales_type_id"
                            v-model="formData.sales_type_id"
                            :disabled="isFormReadOnly"
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            :class="{'form-readonly': isFormReadOnly}"
                        >
                            <option value="">选择销售分类</option>
                            <option v-for="item in salesTypes" :key="item.id" :value="item.id">{{ item.name }}</option>
                        </select>
                    </div>

                   <div class="mb-4">
                        <label for="exchange_rate_basis_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">汇率计算基准</label>
                        <select
                            id="exchange_rate_basis_id"
                            v-model="formData.exchange_rate_basis_id"
                            :disabled="isFormReadOnly"
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            :class="{'form-readonly': isFormReadOnly}"
                        >
                            <option value="">选择汇率计算基准</option>
                            <option v-for="item in exchangeRateBases" :key="item.id" :value="item.id">{{ item.name }}</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="tax_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">税号</label>
                        <input
                            type="text"
                            id="tax_number"
                            v-model="formData.tax_number"
                            :readonly="isFormReadOnly"
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            :class="{'form-readonly': isFormReadOnly}"
                        />
                    </div>

                    <div class="mb-4">
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">状态</label>
                        <select
                            id="status"
                            v-model="formData.status"
                            :disabled="isFormReadOnly"
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            :class="{'form-readonly': isFormReadOnly}"
                        >
                            <option value="">选择状态</option>
                            <option v-for="option in statusOptions" :key="option.value" :value="option.value">{{ option.label }}</option>
                        </select>
                    </div>

                    <div class="mb-4 col-span-1 md:col-span-2">
                        <label for="remarks" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">备注</label>
                        <textarea
                            id="remarks"
                            v-model="formData.remarks"
                            rows="3"
                            :readonly="isFormReadOnly"
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            :class="{'form-readonly': isFormReadOnly}"
                        ></textarea>
                    </div>
                </div>
            </div>

            <template #footer v-if="formMode !== 'view'">
                <div class="flex justify-end space-x-3">
                    <button
                        type="button"
                        @click="formVisible = false"
                        class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200 rounded-md transition-colors duration-200"
                    >
                        取消
                    </button>
                    <button
                        type="button"
                        @click="saveForm"
                        class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors duration-200 flex items-center"
                        :disabled="formLoading"
                    >
                        <span v-if="formLoading" class="mr-2">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        </span>
                        保存
                    </button>
                </div>
            </template>
        </Dialog>
    </AppLayout>
</template>

<style>
/* 自定义对话框样式 */
.custom-dialog .p-dialog-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.custom-dialog .p-dialog-content {
    padding: 0;
}

.custom-dialog .p-dialog-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e2e8f0;
}

/* 暗色模式下的样式 */
.dark .custom-dialog .p-dialog-header {
    background-color: #1e293b;
    color: #f8fafc;
    border-bottom: 1px solid #334155;
}

.dark .custom-dialog .p-dialog-content {
    background-color: #1e293b;
    color: #f8fafc;
}

.dark .custom-dialog .p-dialog-footer {
    background-color: #1e293b;
    border-top: 1px solid #334155;
}

/* 隐藏查看模式下的footer */
.custom-dialog .hidden {
    display: none !important;
}

/* 只读表单元素样式 */
.form-readonly {
    background-color: #e5e7eb !important;
    cursor: not-allowed !important;
    opacity: 1 !important;
    border-color: transparent !important;
    box-shadow: none !important;
    color: #111827 !important;
    font-weight: 500 !important;
}

.dark .form-readonly {
    background-color: #1f2937 !important;
    color: #e5e7eb !important;
}

/* 禁用鼠标事件 */
.pointer-events-none {
    pointer-events: none !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}

/* 查看模式下的表单元素样式 */
.view-mode-form input,
.view-mode-form select,
.view-mode-form textarea {
    pointer-events: none !important;
    user-select: none !important;
    background-color: #e5e7eb !important;
    border-color: transparent !important;
    cursor: default !important;
    opacity: 1 !important;
    color: #111827 !important;
    font-weight: 500 !important;
}

.dark .view-mode-form input,
.dark .view-mode-form select,
.dark .view-mode-form textarea {
    background-color: #1f2937 !important;
    color: #e5e7eb !important;
}

/* 隐藏下拉框的箭头 */
.view-mode-form select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
    padding-right: 0.75rem !important;
}

/* 增强文本框的对比度 */
.view-mode-form input,
.view-mode-form textarea {
    border: 1px solid #d1d5db !important;
}

.dark .view-mode-form input,
.dark .view-mode-form textarea {
    border: 1px solid #4b5563 !important;
}
</style>