<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue';
import { router } from '@inertiajs/vue3';
import apiService from '@/Utils/ApiService';
import AppLayout from '@/Layouts/AppLayout.vue';
import TextInput from '@/Components/TextInput.vue';
import InputLabel from '@/Components/InputLabel.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DialogModal from '@/Components/DialogModal.vue';

// 定义props
interface OrderEditProps {
  orderCode: string;
}

const props = defineProps<OrderEditProps>();

// 物料接口定义
interface Material {
  code: string;
  name: string;
  specification?: string;
  sale_price?: number;
  sale_tax_rate?: number;
  unit?: {
    code: string;
    name: string;
  }
}

// 表单选项数据接口
interface FormData {
  customers: Array<{customer_code: string, customer_name: string}>;
  salesPersons: Array<{account: string, name: string}>;
  departments: Array<{id: number | string, name: string}>;
  salesTypes: Array<{sales_type_code: string, sales_type_name: string}>;
  currencies: Array<{currency_code: string, currency_name: string}>;
  exchangeRateBases: Array<{exchange_rate_base_code: string, exchange_rate_base_name: string}>;
  taxTypes: Array<{tax_type_code: string, tax_type_name: string}>;
  tradeTerms: Array<{trade_term_code: string, trade_term_name: string}>;
  paymentTerms: Array<{receipt_payment_term_code: string, receipt_payment_term_name: string}>;
  invoiceTypes: Array<{invoice_type_code: string, invoice_type_name: string}>;
  pricingMethods: Array<{sales_pricing_method_code: string, sales_pricing_method_name: string}>;
}

// 订单明细项
interface OrderDetailItem {
  material_code: string;
  material_name: string;
  material_spec: string;
  unit_code: string;
  quantity: number;
  unit_price: number;
  price_include_tax: boolean;
  tax_type_code: string;
  tax_rate: number;
  expected_completion_date: string;
  expected_delivery_date: string;
  color_code: string;
  brand: string;
  description: string;
  inner_packing: string;
  outer_packing: string;
  warning: string;
  borrow: string;
  remark: string;
}

// 订单表单
interface OrderFormData {
  company_code: string;
  customer_code: string;
  sales_account: string;
  department_id: string;
  order_date: string;
  sales_type_code: string;
  currency_code: string;
  exchange_rate: number;
  exchange_rate_base_code: string;
  tax_type_code: string;
  trade_term_code: string;
  receipt_payment_term_code: string;
  customer_order_number: string; // 客户订单号
  invoice_type_code: string;
  pricing_method_code: string;
  invoice_title: string;
  invoice_content: string;
  invoice_address: string;
  invoice_phone: string;
  invoice_bank: string;
  remark: string;
  details: OrderDetailItem[];
}

// 初始化表单数据
const formData = ref<FormData>({
  customers: [],
  salesPersons: [],
  departments: [],
  salesTypes: [],
  currencies: [],
  exchangeRateBases: [],
  taxTypes: [],
  tradeTerms: [],
  paymentTerms: [],
  invoiceTypes: [],
  pricingMethods: [],
});

const orderForm = reactive<OrderFormData>({
  company_code: '',
  customer_code: '',
  sales_account: '',
  department_id: '',
  order_date: new Date().toISOString().split('T')[0],
  sales_type_code: '',
  currency_code: '',
  exchange_rate: 1,
  exchange_rate_base_code: '',
  tax_type_code: '',
  trade_term_code: '',
  receipt_payment_term_code: '',
  customer_order_number: '', // 客户订单号
  invoice_type_code: '',
  pricing_method_code: '',
  invoice_title: '',
  invoice_content: '',
  invoice_address: '',
  invoice_phone: '',
  invoice_bank: '',
  remark: '',
  details: []
});

// 物料选择相关状态
const materialDialogVisible = ref<boolean>(false);
const materialSearch = ref<string>('');
const materialList = ref<Material[]>([]);
const materialPage = ref<number>(1);
const materialPerPage = ref<number>(10);
const materialTotal = ref<number>(0);

// 客户选择相关状态
const customerSearch = ref<string>('');
const showCustomerDropdown = ref<boolean>(false);
const filteredCustomers = computed(() => {
  if (!customerSearch.value) {
    return formData.value.customers || [];
  }

  const search = customerSearch.value.toLowerCase();
  return (formData.value.customers || []).filter(
    customer => customer.customer_code.toLowerCase().includes(search) ||
              customer.customer_name.toLowerCase().includes(search)
  );
});

// 处理客户搜索框按键事件
const handleCustomerSearchKeydown = (event: KeyboardEvent): void => {
  // 如果按下回车键且搜索结果只有一个客户
  if (event.key === 'Enter' && filteredCustomers.value.length === 1) {
    event.preventDefault();
    // 自动选中唯一的客户
    orderForm.customer_code = filteredCustomers.value[0].customer_code;
    showCustomerDropdown.value = false;
    handleCustomerChange();
  }
};

// 点击外部关闭下拉菜单
const handleClickOutside = (event: MouseEvent): void => {
  const target = event.target as HTMLElement;
  if (showCustomerDropdown.value && !target.closest('#customer-dropdown')) {
    showCustomerDropdown.value = false;
  }
};

// 错误信息和加载状态
const errors = ref<Record<string, string>>({});
const loading = ref<boolean>(true);
const saving = ref<boolean>(false);

// 加载表单下拉选项数据
const loadFormData = async (): Promise<void> => {
  try {
    // 先获取CSRF令牌
    await apiService.get('/sanctum/csrf-cookie');

    // 获取表单数据和订单数据
    const [formDataResponse, orderDataResponse] = await Promise.all([
      apiService.get('/api/sales/orders/form-data'),
      apiService.get(`/api/sales/orders/${props.orderCode}`)
    ]);

    console.log('表单数据响应:', formDataResponse.data);
    console.log('订单数据响应:', orderDataResponse.data);

    formData.value = formDataResponse.data;

    // 确保salesPersons始终为数组
    if (!Array.isArray(formData.value.salesPersons)) {
      console.warn('salesPersons不是数组，进行修正');
      formData.value.salesPersons = formData.value.salesPersons ? [formData.value.salesPersons] : [];
    }

    // 填充订单数据
    const orderData = orderDataResponse.data.order;
    Object.keys(orderForm).forEach(key => {
      if (key !== 'details' && orderData[key] !== undefined) {
        (orderForm as any)[key] = orderData[key];
      }
    });

    // 处理订单明细，为每个明细项添加材料名称
    if (orderData.details && orderData.details.length > 0) {
      orderForm.details = orderData.details.map((detail: any) => ({
        material_code: detail.material_code,
        material_name: detail.material ? detail.material.name : '',
        material_spec: detail.material ? detail.material.specification : '',
        unit_code: detail.material ? detail.material.unit_code : '',
        quantity: detail.quantity,
        unit_price: detail.unit_price,
        price_include_tax: detail.unit_price === detail.unit_price_tax,
        tax_type_code: detail.tax_type_code,
        tax_rate: detail.tax_rate,
        expected_completion_date: detail.expected_completion_date,
        expected_delivery_date: detail.expected_delivery_date,
        color_code: detail.color_code,
        brand: detail.brand,
        description: detail.description,
        inner_packing: detail.inner_packing,
        outer_packing: detail.outer_packing,
        warning: detail.warning,
        borrow: detail.borrow,
        remark: detail.remark
      }));
    }

    loading.value = false;
  } catch (error: any) {
    console.error('加载订单数据失败', error);
    console.error('错误详情:', error.response?.data || '无响应数据');

    // 设置一些默认空数组，确保UI不会崩溃
    formData.value = {
      customers: [],
      salesPersons: [],
      departments: [],
      salesTypes: [],
      currencies: [],
      exchangeRateBases: [],
      taxTypes: [],
      tradeTerms: [],
      paymentTerms: [],
      invoiceTypes: [],
      pricingMethods: [],
    };

    loading.value = false;
  }
};

// 处理客户变更
const handleCustomerChange = async (): Promise<void> => {
  if (!orderForm.customer_code) return;

  loading.value = true;

  try {
    // 调用API获取客户的默认属性和发票信息
    const response = await apiService.get(`/api/sales/orders/customer-defaults/${orderForm.customer_code}`);

    if (response.data.success) {
      const defaults = response.data.data;

      // 填充客户默认值
      orderForm.sales_type_code = defaults.sales_type_code || orderForm.sales_type_code;
      orderForm.currency_code = defaults.currency_code || orderForm.currency_code;
      orderForm.exchange_rate_base_code = defaults.exchange_rate_base_code || orderForm.exchange_rate_base_code;
      orderForm.tax_type_code = defaults.tax_type_code || orderForm.tax_type_code;
      orderForm.trade_term_code = defaults.trade_term_code || orderForm.trade_term_code;
      orderForm.receipt_payment_term_code = defaults.receipt_payment_term_code || orderForm.receipt_payment_term_code;
      orderForm.invoice_type_code = defaults.invoice_type_code || orderForm.invoice_type_code;
      orderForm.pricing_method_code = defaults.pricing_method_code || orderForm.pricing_method_code;

      // 填充发票相关信息
      orderForm.invoice_title = defaults.invoice_title || '';
      orderForm.invoice_content = defaults.invoice_content || '';
      orderForm.invoice_address = defaults.invoice_address || '';
      orderForm.invoice_phone = defaults.invoice_phone || '';
      orderForm.invoice_bank = defaults.invoice_bank || '';

      // 如果有税率，则更新默认明细的税率
      if (defaults.tax_rate && orderForm.details.length > 0) {
        orderForm.details.forEach(detail => {
          detail.tax_rate = defaults.tax_rate;
          // 需要重新计算价格
          updateDetailPrice(orderForm.details.indexOf(detail));
        });
      }

      // 更新汇率
      updateExchangeRate();
    }
  } catch (error) {
    console.error('获取客户默认信息失败', error);
  } finally {
    loading.value = false;
  }
};

// 更新汇率
const updateExchangeRate = (): void => {
  // 在实际项目中这里可能需要根据币种和基准调用API获取最新汇率
  if (orderForm.currency_code === 'CNY') {
    orderForm.exchange_rate = 1;
  }
};

// 打开物料选择对话框
const openMaterialDialog = (): void => {
  materialDialogVisible.value = true;
  searchMaterials();
};

// 关闭物料选择对话框
const closeMaterialDialog = (): void => {
  materialDialogVisible.value = false;
  materialSearch.value = '';
  materialPage.value = 1;
};

// 搜索物料
const searchMaterials = async (): Promise<void> => {
  try {
    const response = await apiService.get('/api/sales/orders/materials', {
      search: materialSearch.value,
      page: materialPage.value,
      perPage: materialPerPage.value,
    });

    materialList.value = response.data.materials;
    materialTotal.value = response.data.total;
  } catch (error) {
    console.error('搜索物料失败', error);
  }
};

// 物料分页
const nextMaterialPage = (): void => {
  if (materialPage.value < Math.ceil(materialTotal.value / materialPerPage.value)) {
    materialPage.value++;
    searchMaterials();
  }
};

const prevMaterialPage = (): void => {
  if (materialPage.value > 1) {
    materialPage.value--;
    searchMaterials();
  }
};

// 选择物料添加到订单
const selectMaterial = (material: Material): void => {
  // 检查是否已添加该物料
  const existingIndex = orderForm.details.findIndex(d => d.material_code === material.code);

  if (existingIndex >= 0) {
    // 如果已存在，增加数量
    orderForm.details[existingIndex].quantity = orderForm.details[existingIndex].quantity + 1;
    updateDetailPrice(existingIndex);
  } else {
    // 添加新物料到订单明细
    orderForm.details.push({
      material_code: material.code,
      material_name: material.name,
      material_spec: material.specification || '',
      unit_code: material.unit?.code || '',
      quantity: 1,
      unit_price: material.sale_price || 0,
      price_include_tax: false, // 默认单价不含税
      tax_type_code: orderForm.tax_type_code,
      tax_rate: material.sale_tax_rate || 13, // 默认税率13%
      expected_completion_date: '',
      expected_delivery_date: '',
      color_code: '',
      brand: '',
      description: '',
      inner_packing: '',
      outer_packing: '',
      warning: '',
      borrow: '',
      remark: ''
    });
  }

  // 关闭对话框
  closeMaterialDialog();
};

// 删除确认对话框状态
const deleteConfirmVisible = ref<boolean>(false);
const deleteIndex = ref<number>(-1);

// 显示删除确认对话框
const showDeleteConfirm = (index: number): void => {
  deleteIndex.value = index;
  deleteConfirmVisible.value = true;
};

// 确认删除订单明细
const confirmRemoveDetail = (): void => {
  if (deleteIndex.value >= 0) {
    orderForm.details.splice(deleteIndex.value, 1);

    // 关闭确认对话框
    deleteConfirmVisible.value = false;
    deleteIndex.value = -1;
  }
};

// 取消删除
const cancelRemoveDetail = (): void => {
  deleteConfirmVisible.value = false;
  deleteIndex.value = -1;
};

// 处理键盘事件
const handleDeleteKeydown = (event: KeyboardEvent): void => {
  if (event.key === 'Enter' && deleteConfirmVisible.value) {
    // 回车键确认删除
    confirmRemoveDetail();
  } else if (event.key === 'Escape' && deleteConfirmVisible.value) {
    // ESC键取消删除
    cancelRemoveDetail();
  }
};

// 添加和移除键盘事件监听器
const addDeleteKeyListener = (): void => {
  document.addEventListener('keydown', handleDeleteKeydown);
};

const removeDeleteKeyListener = (): void => {
  document.removeEventListener('keydown', handleDeleteKeydown);
};

// 监听deleteConfirmVisible的变化
watch(deleteConfirmVisible, (newValue: boolean) => {
  if (newValue) {
    // 当确认对话框显示时，添加键盘事件监听器
    addDeleteKeyListener();
  } else {
    // 当确认对话框隐藏时，移除键盘事件监听器
    removeDeleteKeyListener();
  }
});

// 删除订单明细项
const removeDetail = (index: number): void => {
  showDeleteConfirm(index);
};

// 更新明细价格计算
const updateDetailPrice = (index: number): void => {
  const detail = orderForm.details[index];

  // 确保数值有效
  detail.quantity = parseFloat(String(detail.quantity)) || 0;
  detail.unit_price = parseFloat(String(detail.unit_price)) || 0;
  detail.tax_rate = parseFloat(String(detail.tax_rate)) || 0;

  // 重新计算价格
  calculateTotalPrice(detail);
};

// 计算订单明细总价
const calculateTotalPrice = (detail: OrderDetailItem): number => {
  const quantity = detail.quantity || 0;
  const unitPrice = detail.unit_price || 0;
  const taxRate = detail.tax_rate || 0;

  // 根据含税标志计算含税总价
  if (detail.price_include_tax) {
    // 如果单价是含税价
    return quantity * unitPrice;
  } else {
    // 如果单价是未税价
    return quantity * unitPrice * (1 + taxRate/100);
  }
};

// 计算订单总金额
const calculateOrderTotal = computed((): number => {
  return orderForm.details.reduce((total, detail) => {
    return total + calculateTotalPrice(detail);
  }, 0);
});

// 订单总数量
const totalItems = computed((): number => {
  return orderForm.details.reduce((sum, item) => {
    return sum + (item.quantity || 0);
  }, 0);
});

// 验证表单
const validateForm = (): boolean => {
  const newErrors: Record<string, string> = {};

  // 基础字段验证
  if (!orderForm.customer_code) newErrors.customer_code = '请选择客户';
  if (!orderForm.sales_account) newErrors.sales_account = '请选择销售员';
  if (!orderForm.department_id) newErrors.department_id = '请选择部门';
  if (!orderForm.order_date) newErrors.order_date = '请选择订单日期';
  if (!orderForm.sales_type_code) newErrors.sales_type_code = '请选择销售类型';
  if (!orderForm.currency_code) newErrors.currency_code = '请选择币种';
  if (!orderForm.exchange_rate_base_code) newErrors.exchange_rate_base_code = '请选择汇率基准';
  if (!orderForm.tax_type_code) newErrors.tax_type_code = '请选择税种';
  if (!orderForm.trade_term_code) newErrors.trade_term_code = '请选择交易条件';
  if (!orderForm.receipt_payment_term_code) newErrors.receipt_payment_term_code = '请选择收款条件';
  if (!orderForm.invoice_type_code) newErrors.invoice_type_code = '请选择发票类型';
  if (!orderForm.pricing_method_code) newErrors.pricing_method_code = '请选择取价方式';

  // 明细验证
  if (orderForm.details.length === 0) {
    newErrors.details = '请至少添加一个产品';
  }

  errors.value = newErrors;
  return Object.keys(newErrors).length === 0;
};

// 更新订单
const updateOrder = async (): Promise<void> => {
  if (!validateForm()) return;

  saving.value = true;

  try {
    // 先获取CSRF令牌
    await apiService.get('/sanctum/csrf-cookie');

    const response = await apiService.put(`/api/sales/orders/update/${props.orderCode}`, orderForm);

    if (response.data.success) {
      router.visit('/sales/orders', {
        onSuccess: () => {
          alert('订单更新成功');
        }
      });
    } else {
      errors.value = response.data.errors || { general: response.data.message };
    }
  } catch (error: any) {
    console.error('更新订单失败', error);

    if (error.response && error.response.data && error.response.data.errors) {
      errors.value = error.response.data.errors;
    } else {
      errors.value = { general: '更新订单失败，请稍后重试' };
    }
  } finally {
    saving.value = false;
  }
};

// 切换税价模式
const togglePriceIncludeTax = (index: number): void => {
  const detail = orderForm.details[index];
  detail.price_include_tax = !detail.price_include_tax;
  updateDetailPrice(index);
};

// 组件挂载时加载数据
onMounted(() => {
  loadFormData();
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<template>
  <AppLayout title="编辑销售订单">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
        编辑销售订单 - {{ orderCode }}
      </h2>
    </template>

    <div class="py-12">
      <div class="w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
          <div v-if="loading" class="flex justify-center items-center py-12">
            <div class="spinner-border text-primary" role="status">
              <span class="sr-only">加载中...</span>
            </div>
            <span class="ml-2">加载订单数据中...</span>
          </div>

          <form v-else @submit.prevent="updateOrder">
            <!-- 订单基本信息表单 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div>
                <InputLabel for="customer_code" value="客户" required />
                <div class="relative" id="customer-dropdown">
                  <button
                    type="button"
                    @click="showCustomerDropdown = !showCustomerDropdown"
                    class="mt-1 block w-full border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-300 p-2.5 rounded-lg focus:ring-indigo-500 dark:focus:ring-indigo-600 focus:border-indigo-500 dark:focus:border-indigo-600 flex justify-between items-center"
                  >
                    <span class="truncate">
                      {{ orderForm.customer_code ?
                        (formData.customers.find(c => c.customer_code === orderForm.customer_code)?.customer_name || '已选择客户') :
                        '请选择客户' }}
                    </span>
                    <svg class="w-4 h-4 ml-2" :class="{'transform rotate-180': showCustomerDropdown}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                  </button>

                  <!-- 客户下拉选择框 -->
                  <div
                    v-if="showCustomerDropdown"
                    class="absolute z-50 mt-1 bg-white rounded-md shadow-lg dark:bg-gray-700 w-full overflow-hidden"
                    style="min-width: 250px; max-height: 70vh;"
                  >
                    <div class="p-2 border-b border-gray-200 dark:border-gray-600">
                      <span class="text-sm font-medium text-gray-700 dark:text-gray-300">选择客户</span>
                    </div>

                    <div class="sticky top-0 z-10 bg-white dark:bg-gray-700 p-2 border-b border-gray-200 dark:border-gray-600">
                      <input
                        type="text"
                        v-model="customerSearch"
                        placeholder="搜索客户..."
                        class="w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm text-gray-900 dark:text-white"
                        @click.stop
                        @keydown="handleCustomerSearchKeydown"
                      />
                    </div>

                    <div class="overflow-y-auto" style="max-height: calc(70vh - 90px);">
                      <div class="p-2">
                        <div
                          v-for="customer in filteredCustomers"
                          :key="customer.customer_code"
                          class="flex items-center p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer"
                          @click="orderForm.customer_code = customer.customer_code; showCustomerDropdown = false; handleCustomerChange();"
                        >
                          <span class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300 truncate">
                            {{ customer.customer_name }}
                          </span>
                        </div>
                        <div v-if="filteredCustomers.length === 0" class="p-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                          暂无匹配客户
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <InputError :message="errors.customer_code" />
              </div>

              <div>
                <InputLabel for="sales_account" value="销售员" required />
                <TextInput
                  id="sales_account"
                  type="text"
                  class="mt-1 block w-full bg-gray-100"
                  :value="formData.salesPersons.find(s => s.account === orderForm.sales_account)?.name || ''"
                  readonly
                />
                <input type="hidden" v-model="orderForm.sales_account" />
                <InputError :message="errors.sales_account" />
              </div>

              <div>
                <InputLabel for="department_id" value="部门" required />
                <TextInput
                  id="department_id"
                  type="text"
                  class="mt-1 block w-full bg-gray-100"
                  :value="formData.departments.find(d => d.id === orderForm.department_id)?.name || ''"
                  readonly
                />
                <input type="hidden" v-model="orderForm.department_id" />
                <InputError :message="errors.department_id" />
              </div>

              <div>
                <InputLabel for="order_date" value="订单日期" required />
                <TextInput
                  id="order_date"
                  type="date"
                  class="mt-1 block w-full"
                  v-model="orderForm.order_date"
                  required
                />
                <InputError :message="errors.order_date" />
              </div>

              <div>
                <InputLabel for="sales_type_code" value="销售类型" required />
                <select
                  id="sales_type_code"
                  v-model="orderForm.sales_type_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  required
                >
                  <option value="">请选择销售类型</option>
                  <option v-for="salesType in formData.salesTypes" :key="salesType.sales_type_code" :value="salesType.sales_type_code">
                    {{ salesType.sales_type_name }}
                  </option>
                </select>
                <InputError :message="errors.sales_type_code" />
              </div>

              <div>
                <InputLabel for="currency_code" value="币种" required />
                <select
                  id="currency_code"
                  v-model="orderForm.currency_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  required
                  @change="updateExchangeRate"
                >
                  <option value="">请选择币种</option>
                  <option v-for="currency in formData.currencies" :key="currency.currency_code" :value="currency.currency_code">
                    {{ currency.currency_name }}
                  </option>
                </select>
                <InputError :message="errors.currency_code" />
              </div>

              <div>
                <InputLabel for="exchange_rate" value="汇率" required />
                <TextInput
                  id="exchange_rate"
                  type="number"
                  step="0.0001"
                  class="mt-1 block w-full"
                  :model-value="String(orderForm.exchange_rate)"
                  @update:model-value="val => orderForm.exchange_rate = parseFloat(val) || 0"
                  required
                />
                <InputError :message="errors.exchange_rate" />
              </div>

              <div>
                <InputLabel for="exchange_rate_base_code" value="汇率基准" required />
                <select
                  id="exchange_rate_base_code"
                  v-model="orderForm.exchange_rate_base_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  required
                >
                  <option value="">请选择汇率基准</option>
                  <option v-for="base in formData.exchangeRateBases" :key="base.exchange_rate_base_code" :value="base.exchange_rate_base_code">
                    {{ base.exchange_rate_base_name }}
                  </option>
                </select>
                <InputError :message="errors.exchange_rate_base_code" />
              </div>

              <div>
                <InputLabel for="tax_type_code" value="税种" required />
                <select
                  id="tax_type_code"
                  v-model="orderForm.tax_type_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  required
                >
                  <option value="">请选择税种</option>
                  <option v-for="taxType in formData.taxTypes" :key="taxType.tax_type_code" :value="taxType.tax_type_code">
                    {{ taxType.tax_type_name }}
                  </option>
                </select>
                <InputError :message="errors.tax_type_code" />
              </div>

              <div>
                <InputLabel for="trade_term_code" value="交易条件" required />
                <select
                  id="trade_term_code"
                  v-model="orderForm.trade_term_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  required
                >
                  <option value="">请选交易条件</option>
                  <option v-for="term in formData.tradeTerms" :key="term.trade_term_code" :value="term.trade_term_code">
                    {{ term.trade_term_name }}
                  </option>
                </select>
                <InputError :message="errors.trade_term_code" />
              </div>

              <div>
                <InputLabel for="receipt_payment_term_code" value="收款条件" required />
                <select
                  id="receipt_payment_term_code"
                  v-model="orderForm.receipt_payment_term_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  required
                >
                  <option value="">请选择收款条件</option>
                  <option v-for="term in formData.paymentTerms" :key="term.receipt_payment_term_code" :value="term.receipt_payment_term_code">
                    {{ term.receipt_payment_term_name }}
                  </option>
                </select>
                <InputError :message="errors.receipt_payment_term_code" />
              </div>

              <div>
                <InputLabel for="invoice_type_code" value="发票类型" required />
                <select
                  id="invoice_type_code"
                  v-model="orderForm.invoice_type_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  required
                >
                  <option value="">请选择发票类型</option>
                  <option v-for="type in formData.invoiceTypes" :key="type.invoice_type_code" :value="type.invoice_type_code">
                    {{ type.invoice_type_name }}
                  </option>
                </select>
                <InputError :message="errors.invoice_type_code" />
              </div>

              <div>
                <InputLabel for="pricing_method_code" value="取价方式" required />
                <select
                  id="pricing_method_code"
                  v-model="orderForm.pricing_method_code"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  required
                >
                  <option value="">请选择取价方式</option>
                  <option v-for="method in formData.pricingMethods" :key="method.sales_pricing_method_code" :value="method.sales_pricing_method_code">
                    {{ method.sales_pricing_method_name }}
                  </option>
                </select>
                <InputError :message="errors.pricing_method_code" />
              </div>

              <div>
                <InputLabel for="remark" value="备注" />
                <textarea
                  id="remark"
                  v-model="orderForm.remark"
                  class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                  rows="4"
                  placeholder="请输入备注信息"
                ></textarea>
                <InputError :message="errors.remark" />
              </div>
            </div>

            <!-- 订单明细 -->
            <div class="mb-6">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">订单明细</h3>
                <div class="flex items-center space-x-3">
                  <div class="text-sm">
                    <span class="text-gray-600 dark:text-gray-400">总件数: </span>
                    <span class="font-medium">{{ totalItems.toFixed(2) }}</span>
                  </div>
                  <PrimaryButton type="button" @click="openMaterialDialog">添加物料</PrimaryButton>
                </div>
              </div>

              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">序号</th>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">物料编码</th>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">物料名称</th>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">规格</th>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">数量</th>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">单位</th>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">单价</th>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">含税</th>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">税率(%)</th>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">含税总价</th>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">交货日期</th>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">操作</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <tr v-for="(detail, index) in orderForm.details" :key="index">
                      <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ index + 1 }}</td>
                      <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ detail.material_code }}</td>
                      <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ detail.material_name }}</td>
                      <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ detail.material_spec }}</td>
                      <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <TextInput
                          type="number"
                          step="0.01"
                          class="w-20"
                          :model-value="String(detail.quantity)"
                          @update:model-value="val => { detail.quantity = parseFloat(val) || 0; updateDetailPrice(index); }"
                          min="0.01"
                        />
                      </td>
                      <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {{ detail.unit_code }}
                      </td>
                      <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <TextInput
                          type="number"
                          step="0.01"
                          class="w-24"
                          :model-value="String(detail.unit_price)"
                          @update:model-value="val => { detail.unit_price = parseFloat(val) || 0; updateDetailPrice(index); }"
                        />
                      </td>
                      <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <input
                          type="checkbox"
                          v-model="detail.price_include_tax"
                          @change="updateDetailPrice(index)"
                          class="rounded border-gray-300 dark:border-gray-700 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                        />
                      </td>
                      <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <TextInput
                          type="number"
                          step="0.01"
                          class="w-16"
                          :model-value="String(detail.tax_rate)"
                          @update:model-value="val => { detail.tax_rate = parseFloat(val) || 0; updateDetailPrice(index); }"
                        />
                      </td>
                      <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {{ calculateTotalPrice(detail).toFixed(2) }}
                      </td>
                      <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <TextInput
                          type="date"
                          class="w-32"
                          v-model="detail.expected_delivery_date"
                        />
                      </td>
                      <td class="px-4 py-2 whitespace-nowrap text-sm font-medium">
                        <button type="button" @click="showDeleteConfirm(index)" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                          删除
                        </button>
                      </td>
                    </tr>
                    <tr v-if="orderForm.details.length === 0">
                      <td colspan="12" class="px-4 py-2 text-center text-gray-500 dark:text-gray-400">
                        请添加订单明细
                      </td>
                    </tr>
                    <tr v-if="orderForm.details.length > 0" class="bg-gray-50 dark:bg-gray-700">
                      <td colspan="9" class="px-4 py-2 text-right font-medium">合计金额:</td>
                      <td class="px-4 py-2 whitespace-nowrap text-sm font-bold text-gray-900 dark:text-gray-100">
                        {{ calculateOrderTotal.toFixed(2) }} {{ orderForm.currency_code }}
                      </td>
                      <td colspan="2"></td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <InputError :message="errors.details" />
            </div>

            <!-- 保存按钮 -->
            <div class="flex justify-end mt-6 space-x-3">
              <SecondaryButton type="button" @click="router.visit('/sales/orders')">
                取消
              </SecondaryButton>
              <PrimaryButton type="submit" :disabled="saving">
                {{ saving ? '保存中...' : '保存订单' }}
              </PrimaryButton>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 物料选择对话框 -->
    <DialogModal :show="materialDialogVisible" max-width="2xl" :closeable="false">
      <template #title>
        选择物料
      </template>
      <template #content>
        <div class="mt-4">
          <div class="flex items-center mb-4">
            <TextInput
              type="search"
              placeholder="搜索物料编码、名称或规格"
              class="flex-1 mr-2"
              v-model="materialSearch"
            />
            <button
              type="button"
              @click="searchMaterials"
              class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            >
              搜索
            </button>
          </div>

          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">编码</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">名称</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">规格</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">单位</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                <tr v-for="material in materialList" :key="material.code" class="hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ material.code }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ material.name }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ material.specification }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ material.unit?.code }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      type="button"
                      @click="selectMaterial(material)"
                      class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-200"
                    >
                      选择
                    </button>
                  </td>
                </tr>
                <tr v-if="materialList.length === 0">
                  <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                    {{ materialSearch ? '没有找到匹配的物料，请修改搜索条件' : '请输入搜索条件查找物料' }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div v-if="materialList.length > 0" class="flex justify-between items-center mt-4">
            <div class="text-sm text-gray-700 dark:text-gray-300">
              显示 {{ materialList.length }} 条，共 {{ materialTotal }} 条
            </div>
            <div class="flex space-x-2">
              <button
                type="button"
                @click="prevMaterialPage"
                :disabled="materialPage === 1"
                class="px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-md disabled:opacity-50"
              >
                上一页
              </button>
              <button
                type="button"
                @click="nextMaterialPage"
                :disabled="materialPage >= Math.ceil(materialTotal / materialPerPage)"
                class="px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-md disabled:opacity-50"
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <SecondaryButton @click="closeMaterialDialog">
          取消
        </SecondaryButton>
      </template>
    </DialogModal>

    <!-- 删除确认对话框 -->
    <div v-if="deleteConfirmVisible" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- 背景遮罩 -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="cancelRemoveDetail"></div>

        <!-- 定位弹框到订单明细位置 -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
             :style="deleteIndex >= 0 && orderForm.details[deleteIndex] ?
                    { position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)' } :
                    {}">
          <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900 sm:mx-0 sm:h-10 sm:w-10">
                <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                  确认删除
                </h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    您确定要删除这条订单明细吗？此操作无法撤销。
                  </p>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    <span class="inline-flex items-center">
                      <span class="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded text-gray-700 dark:text-gray-300 font-mono">Enter</span>
                      <span class="ml-1">确认删除</span>
                    </span>
                    <span class="inline-flex items-center ml-3">
                      <span class="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded text-gray-700 dark:text-gray-300 font-mono">Esc</span>
                      <span class="ml-1">取消</span>
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
              @click="confirmRemoveDetail"
            >
              确认删除
            </button>
            <button
              type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              @click="cancelRemoveDetail"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.highlight-row {
  background-color: rgba(79, 70, 229, 0.1) !important;
}
</style>