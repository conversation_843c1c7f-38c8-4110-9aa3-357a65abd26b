<script setup lang="ts">
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref } from 'vue';

interface Contract {
    id: number;
    code: string;
    customer_name: string;
    amount: string;
    status: '待评审' | '已评审' | '评审通过';
    created_at: string;
}

const contracts = ref<Contract[]>([
    { id: 1, code: 'HT-2023-001', customer_name: '上海某某科技有限公司', amount: '￥500,000', status: '待评审', created_at: '2023-09-15' },
    { id: 2, code: 'HT-2023-002', customer_name: '北京某某制造有限公司', amount: '￥320,000', status: '已评审', created_at: '2023-10-02' },
    { id: 3, code: 'HT-2023-003', customer_name: '广州某某贸易有限公司', amount: '￥180,000', status: '评审通过', created_at: '2023-10-18' },
]);
</script>

<template>
    <AppLayout title="合同评审">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">合同评审</h2>
                <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
                    新增合同
                </button>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                合同编号
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                客户名称
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                合同金额
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                状态
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                创建日期
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <tr v-for="contract in contracts" :key="contract.id">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ contract.code }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ contract.customer_name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ contract.amount }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                                <span :class="{
                                    'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300': contract.status === '待评审',
                                    'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300': contract.status === '已评审',
                                    'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300': contract.status === '评审通过'
                                }" class="px-2 py-1 text-xs rounded-full">
                                    {{ contract.status }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ contract.created_at }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">查看</button>
                                <button class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3">评审</button>
                                <button class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </AppLayout>
</template> 