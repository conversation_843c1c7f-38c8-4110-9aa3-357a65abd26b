<script setup lang="ts">
import AppLayout from '@/Layouts/AppLayout.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import DialogModal from '@/Components/DialogModal.vue';
import Dialog from 'primevue/dialog';
import { ref, onMounted, reactive, computed, watch, onBeforeUnmount } from 'vue';
import axios, { AxiosError } from 'axios';
import { Link, router, usePage } from '@inertiajs/vue3';
import apiService from '@/Utils/ApiService';
import { useToast } from 'primevue/usetoast';
import { PageProps, User } from '@/types';
import { debounce } from '@/Utils/DebounceUtil';

// 定义类型接口
interface Pagination {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  from: number;
  to: number;
}

interface ApiResponse {
  status: string;
  message?: string;
  data?: any;
  pagination?: Pagination;
}

interface OrderFilters {
  companyCode: string;
  customerCode: string[];
  startDate: string;
  endDate: string;
  status: string;
}

interface Company {
  company_code: string;
  company_name: string;
}

interface CustomerOption {
  customer_code: string;
  customer_name: string;
}

interface Customer {
  id?: number;
  customer_code?: string;
  customer_name: string;
}

interface OrderDetail {
  id: number;
  order_item: number;
  material_code: string;
  material?: {
    material_name: string;
  };
  quantity: number;
  unit_price: number;
  tax_rate: number;
  total_price_tax: number;
}

interface SalesPerson {
  account: string;
  name: string;
}

interface Order {
  order_code: string;
  customer?: Customer;
  sales_account: string;
  sales_person?: SalesPerson;
  order_date: string;
  status: string;
  currency_code: string;
  details: OrderDetail[];
}

interface ApiResponseList {
  status: string;
  data: Order[];
  message?: string;
  pagination: Pagination;
}

interface ApiResponseDetail {
  order: Order;
}

// API错误接口定义
interface ApiError {
  response?: {
    status?: number;
    data?: {
      message?: string;
      errors?: Record<string, string[]>;
      status?: string;
    }
  };
  message?: string;
}

const toast = useToast();

// 订单列表数据
const orders = ref<Order[]>([]);

// 分页相关
const pagination = ref<Pagination>({
  total: 0,
  per_page: 20,
  current_page: 1,
  last_page: 0,
  from: 0,
  to: 0
});

// 搜索相关
const search = ref<string>('');
// 使用导入的debounce函数
const searchDebounced = debounce(() => {
  pagination.value.current_page = 1; // 搜索时重置到第一页
  getOrders();
}, 500);

// 监听搜索框变化
watch(search, () => {
  searchDebounced();
});

// 下拉选项数据
const companies = ref<Company[]>([]);
const customers = ref<CustomerOption[]>([]);

// 下拉菜单显示控制
const showCompanyDropdown = ref<boolean>(false);
const showCustomerDropdown = ref<boolean>(false);

// 认证和UI状态
const loading = ref<boolean>(true);
const error = ref<string | null>(null);
const debugInfo = ref<string | null>(null);

// 获取当前用户信息
const page = computed(() => {
  try {
    return usePage<PageProps>();
  } catch (e) {
    console.error('获取页面数据错误:', e);
    return { props: { auth: { user: {} } } } as any;
  }
});
const user = computed(() => {
  try {
    return page.value.props.auth.user;
  } catch (e) {
    console.error('获取用户数据错误:', e);
    return {} as User;
  }
});

// 获取当前日期，格式化为YYYY-MM-DD
const getCurrentDate = (): string => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 获取当年1月1日，格式化为YYYY-MM-DD
const getFirstDayOfYear = (): string => {
  const year = new Date().getFullYear();
  return `${year}-01-01`;
};

// 筛选条件
const filters = reactive<OrderFilters>({
  companyCode: '',
  customerCode: [],
  startDate: getFirstDayOfYear(), // 默认为当年1月1日
  endDate: getCurrentDate(), // 默认为当前日期
  status: '',
});

// 添加排序相关变量
const sortField = ref<string>('order_date');
const sortDirection = ref<'asc' | 'desc'>('desc');

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '未审核', value: 'N' },
  { label: '已审核', value: 'Y' },
  { label: '已结案', value: 'C' },
  { label: '已作废', value: 'X' }
];

// 对话框控制
const confirmDialogVisible = ref<boolean>(false);
const confirmDialogTitle = ref<string>('');
const confirmDialogMessage = ref<string>('');
const confirmAction = ref<((param: string) => Promise<void>) | null>(null);
const confirmParam = ref<string | null>(null);

const viewDialogVisible = ref<boolean>(false);
const currentOrder = ref<Order | null>(null);

// 监听筛选条件变化
watch(() => filters.companyCode, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    console.log('选中公司变化，重新获取数据:', newVal);
    pagination.value.current_page = 1; // 重置到第一页
    getOrders(); // 重新获取数据
    // 公司变化时，重新获取该公司的客户列表
    fetchCustomers();
  }
});

// 监听状态筛选变化
watch(() => filters.status, () => {
  pagination.value.current_page = 1; // 重置到第一页
  getOrders();
});

// 使用debounce函数处理日期变化
const dateChangeDebounced = debounce(() => {
  pagination.value.current_page = 1; // 重置到第一页
  getOrders();
}, 500);

// 监听日期变化
watch(() => [filters.startDate, filters.endDate], () => {
  dateChangeDebounced();
}, { deep: true });

// 监听客户选择变化
watch(() => filters.customerCode, () => {
  pagination.value.current_page = 1; // 重置到第一页
  getOrders();
}, { deep: true });

// 获取用户可访问的公司列表
const fetchUserCompanies = async (): Promise<void> => {
  try {
    const response = await apiService.get('user/companies');
    if (response.data && response.data.length > 0) {
      companies.value = response.data;

      // 设置默认公司为用户默认公司
      if (user.value?.default_company_code && !filters.companyCode) {
        filters.companyCode = user.value.default_company_code;
      } else if (companies.value.length > 0 && !filters.companyCode) {
        // 如果没有默认公司，则使用第一个公司
        filters.companyCode = companies.value[0].company_code;
      }
    }
  } catch (err) {
    console.error('获取用户公司列表失败:', err);
    toast.add({ severity: 'error', summary: '错误', detail: '获取公司列表失败', life: 3000 });
  }
};

// 获取订单列表
const getOrders = async (): Promise<void> => {
  try {
    loading.value = true;
    error.value = null;

    // 准备请求参数
    const params: Record<string, any> = {
      page: pagination.value.current_page,
      per_page: pagination.value.per_page,
      status: filters.status,
      start_date: filters.startDate,
      end_date: filters.endDate,
      sortField: sortField.value,
      sortDirection: sortDirection.value,
      _t: new Date().getTime() // 防止缓存
    };

    // 添加公司筛选参数
    if (filters.companyCode) {
      params.company_code = filters.companyCode;
    }

    // 添加客户筛选参数
    if (filters.customerCode && filters.customerCode.length > 0) {
      params.customer_code = filters.customerCode;
    }

    // 添加搜索参数
    if (search.value) {
      params.search = search.value;
    }

    console.log('请求参数:', params);
    const response = await apiService.get('/sales/orders/getList', params);
    console.log('API返回数据:', response.data);

    const apiResponse = response.data as unknown as ApiResponse;

    if (apiResponse && apiResponse.status === 'success') {
      orders.value = apiResponse.data;
      if (apiResponse.pagination) {
        pagination.value = apiResponse.pagination;
      }
      error.value = null;
    } else {
      error.value = '获取数据失败: ' + (apiResponse.message || '未知错误');
      debugInfo.value = JSON.stringify(apiResponse || {});
    }
  } catch (err: unknown) {
    const apiError = err as ApiError;
    console.error('获取订单列表失败', apiError);
    const errMessage = apiError.response?.data?.message || '未知错误';

    error.value = `获取订单列表失败: ${errMessage}`;

    if (apiError.response?.status === 401) {
      toast.add({ severity: 'error', summary: '认证失败', detail: '认证已过期，请重新登录', life: 5000 });
    } else {
      toast.add({ severity: 'error', summary: '数据加载失败', detail: errMessage, life: 5000 });
    }
  } finally {
    loading.value = false;
  }
};

// 点击页面其他地方关闭下拉列表
const handleClickOutside = (event: MouseEvent): void => {
  const companyDropdown = document.getElementById('company-dropdown');
  if (companyDropdown && !companyDropdown.contains(event.target as Node)) {
    showCompanyDropdown.value = false;
  }

  const customerDropdown = document.getElementById('customer-dropdown');
  if (customerDropdown && !customerDropdown.contains(event.target as Node)) {
    showCustomerDropdown.value = false;
  }
};

// 计算属性：页码列表
const pageNumbers = computed<(number | string)[]>(() => {
  const current = pagination.value.current_page;
  const last = pagination.value.last_page;
  const delta = 2; // 当前页前后显示的页数
  const pages: (number | string)[] = [];

  // 始终显示第一页
  pages.push(1);

  // 计算当前页附近要显示的页码
  for (let i = Math.max(2, current - delta); i <= Math.min(last - 1, current + delta); i++) {
    pages.push(i);
  }

  // 如果有必要，添加省略号
  if (current - delta > 2) pages.splice(1, 0, '...');
  if (current + delta < last - 1) pages.push('...');

  // 如果最后一页不是第一页，则添加最后一页
  if (last > 1) pages.push(last);

  return pages;
});

// 切换页码
const goToPage = (page: number | string): void => {
  if (page === '...' || page === pagination.value.current_page) return;
  pagination.value.current_page = typeof page === 'string' ? parseInt(page) : page;
  getOrders();
};

// 上一页
const prevPage = (): void => {
  if (pagination.value.current_page > 1) {
    pagination.value.current_page--;
    getOrders();
  }
};

// 下一页
const nextPage = (): void => {
  if (pagination.value.current_page < pagination.value.last_page) {
    pagination.value.current_page++;
    getOrders();
  }
};

// 切换每页显示数量
const changePerPage = (perPage: number) => {
    pagination.value.per_page = perPage;
    pagination.value.current_page = 1; // 重置到第一页
    getOrders();
};

// 查看订单详情
const viewOrder = async (orderCode: string): Promise<void> => {
  try {
    const response = await apiService.get(`/sales/orders/${orderCode}`);
    const apiResponse = response.data as unknown as ApiResponse;

    if (apiResponse && apiResponse.status === 'success' && apiResponse.data) {
      currentOrder.value = apiResponse.data.order || apiResponse.data;
      viewDialogVisible.value = true;
    } else {
      toast.add({ severity: 'error', summary: '错误', detail: '获取订单详情失败', life: 3000 });
    }
  } catch (err: unknown) {
    const apiError = err as ApiError;
    console.error('获取订单详情失败', apiError);
    const errMessage = apiError.response?.data?.message || '未知错误';
    toast.add({ severity: 'error', summary: '错误', detail: `获取订单详情失败: ${errMessage}`, life: 3000 });
  }
};

// 格式化日期
const formatDate = (dateStr: string): string => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 获取状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case 'N': return '未审核';
    case 'Y': return '已审核';
    case 'C': return '已结案';
    case 'X': return '已作废';
    default: return '未知状态';
  }
};

// 获取状态样式
const getStatusClass = (status: string): string => {
  switch (status) {
    case 'N': return 'px-2 py-1 rounded-full text-sm bg-yellow-100 text-yellow-800 dark:bg-yellow-700 dark:text-yellow-100';
    case 'Y': return 'px-2 py-1 rounded-full text-sm bg-green-100 text-green-800 dark:bg-green-700 dark:text-green-100';
    case 'C': return 'px-2 py-1 rounded-full text-sm bg-blue-100 text-blue-800 dark:bg-blue-700 dark:text-blue-100';
    case 'X': return 'px-2 py-1 rounded-full text-sm bg-red-100 text-red-800 dark:bg-red-700 dark:text-red-100';
    default: return 'px-2 py-1 rounded-full text-sm bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100';
  }
};

// 确认删除
const confirmDelete = (orderCode: string): void => {
  confirmDialogTitle.value = '确认删除';
  confirmDialogMessage.value = `确定要删除订单 ${orderCode} 吗？此操作不可恢复。`;
  confirmAction.value = deleteOrder;
  confirmParam.value = orderCode;
  confirmDialogVisible.value = true;
};

// 确认审核
const confirmApprove = (orderCode: string): void => {
  confirmDialogTitle.value = '确认审核';
  confirmDialogMessage.value = `确定要审核订单 ${orderCode} 吗？审核后将无法修改订单信息。`;
  confirmAction.value = approveOrder;
  confirmParam.value = orderCode;
  confirmDialogVisible.value = true;
};

// 确认结案
const confirmClose = (orderCode: string): void => {
  confirmDialogTitle.value = '确认结案';
  confirmDialogMessage.value = `确定要结案订单 ${orderCode} 吗？结案后订单将不再参与业务流程。`;
  confirmAction.value = closeOrder;
  confirmParam.value = orderCode;
  confirmDialogVisible.value = true;
};

// 确认作废
const confirmVoid = (orderCode: string): void => {
  confirmDialogTitle.value = '确认作废';
  confirmDialogMessage.value = `确定要作废订单 ${orderCode} 吗？作废后订单将不可恢复。`;
  confirmAction.value = cancelOrder;
  confirmParam.value = orderCode;
  confirmDialogVisible.value = true;
};

// 处理确认
const handleConfirm = (): void => {
  if (confirmAction.value && confirmParam.value) {
    confirmAction.value(confirmParam.value);
    confirmDialogVisible.value = false;
  }
};

// 跳转到创建订单页面
const openCreateDialog = (): void => {
  // 传递当前选中的公司代码
  router.visit(`/sales/orders/create?company_code=${filters.companyCode}`);
};

// 获取客户列表
const fetchCustomers = async (): Promise<void> => {
  try {
    const params: Record<string, any> = {};
    if (filters.companyCode) {
      params.company_code = filters.companyCode;
    }

    const response = await apiService.get('/sales/customers/getList', params);
    const apiResponse = response.data as unknown as ApiResponse;

    if (apiResponse && apiResponse.status === 'success' && apiResponse.data) {
      customers.value = apiResponse.data;
    } else {
      toast.add({
        severity: 'error',
        summary: '错误',
        detail: '获取客户列表失败',
        life: 3000
      });
    }
  } catch (err: unknown) {
    const apiError = err as ApiError;
    console.error('获取客户列表失败', apiError);
    toast.add({
      severity: 'error',
      summary: '错误',
      detail: '获取客户列表失败',
      life: 3000
    });
  }
};

// 切换客户选择
const toggleCustomerSelection = (customerCode: string): void => {
  const index = filters.customerCode.indexOf(customerCode);
  if (index === -1) {
    filters.customerCode.push(customerCode);
  } else {
    filters.customerCode.splice(index, 1);
  }
};

// 验证认证状态并加载数据
const validateAndLoad = async (): Promise<void> => {
  try {
    // 验证token有效性
    await apiService.get('/user');

    // 加载下拉选项数据
    await Promise.all([
      fetchUserCompanies(),
      fetchCustomers()
    ]);

    // 加载订单数据
    await getOrders();
  } catch (error: any) {
    console.error('认证验证失败', error);
    if (error?.response?.status === 401) {
      error.value = '您的登录已过期，请重新登录';
      toast.add({ severity: 'error', summary: '认证失败', detail: '认证已过期，请重新登录', life: 5000 });

      // 重定向到登录页
      setTimeout(() => {
        window.location.href = '/login';
      }, 2000);
    } else {
      error.value = '加载数据时发生错误，请稍后再试';
    }
  } finally {
    loading.value = false;
  }
};

// 生命周期钩子
onMounted(async () => {
  await validateAndLoad();

  // 添加点击事件监听器，用于关闭下拉菜单
  document.addEventListener('click', handleClickOutside);
});

// 组件卸载前移除事件监听器
onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});

// 添加排序处理函数
const handleSort = (field: string): void => {
  if (sortField.value === field) {
    // 如果已经按照这个字段排序，切换排序方向
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    // 否则，设置新的排序字段，默认升序
    sortField.value = field;
    sortDirection.value = 'asc';
  }

  // 重置到第一页并重新获取数据
  pagination.value.current_page = 1;
  getOrders();
};

// API操作函数
const approveOrder = async (orderCode: string): Promise<void> => {
  try {
    const response = await apiService.put(`/sales/orders/${orderCode}/approve`);
    const apiResponse = response.data as unknown as ApiResponse;

    if (apiResponse && apiResponse.status === 'success') {
      toast.add({ severity: 'success', summary: '成功', detail: '订单已成功审核', life: 3000 });
      getOrders();
    } else {
      toast.add({
        severity: 'error',
        summary: '错误',
        detail: apiResponse?.message || '审核订单失败',
        life: 3000
      });
    }
  } catch (err: unknown) {
    const apiError = err as ApiError;
    console.error('审核订单失败', apiError);
    const errMessage = apiError.response?.data?.message || '未知错误';
    toast.add({ severity: 'error', summary: '错误', detail: `审核订单失败: ${errMessage}`, life: 3000 });
  }
};

const closeOrder = async (orderCode: string): Promise<void> => {
  try {
    const response = await apiService.put(`/sales/orders/${orderCode}/close`);
    const apiResponse = response.data as unknown as ApiResponse;

    if (apiResponse && apiResponse.status === 'success') {
      toast.add({ severity: 'success', summary: '成功', detail: '订单已成功结案', life: 3000 });
      getOrders();
    } else {
      toast.add({
        severity: 'error',
        summary: '错误',
        detail: apiResponse?.message || '结案订单失败',
        life: 3000
      });
    }
  } catch (err: unknown) {
    const apiError = err as ApiError;
    console.error('结案订单失败', apiError);
    const errMessage = apiError.response?.data?.message || '未知错误';
    toast.add({ severity: 'error', summary: '错误', detail: `结案订单失败: ${errMessage}`, life: 3000 });
  }
};

const cancelOrder = async (orderCode: string): Promise<void> => {
  try {
    const response = await apiService.put(`/sales/orders/${orderCode}/cancel`);
    const apiResponse = response.data as unknown as ApiResponse;

    if (apiResponse && apiResponse.status === 'success') {
      toast.add({ severity: 'success', summary: '成功', detail: '订单已成功作废', life: 3000 });
      getOrders();
    } else {
      toast.add({
        severity: 'error',
        summary: '错误',
        detail: apiResponse?.message || '作废订单失败',
        life: 3000
      });
    }
  } catch (err: unknown) {
    const apiError = err as ApiError;
    console.error('作废订单失败', apiError);
    const errMessage = apiError.response?.data?.message || '未知错误';
    toast.add({ severity: 'error', summary: '错误', detail: `作废订单失败: ${errMessage}`, life: 3000 });
  }
};

const deleteOrder = async (orderCode: string): Promise<void> => {
  try {
    const response = await apiService.delete(`/sales/orders/${orderCode}`);
    const apiResponse = response.data as unknown as ApiResponse;

    if (apiResponse && apiResponse.status === 'success') {
      toast.add({ severity: 'success', summary: '成功', detail: '订单已成功删除', life: 3000 });
      getOrders();
    } else {
      toast.add({
        severity: 'error',
        summary: '错误',
        detail: apiResponse?.message || '删除订单失败',
        life: 3000
      });
    }
  } catch (err: unknown) {
    const apiError = err as ApiError;
    console.error('删除订单失败', apiError);
    const errMessage = apiError.response?.data?.message || '未知错误';
    toast.add({ severity: 'error', summary: '错误', detail: `删除订单失败: ${errMessage}`, life: 3000 });
  }
};
</script>

<template>
  <AppLayout title="销售订单管理">
    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
      <div class="flex flex-col space-y-6">
        <!-- 页头 -->
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">销售订单管理</h2>
          <button
            @click="openCreateDialog"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-300 flex items-center"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            新增订单
          </button>
        </div>

        <!-- 筛选工具栏 -->
        <div class="mb-4 flex flex-wrap gap-2">
          <!-- 公司下拉列表 -->
          <div class="relative" id="company-dropdown">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">公司</label>
            <button
                @click="showCompanyDropdown = !showCompanyDropdown"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 flex justify-between items-center"
                style="min-width: 150px"
            >
                <span>{{ companies.find(c => c.company_code === filters.companyCode)?.company_name || '选择公司' }}</span>
                <svg class="w-4 h-4 ml-2" :class="{'transform rotate-180': showCompanyDropdown}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>

            <!-- 下拉内容 -->
            <div v-if="showCompanyDropdown" class="fixed z-50 mt-1 bg-white rounded-md shadow-lg dark:bg-gray-700"
                  style="min-width: 150px; top: auto; left: auto;">
                <div class="p-2 border-b border-gray-200 dark:border-gray-600">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">选择公司</span>
                </div>
                <div class="overflow-y-auto p-2" style="max-height: 300px; min-height: 100px;">
                    <div
                        v-for="company in companies"
                        :key="company.company_code"
                        class="flex items-center p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer"
                        @click="filters.companyCode = company.company_code; showCompanyDropdown = false;"
                    >
                        <span class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
                            {{ company.company_name }}
                        </span>
                    </div>
                    <!-- 无数据显示 -->
                    <div v-if="companies.length === 0" class="p-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                        暂无数据
                    </div>
                </div>
            </div>
          </div>

          <!-- 客户选择 -->
          <div class="relative" id="customer-dropdown">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">客户</label>
            <button
              @click="showCustomerDropdown = !showCustomerDropdown"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 flex justify-between items-center"
              style="min-width: 150px"
            >
              <span class="truncate">
                {{ filters.customerCode.length === 0 ? '全部客户' :
                   filters.customerCode.length === 1 ?
                   (customers.find(c => c.customer_code === filters.customerCode[0])?.customer_name || '已选择1个客户') :
                   `已选择${filters.customerCode.length}个客户` }}
              </span>
              <svg class="w-4 h-4 ml-2" :class="{'transform rotate-180': showCustomerDropdown}" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <!-- 下拉选择框 -->
            <div
              v-if="showCustomerDropdown"
              class="fixed z-50 mt-1 bg-white rounded-md shadow-lg dark:bg-gray-700"
              style="min-width: 250px; top: auto; left: auto;"
            >
              <div class="p-2 border-b border-gray-200 dark:border-gray-600">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">选择客户</span>
              </div>
              <div class="sticky top-0 bg-white dark:bg-gray-700 p-2 border-b border-gray-200 dark:border-gray-600">
                <input
                  type="text"
                  v-model="search"
                  placeholder="搜索客户..."
                  class="w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm"
                />
              </div>
              <div class="overflow-y-auto p-2" style="max-height: 300px; min-height: 100px;">
                <div
                  v-for="customer in customers"
                  :key="customer.customer_code"
                  class="flex items-center p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer"
                >
                  <input
                    type="checkbox"
                    :id="`customer-${customer.customer_code}`"
                    :checked="filters.customerCode.includes(customer.customer_code)"
                    @change="toggleCustomerSelection(customer.customer_code)"
                    class="mr-2"
                  />
                  <label :for="`customer-${customer.customer_code}`" class="cursor-pointer truncate flex-1 text-sm font-medium text-gray-900 dark:text-gray-300">
                    {{ customer.customer_name }}
                  </label>
                </div>
                <div v-if="customers.length === 0" class="p-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                  暂无客户数据
                </div>
              </div>
            </div>
          </div>

          <!-- 日期范围选择 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">订单日期</label>
            <input
              type="date"
              v-model="filters.startDate"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
              style="min-width: 150px"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">结束日期</label>
            <input
              type="date"
              v-model="filters.endDate"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
              style="min-width: 150px"
            />
          </div>

          <!-- 状态选择 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">订单状态</label>
            <select
              v-model="filters.status"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
              style="min-width: 150px"
            >
              <option v-for="status in statusOptions" :key="status.value" :value="status.value">
                {{ status.label }}
              </option>
            </select>
          </div>

          <!-- 每页显示数量 -->
          <div class="flex items-center ml-auto pl-2">
              <span class="text-sm text-gray-700 dark:text-gray-300">每页显示:</span>
              <select
                  v-model="pagination.per_page"
                  @change="changePerPage(pagination.per_page)"
                  class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2 ml-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
              >
                  <option value="20">20</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
              </select>
          </div>

        </div>


        <!-- 加载状态显示 -->
        <div v-if="loading" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-10">
          <div class="bg-white dark:bg-gray-800 p-6 rounded-md shadow-lg flex flex-col items-center">
            <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600 dark:text-gray-300">加载中...</p>
          </div>
        </div>

        <!-- 错误信息显示 -->
        <div v-if="error" class="text-center py-4">
          <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <p class="font-bold">错误</p>
            <p>{{ error }}</p>
          </div>
          <button @click="getOrders" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
            重试
          </button>
        </div>

        <!-- 数据表格 -->
        <div v-if="!error" class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="px-6 py-3 text-left">
                  <div @click="handleSort('order_code')" class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer flex items-center">
                    订单编号
                    <svg v-if="sortField === 'order_code'" class="ml-1 w-4 h-4" :class="{'transform rotate-180': sortDirection === 'desc'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                    </svg>
                  </div>
                </th>
                <th scope="col" class="px-6 py-3 text-left">
                  <div @click="handleSort('customer_name')" class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer flex items-center">
                    客户
                    <svg v-if="sortField === 'customer_name'" class="ml-1 w-4 h-4" :class="{'transform rotate-180': sortDirection === 'desc'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                    </svg>
                  </div>
                </th>
                <th scope="col" class="px-6 py-3 text-left">
                  <div @click="handleSort('order_date')" class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer flex items-center">
                    订单日期
                    <svg v-if="sortField === 'order_date'" class="ml-1 w-4 h-4" :class="{'transform rotate-180': sortDirection === 'desc'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                    </svg>
                  </div>
                </th>
                <th scope="col" class="px-6 py-3 text-left">
                  <div @click="handleSort('sales_account')" class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer flex items-center">
                    业务员
                    <svg v-if="sortField === 'sales_account'" class="ml-1 w-4 h-4" :class="{'transform rotate-180': sortDirection === 'desc'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                    </svg>
                  </div>
                </th>
                <th scope="col" class="px-6 py-3 text-left">
                  <div @click="handleSort('status')" class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer flex items-center">
                    状态
                    <svg v-if="sortField === 'status'" class="ml-1 w-4 h-4" :class="{'transform rotate-180': sortDirection === 'desc'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                    </svg>
                  </div>
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="order in orders" :key="order.order_code" class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-200">
                  {{ order.order_code }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                  {{ order.customer?.customer_name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                  {{ formatDate(order.order_date) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                  {{ order.sales_person?.name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getStatusClass(order.status)">
                    {{ getStatusText(order.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button @click="viewOrder(order.order_code)" class="px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                    查看
                  </button>
                  <Link v-if="order.status === 'N'" :href="`/sales/orders/${order.order_code}/edit`" class="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                    编辑
                  </Link>
                  <button v-if="order.status === 'N'" @click="confirmApprove(order.order_code)" class="px-2 py-1 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors">
                    审核
                  </button>
                  <button v-if="order.status === 'Y'" @click="confirmClose(order.order_code)" class="px-2 py-1 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors">
                    结案
                  </button>
                  <button v-if="order.status !== 'X'" @click="confirmVoid(order.order_code)" class="px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                    作废
                  </button>
                </td>
              </tr>
              <!-- 无数据显示 -->
              <tr v-if="orders.length === 0 && !loading">
                <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                  暂无数据
                </td>
              </tr>
            </tbody>
          </table>

          <!-- 分页控件 -->
          <div v-if="pagination.total > 0" class="flex items-center justify-between mt-4">
            <div class="text-sm text-gray-700 dark:text-gray-300">
              显示 {{ pagination.from }} 到 {{ pagination.to }} 条，共 {{ pagination.total }} 条记录
            </div>
            <div class="flex items-center space-x-2">
              <!-- 上一页按钮 -->
              <button
                @click="prevPage"
                :disabled="pagination.current_page === 1"
                :class="[
                  'px-3 py-1 rounded-md',
                  pagination.current_page === 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500'
                    : 'bg-white text-blue-600 hover:bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:hover:bg-gray-700'
                ]"
              >
                上一页
              </button>

              <!-- 页码按钮 -->
              <button
                v-for="page in pageNumbers"
                :key="page"
                @click="goToPage(page)"
                :class="[
                  'px-3 py-1 rounded-md',
                  page === '...'
                    ? 'bg-white text-gray-600 cursor-default dark:bg-gray-800 dark:text-gray-400'
                    : page === pagination.current_page
                      ? 'bg-blue-600 text-white dark:bg-blue-700'
                      : 'bg-white text-blue-600 hover:bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:hover:bg-gray-700'
                ]"
              >
                {{ page }}
              </button>

              <!-- 下一页按钮 -->
              <button
                @click="nextPage"
                :disabled="pagination.current_page === pagination.last_page"
                :class="[
                  'px-3 py-1 rounded-md',
                  pagination.current_page === pagination.last_page
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500'
                    : 'bg-white text-blue-600 hover:bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:hover:bg-gray-700'
                ]"
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <DialogModal :show="confirmDialogVisible" :title="confirmDialogTitle" @close="confirmDialogVisible = false">
      <template #content>
        <p class="text-gray-700 dark:text-gray-300">{{ confirmDialogMessage }}</p>
      </template>
      <template #footer>
        <div class="flex justify-end space-x-3">
          <SecondaryButton @click="confirmDialogVisible = false" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500">
            取消
          </SecondaryButton>
          <PrimaryButton @click="handleConfirm" class="bg-blue-600 hover:bg-blue-700 text-white">
            确定
          </PrimaryButton>
        </div>
      </template>
    </DialogModal>

    <!-- 查看订单详情对话框 -->
    <Dialog
      v-model:visible="viewDialogVisible"
      :modal="true"
      :style="{width: '80vw', maxWidth: '1000px'}"
      header="订单详情"
      class="custom-dialog rounded-lg shadow-xl dark:bg-gray-800"
      :contentClass="'bg-white dark:bg-gray-800 p-6 rounded-lg'"
      :headerClass="'text-xl font-semibold text-gray-800 dark:text-gray-200 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4'"
      :showHeader="true"
    >
      <div v-if="currentOrder" class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <div class="mb-4">
              <p class="text-sm text-gray-500 dark:text-gray-400">订单编号</p>
              <p class="text-lg font-medium text-gray-800 dark:text-gray-200">{{ currentOrder.order_code }}</p>
            </div>
            <div class="mb-4">
              <p class="text-sm text-gray-500 dark:text-gray-400">客户</p>
              <p class="text-lg font-medium text-gray-800 dark:text-gray-200">{{ currentOrder.customer?.customer_name }}</p>
            </div>
            <div class="mb-4">
              <p class="text-sm text-gray-500 dark:text-gray-400">业务员</p>
              <p class="text-lg font-medium text-gray-800 dark:text-gray-200">{{ currentOrder.sales_person?.name }}</p>
            </div>
          </div>
          <div>
            <div class="mb-4">
              <p class="text-sm text-gray-500 dark:text-gray-400">订单日期</p>
              <p class="text-lg font-medium text-gray-800 dark:text-gray-200">{{ formatDate(currentOrder.order_date) }}</p>
            </div>
            <div class="mb-4">
              <p class="text-sm text-gray-500 dark:text-gray-400">状态</p>
              <span :class="getStatusClass(currentOrder.status)" class="text-lg">
                {{ getStatusText(currentOrder.status) }}
              </span>
            </div>
            <div class="mb-4">
              <p class="text-sm text-gray-500 dark:text-gray-400">币种</p>
              <p class="text-lg font-medium text-gray-800 dark:text-gray-200">{{ currentOrder.currency_code }}</p>
            </div>
          </div>
        </div>

        <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4">订单明细</h3>
        <div class="overflow-x-auto bg-gray-50 dark:bg-gray-700 rounded-lg">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
            <thead class="bg-gray-100 dark:bg-gray-600">
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  行号
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  料号
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  品名
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  数量
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  单价
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  税率
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  含税金额
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
              <tr v-for="detail in currentOrder.details" :key="detail.id" class="hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                  {{ detail.order_item }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                  {{ detail.material_code }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                  {{ detail.material?.material_name }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                  {{ detail.quantity }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                  {{ detail.unit_price }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                  {{ detail.tax_rate }}%
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                  {{ detail.total_price_tax }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end p-4 border-t border-gray-200 dark:border-gray-700">
          <button
            @click="viewDialogVisible = false"
            class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors"
          >
            关闭
          </button>
        </div>
      </template>
    </Dialog>
  </AppLayout>
</template>

<style scoped>
/* 表格样式优化 */
.overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 4px;
}

.dark .overflow-x-auto::-webkit-scrollbar-track {
  background: #374151;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #9ca3af;
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 按钮动效 */
button {
  transition: all 0.2s ease;
}

button:active {
  transform: scale(0.95);
}

/* Dialog 样式 */
.custom-dialog :deep(.p-dialog-header) {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.custom-dialog :deep(.p-dialog-content) {
  padding: 0;
}

.dark .custom-dialog :deep(.p-dialog-header) {
  background: #1f2937;
  color: #e5e7eb;
  border-bottom-color: #374151;
}

.dark .custom-dialog :deep(.p-dialog-content) {
  background: #1f2937;
  color: #e5e7eb;
}

.dark .custom-dialog :deep(.p-dialog-footer) {
  background: #1f2937;
  border-top-color: #374151;
}

/* 状态标签样式 */
:deep(.bg-yellow-100) {
  background-color: rgba(254, 243, 199, 1);
}
:deep(.bg-green-100) {
  background-color: rgba(209, 250, 229, 1);
}
:deep(.bg-blue-100) {
  background-color: rgba(219, 234, 254, 1);
}
:deep(.bg-red-100) {
  background-color: rgba(254, 226, 226, 1);
}

.dark :deep(.bg-yellow-100) {
  background-color: rgba(146, 123, 67, 0.3);
}
.dark :deep(.bg-green-100) {
  background-color: rgba(74, 222, 128, 0.2);
}
.dark :deep(.bg-blue-100) {
  background-color: rgba(59, 130, 246, 0.2);
}
.dark :deep(.bg-red-100) {
  background-color: rgba(248, 113, 113, 0.2);
}
</style>