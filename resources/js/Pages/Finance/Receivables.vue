<script setup lang="ts">
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref } from 'vue';

interface Receivable {
    id: number;
    code: string;
    customer_name: string;
    invoice_code: string;
    amount: string;
    due_date: string;
    status: '未付款' | '部分付款' | '已付款' | '逾期';
    aging: '30天内' | '31-60天' | '61-90天' | '90天以上' | '已结清';
}

interface StatusClasses {
    [key: string]: string;
}

interface AgingClasses {
    [key: string]: string;
}

const receivables = ref<Receivable[]>([
    { id: 1, code: 'AR-2023-001', customer_name: '上海某某科技有限公司', invoice_code: 'INV-2023-001', amount: '￥120,000', due_date: '2023-12-20', status: '未付款', aging: '30天内' },
    { id: 2, code: 'AR-2023-002', customer_name: '北京某某制造有限公司', invoice_code: 'INV-2023-002', amount: '￥80,000', due_date: '2023-11-15', status: '部分付款', aging: '31-60天' },
    { id: 3, code: 'AR-2023-003', customer_name: '广州某某贸易有限公司', invoice_code: 'INV-2023-003', amount: '￥150,000', due_date: '2023-10-30', status: '已付款', aging: '已结清' },
    { id: 4, code: 'AR-2023-004', customer_name: '深圳某某电子有限公司', invoice_code: 'INV-2023-004', amount: '￥200,000', due_date: '2023-09-30', status: '逾期', aging: '61-90天' },
]);

const statusClasses: StatusClasses = {
    '未付款': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    '部分付款': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    '已付款': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    '逾期': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
};

const agingClasses: AgingClasses = {
    '30天内': 'text-green-600 dark:text-green-400',
    '31-60天': 'text-yellow-600 dark:text-yellow-400',
    '61-90天': 'text-orange-600 dark:text-orange-400',
    '90天以上': 'text-red-600 dark:text-red-400',
    '已结清': 'text-gray-500 dark:text-gray-400',
};
</script>

<template>
    <AppLayout title="应收账款">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">应收账款</h2>
                <div class="flex space-x-2">
                    <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
                        新增账款
                    </button>
                    <button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md">
                        导出报表
                    </button>
                </div>
            </div>
            
            <!-- 汇总统计 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg shadow">
                    <div class="text-blue-800 dark:text-blue-200 text-sm font-medium">总应收金额</div>
                    <div class="text-blue-900 dark:text-blue-100 text-2xl font-bold">￥550,000</div>
                </div>
                <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg shadow">
                    <div class="text-green-800 dark:text-green-200 text-sm font-medium">已收金额</div>
                    <div class="text-green-900 dark:text-green-100 text-2xl font-bold">￥150,000</div>
                </div>
                <div class="bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg shadow">
                    <div class="text-yellow-800 dark:text-yellow-200 text-sm font-medium">待收金额</div>
                    <div class="text-yellow-900 dark:text-yellow-100 text-2xl font-bold">￥200,000</div>
                </div>
                <div class="bg-red-50 dark:bg-red-900 p-4 rounded-lg shadow">
                    <div class="text-red-800 dark:text-red-200 text-sm font-medium">逾期金额</div>
                    <div class="text-red-900 dark:text-red-100 text-2xl font-bold">￥200,000</div>
                </div>
            </div>
            
            <div class="mb-4">
                <div class="flex space-x-2">
                    <input type="text" placeholder="搜索账款..." class="rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 flex-grow">
                    <select class="rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <option value="">全部状态</option>
                        <option value="未付款">未付款</option>
                        <option value="部分付款">部分付款</option>
                        <option value="已付款">已付款</option>
                        <option value="逾期">逾期</option>
                    </select>
                    <button class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md">
                        搜索
                    </button>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                账款编号
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                客户名称
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                发票编号
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                金额
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                到期日
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                状态
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                账龄
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <tr v-for="receivable in receivables" :key="receivable.id">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ receivable.code }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ receivable.customer_name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ receivable.invoice_code }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ receivable.amount }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ receivable.due_date }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                                <span :class="statusClasses[receivable.status]" class="px-2 py-1 text-xs rounded-full">
                                    {{ receivable.status }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm" :class="agingClasses[receivable.aging]">
                                {{ receivable.aging }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">查看</button>
                                <button class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3">收款</button>
                                <button class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="mt-4 flex justify-end">
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">
                        <span class="sr-only">上一页</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">1</a>
                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">
                        <span class="sr-only">下一页</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                </nav>
            </div>
        </div>
    </AppLayout>
</template> 