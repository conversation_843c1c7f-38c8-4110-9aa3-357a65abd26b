<script setup lang="ts">
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref, onMounted, computed, watch } from 'vue';
import apiService from '@/Utils/ApiService';
import { usePage } from '@inertiajs/vue3';
import { PageProps, User } from '@/types';
import { debounce } from '@/Utils/DebounceUtil';

// 自定义料件项类型，扩展全局的Material接口
interface MaterialItem {
    id: string | number; // 可以是料号或ID
    material_code: string;
    figure: string;
    product_name: string;
    specification: string;
    category_code: string;
    category_name: string;
}

// 分页接口
interface Pagination {
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
    from: number;
    to: number;
}

// 公司接口
interface Company {
    company_code: string;
    company_name: string;
}

// 产品分类接口
interface ProductCategory {
    value: string;
    label: string;
}

// 分页相关
const pagination = ref<Pagination>({
    total: 0,
    per_page: 20,
    current_page: 1,
    last_page: 0,
    from: 0,
    to: 0
});

// 搜索相关
const search = ref<string>('');

// 定义请求参数接口
interface RequestParams {
    page: number;
    per_page: number;
    _t: number;
    search?: string;
    sort_field?: string;
    sort_direction?: string;
    categories?: string;
    company_code?: string;
}

// 使用导入的debounce函数
const searchDebounced = debounce((newVal: string) => {
    pagination.value.current_page = 1; // 搜索时重置到第一页
    fetchFinishedProducts();
}, 500);

// 监听搜索框变化
watch(search, (newVal) => {
    searchDebounced(newVal);
});

// 公司相关
const companies = ref<Company[]>([]); // 存储用户可访问的公司列表
const selectedCompany = ref<string>(''); // 当前选中的公司
const showCompanyDropdown = ref<boolean>(false); // 控制公司下拉列表显示

// 获取当前用户信息
const page = computed(() => {
    try {
        return usePage<PageProps>();
    } catch (e) {
        console.error('获取页面数据错误:', e);
        return { props: { auth: { user: {} } } } as any;
    }
});
const user = computed(() => {
    try {
        return page.value.props.auth.user;
    } catch (e) {
        console.error('获取用户数据错误:', e);
        return {} as User;
    }
});

// 监听选中公司变化
watch(selectedCompany, (newVal, oldVal) => {
    if (newVal !== oldVal) {
        console.log('选中公司变化，重新获取产品分类:', newVal);
        selectedCategories.value = []; // 清空已选产品分类
        fetchProductCategories(); // 重新获取产品分类
        pagination.value.current_page = 1; // 重置到第一页
        fetchFinishedProducts(); // 重新获取数据
    }
});

// 获取用户可访问的公司列表
const fetchUserCompanies = async (): Promise<void> => {
    try {
        const response = await apiService.get('user/companies');
        if (response.data && response.data.length > 0) {
            companies.value = response.data;
            
            // 设置默认公司为用户默认公司
            if (user.value?.default_company_code && !selectedCompany.value) {
                selectedCompany.value = user.value.default_company_code;
            } else if (companies.value.length > 0 && !selectedCompany.value) {
                // 如果没有默认公司，则使用第一个公司
                selectedCompany.value = companies.value[0].company_code;
            }
        }
    } catch (err) {
        console.error('获取用户公司列表失败:', err);
    }
};

// 产品分类相关
const productCategories = ref<ProductCategory[]>([]); // 存储所有产品分类
const selectedCategories = ref<string[]>([]); // 存储选中的产品分类
const showCategoryDropdown = ref<boolean>(false); // 控制下拉列表显示

// 监听选中分类变化
watch(selectedCategories, (newVal, oldVal) => {
    // 仅当数组内容实际变化时才刷新数据
    if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        console.log('产品分类选择变化，重新获取数据:', newVal);
        pagination.value.current_page = 1; // 筛选时重置到第一页
        fetchFinishedProducts();
    }
}, { deep: true });

// 获取产品分类数据
const fetchProductCategories = async (): Promise<void> => {
    try {
        // 添加公司参数到请求中
        const params: Record<string, any> = {};
        if (selectedCompany.value) {
            params.company_code = selectedCompany.value;
        }
        
        const response = await apiService.get('materials/product-categories', params);
        if (response.data) {
            productCategories.value = response.data.map((category: {value: string, label: string}) => ({
                value: category.value,
                label: `${category.value} - ${category.label}`
            }));
        }
    } catch (err) {
        console.error('获取产品分类失败:', err);
    }
};

// 处理分类选择
const toggleCategory = (categoryValue: string): void => {
    // 使用nextTick确保DOM更新
    const index = selectedCategories.value.indexOf(categoryValue);
    // 创建新数组触发响应式更新
    if (index === -1) {
        // 选中：添加到数组
        selectedCategories.value = [...selectedCategories.value, categoryValue];
    } else {
        // 取消选中：从数组中移除
        selectedCategories.value = selectedCategories.value.filter(v => v !== categoryValue);
    }
    
    // 确保在toggleCategory后立即更新数据
    pagination.value.current_page = 1;
    fetchFinishedProducts();
};

// 清空所有选中分类
const clearCategories = (): void => {
    selectedCategories.value = [];
    // 立即刷新数据
    pagination.value.current_page = 1;
    fetchFinishedProducts();
};

// 全选所有分类
const selectAllCategories = (): void => {
    // 创建新数组以确保响应式更新
    selectedCategories.value = [...productCategories.value.map(category => category.value)];
    // 立即刷新数据
    pagination.value.current_page = 1;
    fetchFinishedProducts();
};

// 选中分类的文本展示
const selectedCategoriesText = computed((): string => {
    if (selectedCategories.value.length === 0) {
        return '产品分类';
    } else if (selectedCategories.value.length === 1) {
        const category = productCategories.value.find(c => c.value === selectedCategories.value[0]);
        return category ? category.label : '产品分类';
    } else {
        return `已选 ${selectedCategories.value.length} 项`;
    }
});

// 计算属性：页码列表
const pageNumbers = computed((): (number | string)[] => {
    const current = pagination.value.current_page;
    const last = pagination.value.last_page;
    const delta = 2; // 当前页前后显示的页数
    const pages: (number | string)[] = [];
    
    // 始终显示第一页
    pages.push(1);
    
    // 计算当前页附近要显示的页码
    for (let i = Math.max(2, current - delta); i <= Math.min(last - 1, current + delta); i++) {
        pages.push(i);
    }
    
    // 如果有必要，添加省略号
    if (current - delta > 2) pages.splice(1, 0, '...');
    if (current + delta < last - 1) pages.push('...');
    
    // 如果最后一页不是第一页，则添加最后一页
    if (last > 1) pages.push(last);
    
    return pages;
});

// 定义缺失的变量
const items = ref<MaterialItem[]>([]);
const loading = ref<boolean>(false);
const error = ref<string | null>(null);
const debugInfo = ref<any>(null);
const useTestRoute = ref<boolean>(false);

// 添加排序相关变量
const sortField = ref<string>('');
const sortDirection = ref<string>('asc');

// 从API获取成品列表
const fetchFinishedProducts = async (retryCount = 0): Promise<void> => {
    loading.value = true;
    
    // 保存下拉列表的当前状态，防止刷新后状态被重置
    const wasCategoryDropdownOpen = showCategoryDropdown.value;
    const wasCompanyDropdownOpen = showCompanyDropdown.value;
    
    try {
        // 准备请求参数
        const params: RequestParams = {
            page: pagination.value.current_page,
            per_page: pagination.value.per_page,
            _t: new Date().getTime() // 防止缓存
        };
            
        // 添加搜索参数
        if (search.value) {
            params.search = search.value;
        }
        
        // 添加排序参数
        if (sortField.value) {
            params.sort_field = sortField.value;
            params.sort_direction = sortDirection.value;
        }
        
        // 添加产品分类筛选参数
        if (selectedCategories.value.length > 0) {
            params.categories = selectedCategories.value.join(',');
        }
        
        // 添加公司筛选参数
        if (selectedCompany.value) {
            params.company_code = selectedCompany.value;
        }

        console.log('请求参数:', params);
        
        // 使用apiService发送请求
        const response = await apiService.get('materials', params);
        
        console.log('API返回数据:', response.data);
        
        if (response.data && response.data.status === 'success') {
            // 更新分页信息
            pagination.value = response.data.pagination;
            
            // 将API返回的成品数据转换为表格所需的格式
            items.value = response.data.data.map((product: any) => ({  
                figure: product.figure, // 图号
                id: product.material_code, // 使用料号作为ID
                material_code: product.material_code, // 料号作为编号
                product_name: product.product_name, // 品名
                specification: product.specification, // 规格说明
                category_code: product.category_code, // 产品分类代码
                category_name: product.category_name, // 产品分类名称
            }));
            
            // 成功获取数据，清除错误信息
            error.value = null;
            loading.value = false;
            
            // 恢复下拉列表的状态
            showCategoryDropdown.value = wasCategoryDropdownOpen;
            showCompanyDropdown.value = wasCompanyDropdownOpen;
            
            return;
        } else {
            error.value = '获取数据失败: ' + (response.data?.message || '未知错误');
            debugInfo.value = JSON.stringify(response.data || {});
            loading.value = false;
            
            // 恢复下拉列表的状态
            showCategoryDropdown.value = wasCategoryDropdownOpen;
            showCompanyDropdown.value = wasCompanyDropdownOpen;
        }
    } catch (err: any) {
        console.error('获取成品列表失败:', err);
        error.value = '获取数据失败: ' + (err.message || '未知错误');
        loading.value = false;
        
        // 恢复下拉列表的状态
        showCategoryDropdown.value = wasCategoryDropdownOpen;
        showCompanyDropdown.value = wasCompanyDropdownOpen;
        
        // 如果请求失败，重试一次
        if (retryCount < 2) {
            setTimeout(() => {
                fetchFinishedProducts(retryCount + 1);
            }, 1000); // 延迟1秒后重试
        }
    }
};

// 切换排序方式
const toggleSort = (field: string): void => {
    // 如果点击的是当前排序字段，则切换排序方向
    if (field === sortField.value) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
    } else {
        // 否则，设置新的排序字段，默认升序
        sortField.value = field;
        sortDirection.value = 'asc';
    }
    
    // 重置到第一页并重新获取数据
    pagination.value.current_page = 1;
    fetchFinishedProducts();
};

// 辅助函数：获取cookie值
const getCookie = (name: string): string | null => {
    const match = document.cookie.match(new RegExp('(^|;\\s*)(' + name + ')=([^;]*)'));
    return match ? decodeURIComponent(match[3]) : null;
};

// 切换页码
const goToPage = (page: number | string): void => {
    if (page === '...' || page === pagination.value.current_page) return;
    pagination.value.current_page = Number(page);
    fetchFinishedProducts();
};

// 下一页
const nextPage = (): void => {
    if (pagination.value.current_page < pagination.value.last_page) {
        pagination.value.current_page++;
        fetchFinishedProducts();
    }
};

// 上一页
const prevPage = (): void => {
    if (pagination.value.current_page > 1) {
        pagination.value.current_page--;
        fetchFinishedProducts();
    }
};

// 切换每页显示数量
const changePerPage = (perPage: number): void => {
    pagination.value.per_page = perPage;
    pagination.value.current_page = 1; // 重置到第一页
    fetchFinishedProducts();
};

// 切换API路由并重新获取数据
const toggleTestRoute = (): void => {
    useTestRoute.value = !useTestRoute.value;
    fetchFinishedProducts();
};

// 点击页面其他地方关闭下拉列表
const handleClickOutside = (event: MouseEvent): void => {
    const categoryDropdown = document.getElementById('category-dropdown');
    if (categoryDropdown && !categoryDropdown.contains(event.target as Node)) {
        showCategoryDropdown.value = false;
    }
    
    const companyDropdown = document.getElementById('company-dropdown');
    if (companyDropdown && !companyDropdown.contains(event.target as Node)) {
        showCompanyDropdown.value = false;
    }
};

// 组件挂载时加载数据
onMounted(() => {
    console.log('料件管理组件已挂载，获取数据');
    fetchUserCompanies(); // 获取用户可访问的公司列表
    // 注意：fetchProductCategories和fetchFinishedProducts会在selectedCompany变化时自动调用
    
    // 添加点击外部关闭下拉列表的事件监听
    document.addEventListener('click', handleClickOutside);
});

// 组件卸载时移除事件监听
const onBeforeUnmount = (): void => {
    document.removeEventListener('click', handleClickOutside);
};
</script>

<template>
    <AppLayout title="料件管理">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">料件管理</h2>
                <div class="flex items-center space-x-4 " v-if="false">
                    <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
                        添加料件
                    </button>
                </div>
            </div>
            
            <!-- 搜索栏 -->
            <div class="mb-4 flex items-center">
                <!-- 公司下拉列表 -->
                <div class="relative" id="company-dropdown">
                    <button 
                        @click="showCompanyDropdown = !showCompanyDropdown" 
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 flex justify-between items-center"
                        style="min-width: 150px"
                    >
                        <span>{{ companies.find(c => c.company_code === selectedCompany)?.company_name || '选择公司' }}</span>
                        <svg class="w-4 h-4 ml-2" :class="{'transform rotate-180': showCompanyDropdown}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    
                    <!-- 下拉内容 -->
                    <div v-if="showCompanyDropdown" class="fixed z-50 mt-1 bg-white rounded-md shadow-lg dark:bg-gray-700" 
                         style="min-width: 150px; top: auto; left: auto;">
                        <div class="p-2 border-b border-gray-200 dark:border-gray-600">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">选择公司</span>
                        </div>
                        <div class="overflow-y-auto p-2" style="max-height: 300px; min-height: 100px;">
                            <div 
                                v-for="company in companies" 
                                :key="company.company_code"
                                class="flex items-center p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer"
                                @click="selectedCompany = company.company_code; showCompanyDropdown = false;"
                            >
                                <span class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
                                    {{ company.company_name }}
                                </span>
                            </div>
                            <!-- 无数据显示 -->
                            <div v-if="companies.length === 0" class="p-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                                暂无数据
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 产品分类下拉列表 -->
                <div class="relative ml-2" id="category-dropdown">
                    <button 
                        @click="showCategoryDropdown = !showCategoryDropdown" 
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 flex justify-between items-center"
                        style="min-width: 270px"
                    >
                        <span>{{ selectedCategoriesText }}</span>
                        <svg class="w-4 h-4 ml-2" :class="{'transform rotate-180': showCategoryDropdown}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    
                    <!-- 下拉内容 -->
                    <div v-if="showCategoryDropdown" class="fixed z-50 mt-1 bg-white rounded-md shadow-lg dark:bg-gray-700"
                         style="min-width: 270px; top: auto; left: auto;">
                        <div class="p-2 border-b border-gray-200 dark:border-gray-600 flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">产品分类</span>
                            <button 
                                @click="clearCategories" 
                                class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                            >
                                清空
                            </button>
                            <button 
                                @click="selectAllCategories"
                                class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                            >
                                全选
                            </button>
                        </div>
                        <div class="overflow-y-auto p-2" style="max-height: 400px; min-height: 100px;">
                            <div 
                                v-for="category in productCategories" 
                                :key="category.value"
                                class="flex items-center p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer"
                                @click.prevent="toggleCategory(category.value)"
                            >
                                <input 
                                    type="checkbox" 
                                    :id="`category-${category.value}`"
                                    :checked="selectedCategories.includes(category.value)" 
                                    @click.stop="toggleCategory(category.value)"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                    autocomplete="off"
                                >
                                <label 
                                    :for="`category-${category.value}`"
                                    class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300 w-full cursor-pointer"
                                    @click.stop.prevent="toggleCategory(category.value)"
                                >
                                    {{ category.label }}
                                </label>
                            </div>
                            <!-- 无数据显示 -->
                            <div v-if="productCategories.length === 0" class="p-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                                暂无数据
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 搜索框 -->
                <div class="relative flex-1 ml-2" style="max-width: 50%;">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                        </svg>
                    </div>
                    <input 
                        v-model="search" 
                        type="search" 
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" 
                        placeholder="搜索图号、料号、品名或规格..."
                    >
                </div>
                
                <!-- 每页显示数量 -->
                <div class="flex items-center ml-auto pl-2">
                    <span class="text-sm text-gray-700 dark:text-gray-300">每页显示:</span>
                    <select 
                        v-model="pagination.per_page" 
                        @change="changePerPage(pagination.per_page)"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2 ml-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                    >
                        <option value="20">20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>
            </div>
            
            <!-- 选中的分类标签展示 -->
            <div v-if="selectedCategories.length > 0" class="mb-4 flex flex-wrap gap-2">
                <div 
                    v-for="categoryValue in selectedCategories" 
                    :key="categoryValue"
                    class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300 flex items-center"
                >
                    {{ productCategories.find(c => c.value === categoryValue)?.label || categoryValue }}
                    <button 
                        @click.stop="toggleCategory(categoryValue)" 
                        class="ml-1.5 text-blue-800 dark:text-blue-300 hover:text-blue-900 dark:hover:text-blue-200"
                    >
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- 加载状态显示 -->
            <div v-if="loading" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-10">
                <div class="bg-white dark:bg-gray-800 p-6 rounded-md shadow-lg flex flex-col items-center">
                    <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-500 dark:text-gray-400">正在加载数据...</p>
                </div>
            </div>
            
            <!-- 错误信息显示 -->
            <div v-if="error" class="text-center py-4">
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <p class="font-bold">错误</p>
                    <p>{{ error }}</p>
                    <p v-if="debugInfo" class="mt-2 text-xs text-gray-600 overflow-auto max-h-32 bg-gray-100 p-2 rounded">
                        调试信息: {{ debugInfo }}
                    </p>
                </div>
                <button 
                    @click="() => fetchFinishedProducts(0)" 
                    class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
                >
                    重试
                </button>
            </div>
            
            <!-- 数据表格 -->
            <div v-if="!error" class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th 
                                scope="col" 
                                @click="toggleSort('category_code')" 
                                class="px-6 py-3 text-left text-base font-bold text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                            >
                                <div class="flex items-center">
                                    分类编码
                                    <svg v-if="sortField === 'category_code'" class="ml-1 h-4 w-4" :class="{'transform rotate-180': sortDirection === 'desc'}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                                    </svg>
                                </div>
                            </th>
                            <th 
                                scope="col" 
                                @click="toggleSort('category_name')" 
                                class="px-6 py-3 text-left text-base font-bold text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                            >
                                <div class="flex items-center">
                                    分类名称
                                    <svg v-if="sortField === 'category_name'" class="ml-1 h-4 w-4" :class="{'transform rotate-180': sortDirection === 'desc'}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                                    </svg>
                                </div>
                            </th>
                            <th 
                                scope="col" 
                                @click="toggleSort('figure')" 
                                class="px-6 py-3 text-left text-base font-bold text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                            >
                                <div class="flex items-center">
                                    图号
                                    <svg v-if="sortField === 'figure'" class="ml-1 h-4 w-4" :class="{'transform rotate-180': sortDirection === 'desc'}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                                    </svg>
                                </div>
                            </th>
                            <th 
                                scope="col" 
                                @click="toggleSort('product_name')" 
                                class="px-6 py-3 text-left text-base font-bold text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                            >
                                <div class="flex items-center">
                                    品名
                                    <svg v-if="sortField === 'product_name'" class="ml-1 h-4 w-4" :class="{'transform rotate-180': sortDirection === 'desc'}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                                    </svg>
                                </div>
                            </th>
                            <th 
                                scope="col" 
                                @click="toggleSort('specification')" 
                                class="px-6 py-3 text-left text-base font-bold text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                            >
                                <div class="flex items-center">
                                    规格
                                    <svg v-if="sortField === 'specification'" class="ml-1 h-4 w-4" :class="{'transform rotate-180': sortDirection === 'desc'}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                                    </svg>
                                </div>
                            </th>
                            <th 
                                scope="col" 
                                @click="toggleSort('material_code')" 
                                class="px-6 py-3 text-left text-base font-bold text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                            >
                                <div class="flex items-center">
                                    料号
                                    <svg v-if="sortField === 'material_code'" class="ml-1 h-4 w-4" :class="{'transform rotate-180': sortDirection === 'desc'}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                                    </svg>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <tr v-for="item in items" :key="item.id">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ item.category_code }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ item.category_name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ item.figure }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ item.product_name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ item.specification }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ item.material_code }}</td>
                        </tr>
                        <!-- 无数据显示 -->
                        <tr v-if="items.length === 0">
                            <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                                暂无数据
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- 分页控件 -->
                <div v-if="pagination.total > 0" class="flex items-center justify-between mt-4">
                    <div class="text-sm text-gray-700 dark:text-gray-300">
                        显示 {{ pagination.from }} 到 {{ pagination.to }} 条，共 {{ pagination.total }} 条记录
                    </div>
                    <div class="flex items-center space-x-2">
                        <!-- 上一页按钮 -->
                        <button 
                            @click="prevPage" 
                            :disabled="pagination.current_page === 1"
                            :class="[
                                'px-3 py-1 rounded-md',
                                pagination.current_page === 1 
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500'
                                    : 'bg-white text-blue-600 hover:bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:hover:bg-gray-700'
                            ]"
                        >
                            上一页
                        </button>
                        
                        <!-- 页码按钮 -->
                        <button 
                            v-for="page in pageNumbers" 
                            :key="page"
                            @click="goToPage(page)"
                            :class="[
                                'px-3 py-1 rounded-md',
                                page === '...' 
                                    ? 'bg-white text-gray-600 cursor-default dark:bg-gray-800 dark:text-gray-400'
                                    : page === pagination.current_page
                                        ? 'bg-blue-600 text-white dark:bg-blue-700'
                                        : 'bg-white text-blue-600 hover:bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:hover:bg-gray-700'
                            ]"
                        >
                            {{ page }}
                        </button>
                        
                        <!-- 下一页按钮 -->
                        <button 
                            @click="nextPage" 
                            :disabled="pagination.current_page === pagination.last_page"
                            :class="[
                                'px-3 py-1 rounded-md',
                                pagination.current_page === pagination.last_page 
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500'
                                    : 'bg-white text-blue-600 hover:bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:hover:bg-gray-700'
                            ]"
                        >
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>