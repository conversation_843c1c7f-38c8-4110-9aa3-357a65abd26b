<script setup lang="ts">
import { ref, onMounted, nextTick, computed, watch, inject, provide, Ref, ComputedRef } from 'vue';

// 定义节点类型接口
interface BomNode {
    bmba003?: string;
    material_code?: string;
    product_name?: string;
    specification?: string;
    supply_type?: string;
    base_quantity?: string | number;
    child_quantity?: string | number;
    unit?: string;
    gross_weight?: string | number;
    net_weight?: string | number;
    length?: string | number;
    width?: string | number;
    height?: string | number;
    is_optional?: string;
    customer_code?: string;
    full_name?: string;
    is_order_expand?: string;
    is_agent_purchase?: string;
    is_customer_material?: string;
    effective_time?: string;
    failure_time?: string;
    isLeaf?: boolean;
    IS_LEAF?: boolean;
    is_leaf?: boolean;
    childs?: BomNode[] | Record<string, BomNode>;
    CHILDS?: BomNode[] | Record<string, BomNode>;
    children?: BomNode[] | Record<string, BomNode>;
    CHILDREN?: BomNode[] | Record<string, BomNode>;
    has_children?: boolean;
    hasChildren?: boolean;
    HAS_CHILDREN?: boolean;
    is_circular_ref?: boolean;
    [key: string]: any; // 允许访问其他可能的属性
}

// 注入全局选中节点状态，如果父组件没有提供，则创建一个新的
const selectedNodeKey = inject<Ref<string | null>>('selectedNodeKey', ref(null));

// 提供给子组件使用
provide('selectedNodeKey', selectedNodeKey);

// 创建一个唯一的节点ID，用于选中状态标识
const uniqueNodeId = ref<string>(`node-${Math.random().toString(36).substring(2, 11)}`);

// 提供给子组件使用
provide('uniqueNodeId', uniqueNodeId);

interface BomTreeProps {
    node: BomNode | BomNode[];
    level?: number;
    expanded?: boolean;
    mode?: 'multi' | 'single' | 'leaf';
    globalExpandState?: string;
}

const props = defineProps<BomTreeProps>();

// 获取当前节点对象(如果是数组，取第一个元素)
const currentNode = computed<BomNode>(() => {
    return Array.isArray(props.node) ? props.node[0] || {} : props.node;
});

const isExpanded = ref<boolean>(props.expanded ?? true);
const nodeKey = computed<string>(() => {
    // 处理直接包含ID字段的节点
    if (currentNode.value.bmba003) return currentNode.value.bmba003;

    // 如果没有标准字段，创建一个基于级别的唯一ID
    return `node-${props.level ?? 0}-${Math.random().toString(36).substring(2, 11)}`;
});

// 在挂载时输出节点信息，帮助调试
onMounted(() => {
    nextTick(() => {
        // 如果已经设置了全局展开状态，优先使用全局状态
        if (props.globalExpandState === 'minimal') {
            isExpanded.value = (props.level ?? 0) === 0;
        } else if (props.globalExpandState === 'all') {
            isExpanded.value = true;
        } else {
            // 使用默认展开逻辑
            // 根节点始终展开
            if ((props.level ?? 0) === 0) {
                isExpanded.value = true;
                console.log(`根节点自动展开: ${nodeKey.value}`);
            }
            // 根据不同模式决定默认展开状态
            else if (props.mode === 'multi' && (props.level ?? 0) <= 1) {
                isExpanded.value = true;
                console.log(`一级节点自动展开: ${nodeKey.value}`);
            } else if (props.mode === 'leaf') {
                // 对于尾阶模式，所有节点默认展开
                isExpanded.value = true;
                console.log(`尾阶模式节点自动展开: ${nodeKey.value}`);
            } else {
                // 其他情况使用传入的展开状态
                isExpanded.value = props.expanded ?? true;
            }
        }

        // 收集和显示节点信息用于调试
        const nodeInfo = {
            level: props.level ?? 0,
            key: nodeKey.value,
            hasChildren: hasChildren.value,
            showExpandButton: showExpandButton.value,
            isExpanded: isExpanded.value,
            mode: props.mode,
            globalExpandState: props.globalExpandState
        };
        console.log('节点信息:', nodeInfo);
    });
});

// 切换展开/折叠状态
const toggleExpand = (): void => {
    // 如果是0级节点，不允许折叠
    if ((props.level ?? 0) === 0 && isExpanded.value) {
        console.log('根节点不允许折叠');
        return;
    }

    isExpanded.value = !isExpanded.value;
    console.log(`切换节点 ${nodeKey.value} 展开状态: ${isExpanded.value}`);

    // 如果展开，告知父组件更新表格高度
    if (isExpanded.value) {
        nextTick(() => {
            // 触发window的resize事件，让表格容器重新计算高度
            window.dispatchEvent(new Event('resize'));
        });
    }
};

// 获取子节点列表 - 增加调试日志
const getChildNodes = (): BomNode[] => {
    if (!props.node) return [];

    let result: BomNode[] = [];
    const node = currentNode.value;
    const nodeName = node.material_code || '未知节点';

    // 检查是否是叶子节点
    const isLeafItem = node.isLeaf === true || node.IS_LEAF === true || node.is_leaf === true;
    
    if (isLeafItem) {
        console.log(`节点 ${nodeName} 是叶子节点，跳过子节点检查`);
        return [];
    }

    // 处理childs属性 (小写)
    if ('childs' in node && node.childs) {
        console.log(`节点 ${nodeName} 使用childs属性获取子节点，数量:`, Array.isArray(node.childs) ? node.childs.length : Object.keys(node.childs).length);
        result = Array.isArray(node.childs)
            ? node.childs
            : Object.values(node.childs);
    }
    // 处理CHILDS属性 (大写)
    else if ('CHILDS' in node && node.CHILDS) {
        console.log(`节点 ${nodeName} 使用CHILDS属性获取子节点，数量:`, Array.isArray(node.CHILDS) ? node.CHILDS.length : Object.keys(node.CHILDS).length);
        result = Array.isArray(node.CHILDS)
            ? node.CHILDS
            : Object.values(node.CHILDS);
    }
    // 处理children属性
    else if ('children' in node && node.children) {
        console.log(`节点 ${nodeName} 使用children属性获取子节点，数量:`, Array.isArray(node.children) ? node.children.length : Object.keys(node.children).length);
        const childList = Array.isArray(node.children)
            ? node.children
            : Object.values(node.children);

        result = childList.map(child => Array.isArray(child) ? (child[0] || child) : child);
    }
    // 处理CHILDREN属性 (大写)
    else if ('CHILDREN' in node && node.CHILDREN) {
        console.log(`节点 ${nodeName} 使用CHILDREN属性获取子节点，数量:`, Array.isArray(node.CHILDREN) ? node.CHILDREN.length : Object.keys(node.CHILDREN).length);
        const childList = Array.isArray(node.CHILDREN)
            ? node.CHILDREN
            : Object.values(node.CHILDREN);

        result = childList.map(child => Array.isArray(child) ? (child[0] || child) : child);
    }
    // 处理数字索引对象 (当节点本身就是数组的情况)
    else if (Array.isArray(props.node)) {
        console.log(`节点是数组，直接使用，数量: ${props.node.length}`);
        result = props.node;
    }
    // 处理数字索引对象
    else {
        const numericKeys = Object.keys(node).filter(key => !isNaN(parseInt(key)));
        if (numericKeys.length > 0) {
            console.log(`节点 ${nodeName} 使用数字索引获取子节点，找到 ${numericKeys.length} 个子节点`);
            result = numericKeys.map(key => node[key]);
        }
    }

    // 确保所有子节点都是有效对象
    result = result.filter(child => child && typeof child === 'object');

    // 额外检查数据中可能标记的子节点状态
    if (result.length === 0) {
        // 检查节点上可能的子节点标记
        const hasChildFlag =
            node.has_children === true ||
            node.hasChildren === true ||
            node.HAS_CHILDREN === true ||
            node.material_code === 'Y';

        if (hasChildFlag) {
            console.log(`节点 ${nodeName} 标记为有子节点，但未找到具体子节点数据`);
        }
    }

    console.log(`节点 ${nodeName} 的子节点数量: ${result.length}`);
    return result;
};

// 计算子节点
const children = computed<BomNode[]>(() => {
    const childNodes = getChildNodes();

    // 在尾阶模式下对相同料号和客户编号的节点进行汇总
    if (props.mode === 'leaf' && (props.level ?? 0) === 0 && childNodes.length > 0) {
        console.log('尾阶模式：对相同料号和客户编号的节点进行汇总');

        // 创建一个Map用于存储汇总后的节点
        // 使用料号+客户编号作为键
        const groupedNodes = new Map<string, BomNode>();

        childNodes.forEach(node => {
            // 获取料号和客户编号
            const nodeObj = Array.isArray(node) ? node[0] || {} : node;
            const itemCode = nodeObj.material_code || '';
            const customerCode = nodeObj.customer_code || '';
            const key = `${itemCode}-${customerCode}`;

            if (groupedNodes.has(key)) {
                // 如果已存在相同料号和客户编号的节点，则累加组成用量
                const existingNode = groupedNodes.get(key)!;
                const existingDosage = parseFloat(String(existingNode.child_quantity || 0));
                const newDosage = parseFloat(String(nodeObj.child_quantity || 0));

                // 更新组成用量，保留3位小数
                const totalDosage = existingDosage + newDosage;
                existingNode.child_quantity = totalDosage.toFixed(3);

                console.log(`汇总节点 ${itemCode}(${customerCode}): ${existingDosage} + ${newDosage} = ${existingNode.child_quantity}`);
            } else {
                // 如果不存在，则添加到Map中
                groupedNodes.set(key, { ...nodeObj });
            }
        });

        // 将Map转换为数组
        const result = Array.from(groupedNodes.values());
        console.log(`汇总前节点数量: ${childNodes.length}, 汇总后节点数量: ${result.length}`);

        return result;
    }

    return childNodes;
});

// 检查是否有子节点 - 使用计算属性确保响应式
const hasChildren = computed<boolean>(() => {
    const hasChilds = children.value.length > 0;
    return hasChilds;
});

// 检查是否为循环引用节点
const isCircularRef = computed<boolean>(() => {
    return !!currentNode.value.is_circular_ref;
});

// 在叶子节点上显示特殊标记
const isLeafNode = computed<boolean>(() => {
    return !hasChildren.value && (props.level ?? 0) > 0;
});

// 添加日期格式化函数
const formatDate = (dateString?: string): string => {
    if (!dateString) return '';
    try {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    } catch (e) {
        return dateString;
    }
};

// 工单展开选项转换为中文
const formatWorkOrderOption = (option?: string): string => {
    if (!option) return '';
    const options: Record<string, string> = {
        '1': '不展开',
        '2': '不展开，自动开立子工单',
        '3': '展开',
        '4': '开窗询问'
    };
    return options[option] || option;
};

// 补给策略转换为中文
const formatSupplyStrategy = (strategy?: string): string => {
    if (!strategy) return '';
    const options: Record<string, string> = {
        '1': '采购',
        '2': '自制',
        '3': '委外',
        '4': '无'
    };
    return options[strategy] || strategy;
};

// 格式化数字，保留3位小数
const formatNumber = (num?: string | number): string => {
    if (num === undefined || num === null || num === '') return '';

    // 尝试将输入转换为数字
    const numValue = parseFloat(String(num));

    // 如果转换失败或者值为0，返回空字符串
    if (isNaN(numValue) || numValue === 0) return '';

    // 格式化为3位小数
    return numValue.toFixed(3);
};

// 格式化长宽高，保留2位小数
const formatDimension = (num?: string | number): string => {
    if (num === undefined || num === null || num === '') return '';

    // 尝试将输入转换为数字
    const numValue = parseFloat(String(num));

    // 如果转换失败或者值为0，返回空字符串
    if (isNaN(numValue) || numValue === 0) return '';

    // 格式化为2位小数
    return numValue.toFixed(2);
};

// 判断当前节点是否被选中
const isSelected = computed<boolean>(() => {
    // 直接比较唯一ID，不需要使用料号
    return selectedNodeKey.value === uniqueNodeId.value;
});

// 处理节点点击事件，设置选中状态
const handleNodeClick = (event: MouseEvent): void => {
    // 如果点击的是展开/折叠按钮，不触发选中
    if (event.target && (event.target as HTMLElement).closest('button')) return;

    // 如果当前节点已经被选中，则取消选中
    if (selectedNodeKey.value === uniqueNodeId.value) {
        selectedNodeKey.value = null;
        console.log(`取消选中节点: ${nodeKey.value}`);
    } else {
        // 否则设置当前节点为选中状态，使用唯一ID
        selectedNodeKey.value = uniqueNodeId.value;
        console.log(`选中节点: ${nodeKey.value}, 唯一ID: ${uniqueNodeId.value}`);
    }
};

// 行样式计算
const rowClasses = computed<string>(() => {
    const classes = ['transition-all duration-150 hover:bg-gray-100'];

    // 添加层级类名，用于CSS选择器
    classes.push(`level-${props.level ?? 0}`);

    // 循环引用特殊样式 - 使用特殊标记而不是颜色
    if (isCircularRef.value) {
        classes.push('circular-ref-node');
    }

    // 选中状态样式 - 最高优先级
    if (isSelected.value) {
        classes.push('selected-node');
    }

    return classes.join(' ');
});

// 生成层级连接线样式
const getConnectorStyle = (): Record<string, string> | null => {
    // 根节点没有连接线
    if ((props.level ?? 0) === 0) return null;

    return {
        borderLeft: `1px solid #d1d5db`,
        position: 'absolute',
        left: `${((props.level ?? 0) - 1) * 16 + 5}px`,
        top: '0',
        height: '100%',
        opacity: '0.7'
    };
};

// 生成水平连接线样式
const getHorizontalConnectorStyle = (): Record<string, string> | null => {
    if ((props.level ?? 0) === 0) return null;

    return {
        borderBottom: `1px solid #d1d5db`,
        position: 'absolute',
        left: `${((props.level ?? 0) - 1) * 16 + 5}px`,
        top: '50%',
        width: '12px',
        opacity: '0.7'
    };
};

// 在表格模式下显示展开/折叠按钮
const showExpandButton = computed<boolean>(() => {
    // 有子节点的情况
    if (hasChildren.value) return true;

    // 检查节点上可能的子节点标记
    const node = currentNode.value;
    const hasChildFlag =
        node.has_children === true ||
        node.hasChildren === true ||
        node.HAS_CHILDREN === true ||
        node.material_code === 'Y';

    return hasChildFlag;
});

// 递归展开子节点的变量
const deepExpandAll = ref<boolean>(false);

// 监听全局展开状态变化
watch(() => props.globalExpandState, (newState) => {
    if (newState === 'all') {
        // 全部展开 - 标记为递归展开
        isExpanded.value = true;
        deepExpandAll.value = true;
    } else if (newState === 'minimal') {
        // 最小化展开 -
        isExpanded.value = (props.level ?? 0) === 0;
        deepExpandAll.value = false;
    } else if (newState === 'reset') {
        // 重置所有展开状态
        if ((props.level ?? 0) > 0) {
            isExpanded.value = false;
        }
        deepExpandAll.value = false;
    }
    // 默认状态不做处理
}, { immediate: true });

// 监听深度展开标记变化
watch(() => deepExpandAll.value, (newVal) => {
    if (newVal && hasChildren.value) {
        // 首先确保当前节点已展开
        isExpanded.value = true;

        // 然后在下一个渲染周期递归处理子节点
        nextTick(() => {
            // 通过事件触发，但此处不执行DOM操作，而是依赖props传递
            console.log(`节点 ${nodeKey.value} 递归展开子节点`);
        });
    }
});

// 确保0层级始终展开
watch(() => isExpanded.value, (newValue) => {
    if ((props.level ?? 0) === 0 && !newValue) {
        isExpanded.value = true;
    }
});
</script>

<script lang="ts">
// 导出组件名称，使其可以递归引用自身
export default {
    name: 'BomTree'
};
</script>

<template>
    <!-- 表格模式 -->
    <tr :class="rowClasses" @click="handleNodeClick" class="cursor-pointer">
        <!-- 合并展开/折叠按钮和料号列 -->
        <td class="p-2 text-left text-gray-800 relative">
            <!-- 垂直连接线 -->
            <div v-if="(level || 0) > 0" :style="getConnectorStyle()" class="connector-vertical"></div>
            <!-- 水平连接线 -->
            <div v-if="(level || 0) > 0" :style="getHorizontalConnectorStyle()" class="connector-horizontal"></div>

            <div class="flex items-center relative" :style="{ 'padding-left': `${(level || 0) * 16}px` }">
                <!-- 展开/折叠按钮 -->
                <button v-if="showExpandButton" @click="toggleExpand" class="w-5 h-5 flex items-center justify-center text-black hover:text-gray-900 focus:outline-none z-10 mr-1">
                    <svg v-if="isExpanded" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                    <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
                <span v-else class="w-5 mr-1 z-10"></span>

                <!-- 节点图标 - 只在叶子节点和根节点显示 -->
                <span v-if="isLeafNode && (level || 0) > 0" class="inline-flex items-center mr-1">
                    <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </span>
                <span v-else-if="(level || 0) === 0" class="inline-flex items-center mr-1">
                    <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"></path>
                    </svg>
                </span>
                <!-- 非根节点非叶子节点不显示图标 -->

                <!-- 料号 -->
                <span class="font-medium">{{ currentNode.material_code }}</span>
            </div>
        </td>
        <td class="p-2 text-left text-gray-800">{{ currentNode.product_name }}</td>
        <td class="p-2 text-left text-gray-800">{{ currentNode.specification }}</td>
        <td class="p-2 text-center text-gray-800">{{ formatSupplyStrategy(currentNode.supply_type) }}</td>
        <td class="p-2 text-center text-gray-800">{{ formatNumber(currentNode.base_quantity) }}</td>
        <td class="p-2 text-center text-gray-800">{{ formatNumber(currentNode.child_quantity) }}</td>
        <td class="p-2 text-center text-gray-800">{{ currentNode.unit }}</td>
        <td class="p-2 text-center text-gray-800">{{ formatNumber(currentNode.gross_weight) }}</td>
        <td class="p-2 text-center text-gray-800">{{ formatNumber(currentNode.net_weight) }}</td>
        <td class="p-2 text-center text-gray-800">{{ formatDimension(currentNode.length) }}</td>
        <td class="p-2 text-center text-gray-800">{{ formatDimension(currentNode.width) }}</td>
        <td class="p-2 text-center text-gray-800">{{ formatDimension(currentNode.height) }}</td>
        <td class="p-2 text-center text-gray-800">{{ (currentNode.is_optional) === 'Y' ? '是' : '否' }}</td>
        <td class="p-2 text-center text-gray-800">{{ currentNode.customer_code }}</td>
        <td class="p-2 text-center text-gray-800">{{ currentNode.full_name }}</td>
        <td class="p-2 text-center text-gray-800">{{ formatWorkOrderOption(currentNode.is_order_expand) }}</td>
        <td class="p-2 text-center text-gray-800">{{ (currentNode.is_agent_purchase) === 'Y' ? '是' : '否' }}</td>
        <td class="p-2 text-center text-gray-800">{{ (currentNode.is_customer_material) === 'Y' ? '是' : '否' }}</td>
        <td class="p-2 text-center text-gray-800">{{ formatDate(currentNode.effective_time) }}</td>
        <td class="p-2 text-center text-gray-800">{{ formatDate(currentNode.failure_time) }}</td>
    </tr>

    <!-- 表格模式下的子节点渲染 -->
    <template v-if="hasChildren && isExpanded">
        <bom-tree
            v-for="(child, index) in children"
            :key="`table-child-${index}`"
            :node="child"
            :level="(level || 0) + 1"
            :expanded="deepExpandAll"
            :mode="mode"
            :global-expand-state="globalExpandState"
        ></bom-tree>
    </template>
</template>

<style scoped>
/* 添加文本颜色样式 */
.text-gray-600 {
    color: #4b5563;
}

/* 黑色文本样式 */
.text-black {
    color: #000000;
}

/* 使表格行高适中，确保足够的内容显示空间 */
tr {
    height: 40px;
    position: relative;
    border-bottom: 1px solid #f3f4f6;
}

/* 防止表格单元格内容过长时破坏布局 */
td {
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 调整缩进时的过渡效果 */
.bom-tree-node {
    transition: all 0.2s ease-in-out;
}

/* 连接线样式 */
.connector-vertical {
    z-index: 1;
}

.connector-horizontal {
    z-index: 1;
}

/* 为不同层级添加不同的左侧边距和视觉标识 */
.level-0 {
    font-weight: bold;
    background-color: #f9fafb;
}

.level-1 {
    background-color: #ffffff;
}

.level-2 {
    background-color: #f9fafb;
}

.level-3 {
    background-color: #ffffff;
}

.level-4 {
    background-color: #f9fafb;
}

.level-5 {
    background-color: #ffffff;
}

/* 循环引用节点样式 */
.circular-ref-node {
    border-left: 3px solid #ef4444;
}

/* 选中节点样式 */
.selected-node {
    position: relative;
    z-index: 1;
    background-color: #f0f9ff !important; /* 浅蓝色背景，覆盖原有背景 */
    border-left: 3px solid #3b82f6 !important;
    box-shadow: 0 0 0 1px #3b82f6;
}

/* 为选中行添加左侧标记 */
tr.selected-node td:first-child::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: #3b82f6;
    z-index: 2;
}

/* 料号列样式 */
td:first-child {
    min-width: 200px; /* 确保料号列有足够的空间 */
}

/* 优化折叠/展开按钮与文本的对齐 */
td:first-child .flex.items-center {
    min-height: 24px;
}

/* 为不同层级添加左侧边框标识 */
.level-0 td:first-child {
    border-left: 3px solid #3b82f6;
}

.level-1 td:first-child {
    border-left: 3px solid #a5b4fc;
}

.level-2 td:first-child {
    border-left: 3px solid #c4b5fd;
}

.level-3 td:first-child {
    border-left: 3px solid #6ee7b7;
}

.level-4 td:first-child {
    border-left: 3px solid #fcd34d;
}

.level-5 td:first-child {
    border-left: 3px solid #f9a8d4;
}

/* 为叶子节点添加特殊样式 */
tr:has(.w-4.h-4.text-green-600) {
    background-color: #f0fdf4;
}
</style>