<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted, nextTick, watch } from 'vue';
import apiService from '@/Utils/ApiService';
import AppLayout from '@/Layouts/AppLayout.vue';
import BomTree from './Components/BomTree.vue';
// 使用普通xlsx库替代xlsx-style，解决cptable未定义的问题
import * as XLSX from 'xlsx';
// @ts-ignore
import { saveAs } from 'file-saver';
import { router } from '@inertiajs/vue3';

// 扩展ApiService接口
interface ApiServiceType {
    get: (url: string, params?: any) => Promise<any>;
    post: (url: string, data?: any) => Promise<any>;
    setAuthToken: (token: string) => void;
    clearAuthToken: () => void;
    getCurrentUser: () => Promise<any>;
    getTokenFromCurrentSession: () => Promise<string | null>;
}

// 断言apiService为扩展类型
const typedApiService = apiService as unknown as ApiServiceType;

// 定义接口
interface ModeOption {
    value: string;
    label: string;
}

interface Customer {
    code: string;
    name: string;
}

interface CurrentInfo {
    mode: string;
    code: string;
    date: string;
    includeOptional: boolean;
    customerCode: string;
}

interface BomNode {
    parent_material_code?: string;
    child_material_code?: string;
    material_code?: string;
    specification?: string;
    hasChildren?: boolean;
    has_children?: boolean;
    HAS_CHILDREN?: boolean;
    isLeaf?: boolean;
    IS_LEAF?: boolean;
    is_leaf?: boolean;
    childs?: any[] | Record<string, any>;
    CHILDS?: any[] | Record<string, any>;
    children?: any[] | Record<string, any>;
    CHILDREN?: any[] | Record<string, any>;
    [key: string]: any;
}

// 类型化debounce函数
const createDebounce = <T extends (...args: any[]) => void>(fn: T, delay: number) => {
    let timeout: number | null = null;
    const debouncedFn = function(this: any, ...args: Parameters<T>) {
        const context = this;
        if (timeout !== null) clearTimeout(timeout);
        timeout = window.setTimeout(() => fn.apply(context, args), delay);
    };

    // 添加取消方法
    debouncedFn.cancel = function() {
        if (timeout !== null) {
            clearTimeout(timeout);
            timeout = null;
        }
    };

    return debouncedFn;
};

// 定义全局事件处理函数，以便在组件卸载时清理
let handleMouseMove: ((e: MouseEvent) => void) | null = null;
let handleMouseUp: (() => void) | null = null;

// 接收从路由传入的props
const props = defineProps<{
    code?: string;
}>();

// 数据
const bomData = ref<BomNode | null>(null);
const loading = ref<boolean>(true);
const error = ref<string | null>(null);
const bomCode = ref<string>(props.code || '');
const displayMode = ref<string>('multi'); // 默认为多阶模式
const debugInfo = ref<string | null>(null); // 添加调试信息
const globalExpandState = ref<string>('minimal'); // 控制全局展开状态：'default'(默认), 'all'(全部展开), 'minimal'(最小化展开)
const exporting = ref<boolean>(false); // 控制导出状态
const effectiveDate = ref<string>(new Date().toISOString().split('T')[0]); // 默认为当前日期的有效日期
const includeOptional = ref<boolean>(false); // 默认不包含可选件
const customerCode = ref<string>(''); // 客户编号
const customers = ref<Customer[]>([]); // 客户列表
const loadingCustomers = ref<boolean>(false); // 控制客户列表加载状态

// 当前展示信息 - 与输入分离，只在查询后更新
const currentInfo = ref<CurrentInfo>({
    mode: 'multi',
    code: '',
    date: new Date().toISOString().split('T')[0],
    includeOptional: false,
    customerCode: ''
});

// 模式选项
const modeOptions: ModeOption[] = [
    { value: 'multi', label: '树型多阶' },
    { value: 'leaf', label: '扁平尾阶' }
];

// 表格高度自适应处理
const tableContainerRef = ref<HTMLElement | null>(null);
const resizeObserver = ref<ResizeObserver | null>(null);

// 确保API有有效的授权令牌
const ensureApiToken = async (): Promise<boolean> => {
    // 首先检查localStorage中是否有token
    const token = localStorage.getItem('auth_token');
    if (token) {
        try {
            // 设置token并测试它是否有效（通过获取当前用户信息）
            typedApiService.setAuthToken(token);
            await typedApiService.getCurrentUser();
            console.log('现有token有效，将用于API请求');
            return true;
        } catch (error: any) {
            if (error.response && error.response.status === 401) {
                // token无效，清除并尝试获取新token
                typedApiService.clearAuthToken();
                console.log('token已过期，尝试获取新token');
            }
        }
    }

    // 尝试从当前会话获取新token
    try {
        const newToken = await typedApiService.getTokenFromCurrentSession();
        if (newToken) {
            console.log('已获取新token，将用于API请求');
            return true;
        }
    } catch (err: any) {
        console.error('无法获取token:', err.message);
    }

    console.warn('未能获取有效的API令牌，请求可能会失败');
    return false;
};

// 监听可选件状态变化
watch(() => includeOptional.value, (newVal: boolean) => {
    if (newVal) {
        // 如果勾选了可选件并且有料号，则加载该料号相关的客户列表
        if (bomCode.value) {
            fetchCustomers();
        }
    } else {
        // 取消勾选可选件时，清空客户编号和客户列表，并重新获取BOM数据（不带可选件）
        customerCode.value = '';
        customers.value = [];
        if (bomCode.value && bomCode.value.length >= 10) {
            // 自动触发查询
            autoTriggerSearch();
        }
    }
});

// 监听客户编号变化
watch(() => customerCode.value, (newVal: string, oldVal: string) => {
    if (newVal !== oldVal && includeOptional.value && bomCode.value && bomCode.value.length >= 10) {
        // 自动触发查询
        autoTriggerSearch();
    }
});

// 监听有效日期变化
watch(() => effectiveDate.value, (newVal: string, oldVal: string) => {
    if (newVal !== oldVal && bomCode.value && bomCode.value.length >= 10) {
        // 自动触发查询
        autoTriggerSearch();
    }
});

// 监听料号变化
watch(() => bomCode.value, (newVal: string, oldVal: string) => {
    // 料号长度满10位时自动查询
    if (newVal !== oldVal && newVal && newVal.length >= 10) {
        // 如果勾选了可选件并且有料号，则加载该料号相关的客户列表
        if (includeOptional.value) {
            fetchCustomers();
        }
        // 自动触发查询
        autoTriggerSearch();
    }
});

// 触发查询函数
const triggerSearch = (): void => {
    // 检查料号长度是否满足要求
    if (!bomCode.value || bomCode.value.length < 10) {
        return;
    }

    console.log('自动触发查询：', {
        code: bomCode.value,
        mode: displayMode.value,
        date: effectiveDate.value,
        includeOptional: includeOptional.value,
        customerCode: customerCode.value
    });

    // 构建URL查询参数
    const queryParams = new URLSearchParams();
    queryParams.set('code', bomCode.value);
    queryParams.set('mode', displayMode.value);
    queryParams.set('include_optional', includeOptional.value.toString());

    if (includeOptional.value && customerCode.value) {
        queryParams.set('customer_code', customerCode.value);
    }

    // 更新URL，但不刷新页面
    const url = new URL(window.location.href);
    url.search = queryParams.toString();
    window.history.replaceState({}, '', url.toString());

    // 获取BOM数据
    fetchBomTree(bomCode.value, displayMode.value);
};

// 创建防抖版本的查询函数
const autoTriggerSearch = createDebounce(triggerSearch, 600);

// 从API获取客户列表
const fetchCustomers = async (): Promise<void> => {
    if (loadingCustomers.value) return;

    // 确保有有效的API令牌
    await ensureApiToken();

    loadingCustomers.value = true;
    try {
        // 构建请求参数，添加当前料号
        const params: Record<string, any> = {};
        if (bomCode.value) {
            params.code = bomCode.value;
        }

        console.log('请求客户列表:', 'BOM/customers', params);

        const response = await typedApiService.get('BOM/customers', params);

        if (response.data && response.data.status === 'success') {
            customers.value = response.data.data;
            console.log('加载了', customers.value.length, '个客户');

            // 检查当前选中的客户编号是否在列表中
            // 如果customerCode已设置但不在列表中，保持其值不变
            // 这样可以确保URL中的customer_code参数被正确应用
            if (customerCode.value) {
                const customerExists = customers.value.some(customer => customer.code === customerCode.value);
                console.log('检查客户编号是否存在:', customerCode.value, customerExists ? '存在' : '不存在');

                // 如果客户不存在于列表中，可以选择保留或清空customerCode
                // 这里选择保留，因为可能是特定筛选条件下暂时看不到该客户
            }
        } else {
            console.error('获取客户列表失败:', response.data?.message || '未知错误');
        }
    } catch (err: any) {
        console.error('获取客户列表请求失败:', err);
    } finally {
        loadingCustomers.value = false;
    }
};

// 从API获取BOM树结构数据
const fetchBomTree = async (code: string, mode: string = 'multi'): Promise<void> => {
    if (!code) {
        error.value = '请输入有效的BOM料号';
        loading.value = false;
        return;
    }

    // 确保有有效的API令牌
    await ensureApiToken();

    loading.value = true;
    error.value = null;
    bomData.value = null; // 清空之前的数据
    debugInfo.value = null; // 清空调试信息

    try {
        // 构建请求参数
        const params: Record<string, any> = {
            mode,
            date: effectiveDate.value,
            include_optional: includeOptional.value
        };

        // 如果勾选了可选件并选择了客户，则添加客户参数
        if (includeOptional.value && customerCode.value) {
            params.customer_code = customerCode.value;
        }

        console.log('请求BOM树数据:', `BOM/tree/${code}`, params);

        const response = await typedApiService.post(`BOM/tree/${code}`, params);

        console.log('API响应:', response.data);
        debugInfo.value = JSON.stringify(response.data, null, 2);

        if (response.data && response.data.status === 'success') {
            // 更新当前展示信息
            currentInfo.value = {
                mode,
                code,
                date: effectiveDate.value,
                includeOptional: includeOptional.value,
                customerCode: includeOptional.value ? customerCode.value : ''
            };

            // 确保数据结构正确
            if (response.data.data) {
                // 处理不同格式的响应数据
                if (Array.isArray(response.data.data)) {
                    console.log('API返回数组数据，共 ' + response.data.data.length + ' 条记录');

                    // 处理数据，确保能正确显示子节点标记
                    response.data.data.forEach((item: any, index: number) => {
                        // 设置最后一个元素为叶子节点(如果没有其他标记)
                        if (index === response.data.data.length - 1 && !item.hasChildren && !item.has_children) {
                            item.isLeaf = true;
                        }
                       
                    });

                    // 数组格式，根据API结构决定如何处理
                    bomData.value = {
                        parent_material_code: code,
                        child_material_code: code,
                        specification: response.data.data[0]?.specification || response.data.data[0]?.specification || '根节点',
                        hasChildren: true,
                        childs: response.data.data
                    };
                } else {
                    // 对象格式，直接使用
                    bomData.value = response.data.data;
                }

                // 如果勾选了可选件，料号发生变化时重新获取客户列表
                if (includeOptional.value) {
                    fetchCustomers();
                }
            } else {
                bomData.value = null;
                error.value = '返回数据格式错误';
            }

            console.log('解析后的BOM数据:', bomData.value);

            // 数据加载完成后，等待DOM更新再初始化表格
            nextTick(() => {
                onTableReady();
            });

            document.title = `BOM树结构 - ${code} (${getModeLabel(mode)})`;
        } else {
            error.value = response.data?.message || '获取数据失败';
            console.error('API错误:', error.value);
        }
    } catch (err: any) {
        console.error('获取BOM树结构失败:', err);
        error.value = err.response?.data?.message || err.message || '获取数据失败';
        debugInfo.value = JSON.stringify({
            error: err.message,
            response: err.response?.data
        }, null, 2);
    } finally {
        loading.value = false;
    }
};

// 获取模式的中文标签
const getModeLabel = (mode: string): string => {
    const option = modeOptions.find(opt => opt.value === mode);
    return option ? option.label : '多阶';
};

// 处理模式切换
const handleModeChange = (mode: string): void => {
    displayMode.value = mode;

    // 构建URL查询参数
    const queryParams = new URLSearchParams();
    queryParams.set('code', bomCode.value);
    queryParams.set('mode', mode);
    queryParams.set('include_optional', includeOptional.value.toString());

    if (includeOptional.value && customerCode.value) {
        queryParams.set('customer_code', customerCode.value);
    }

    // 更新URL，但不刷新页面
    const url = new URL(window.location.href);
    url.search = queryParams.toString();
    window.history.replaceState({}, '', url.toString());

    // 获取BOM数据
    if (bomCode.value) {
        fetchBomTree(bomCode.value, mode);
    }
};

onMounted(async () => {
    // 确保apiService有有效的身份验证token
    await ensureApiToken();

    // 检查是否有料号参数
    console.log('组件挂载，料号:', bomCode.value);

    // 从URL中获取料号参数
    const urlParams = new URLSearchParams(window.location.search);
    const codeParam = urlParams.get('code');

    // 检查URL参数中是否有可选件和客户编号
    const optionalParam = urlParams.get('include_optional');
    const customerParam = urlParams.get('customer_code');
    const modeParam = urlParams.get('mode');

    // 如果URL有指定模式，使用URL指定的模式
    if (modeParam && ['multi', 'leaf'].includes(modeParam)) {
        displayMode.value = modeParam;
    }

    // 如果URL中有客户编号参数，自动启用可选件并设置客户编号
    if (customerParam) {
        includeOptional.value = true;
        // 加载客户列表
        fetchCustomers();
        // 设置客户编号
        customerCode.value = customerParam;
        console.log('从URL获取客户编号参数:', customerParam);
    }
    // 否则，根据可选件参数决定是否启用可选件
    else if (optionalParam === 'true' || optionalParam === '1') {
        includeOptional.value = true;
        // 加载客户列表
        fetchCustomers();
    } else {
        // 确保默认为不勾选
        includeOptional.value = false;
    }

    if (codeParam) {
        // 如果URL中有code参数，使用该参数
        bomCode.value = codeParam;
        console.log('从URL获取料号参数:', codeParam);
    }

    // 检查是否有料号，有则加载数据
    if (bomCode.value) {
        // 先设置初始当前信息，以防止界面闪烁
        currentInfo.value = {
            mode: displayMode.value,
            code: bomCode.value,
            date: effectiveDate.value,
            includeOptional: includeOptional.value,
            customerCode: includeOptional.value ? customerCode.value : ''
        };

        fetchBomTree(bomCode.value, displayMode.value);
    } else if (window.location.pathname.includes('/bom-tree/')) {
        // 从URL路径中提取料号
        const pathParts = window.location.pathname.split('/');
        const pathCode = pathParts[pathParts.length - 1];
        if (pathCode && pathCode !== 'bom-tree') {
            bomCode.value = pathCode;
            console.log('从路径获取料号:', pathCode);

            // 先设置初始当前信息
            currentInfo.value = {
                mode: displayMode.value,
                code: bomCode.value,
                date: effectiveDate.value,
                includeOptional: includeOptional.value,
                customerCode: includeOptional.value ? customerCode.value : ''
            };

            fetchBomTree(bomCode.value, displayMode.value);
        } else {
            error.value = '未指定BOM料号';
            loading.value = false;
        }
    } else {
        // 默认加载一个示例料号
        bomCode.value = '1010200019'; // 使用示例料号

        // 先设置初始当前信息
        currentInfo.value = {
            mode: displayMode.value,
            code: bomCode.value,
            date: effectiveDate.value,
            includeOptional: includeOptional.value,
            customerCode: includeOptional.value ? customerCode.value : ''
        };

        fetchBomTree(bomCode.value, displayMode.value);
    }

    // 延迟执行列宽调整功能，确保DOM已完全加载
    setTimeout(() => {
        initColumnResizing();
    }, 500);

    // 设置ResizeObserver监听列宽变化
    nextTick(() => {
        if (tableContainerRef.value) {
            resizeObserver.value = new ResizeObserver(() => {
                // 只保留列宽调整相关代码，移除高度计算
                console.log('表格容器大小变化');
            });

            resizeObserver.value.observe(tableContainerRef.value);
        }

        // 移除window resize监听器
    });
});

onUnmounted(() => {
    // 清理监听器
    if (resizeObserver.value) {
        resizeObserver.value.disconnect();
    }
    
    // 清理列宽调整相关的事件监听器
    if (handleMouseMove) {
        document.removeEventListener('mousemove', handleMouseMove);
    }
    if (handleMouseUp) {
        document.removeEventListener('mouseup', handleMouseUp);
    }
    document.body.classList.remove('column-resizing');
});

// 单独提取列宽调整功能为独立函数
const initColumnResizing = (): void => {
    console.log('初始化列宽调整功能');
    const table = document.querySelector('table');
    if (!table) {
        console.error('未找到表格元素');
        return;
    }

    const cols = table.querySelectorAll('th');
    let startX: number;
    let startWidth: number;
    let col: HTMLTableCellElement | null = null;
    let isResizing = false;

    // 恢复保存的列宽
    try {
        const savedWidths = localStorage.getItem('bomTreeColumnWidths');
        if (savedWidths) {
            const widths = JSON.parse(savedWidths);
            cols.forEach((th, index) => {
                if (widths[index] && widths[index] > 50) {
                    th.style.width = `${widths[index]}px`;
                    
                    // 同时调整colgroup中对应的col元素宽度
                    const colgroup = table.querySelector('colgroup');
                    if (colgroup && colgroup.children[index]) {
                        const colElement = colgroup.children[index] as HTMLElement;
                        colElement.style.width = `${widths[index]}px`;
                    }
                }
            });
        }
    } catch (e) {
        console.error('恢复列宽失败:', e);
    }

    // 全局鼠标移动处理函数
    handleMouseMove = function(e: MouseEvent) {
        if (!isResizing) return;
        
        e.preventDefault();
        if (!col) return;
        
        const width = startWidth + e.pageX - startX;
        if (width > 50) { // 最小宽度
            col.style.width = `${width}px`;
            
            // 同时调整colgroup中对应的col元素宽度
            const colgroup = table.querySelector('colgroup');
            const index = Array.from(cols).indexOf(col);
            if (colgroup && colgroup.children[index]) {
                const colElement = colgroup.children[index] as HTMLElement;
                colElement.style.width = `${width}px`;
            }
        }
    };

    // 全局鼠标释放处理函数
    handleMouseUp = function() {
        if (!isResizing) return;
        
        isResizing = false;
        document.body.classList.remove('column-resizing');
        
        if (col) {
            col.classList.remove('resizing-column');
        }
        
        // 保存所有列宽
        try {
            const widths = Array.from(cols).map(col => col.offsetWidth);
            localStorage.setItem('bomTreeColumnWidths', JSON.stringify(widths));
            console.log('列宽已保存', widths);
        } catch (e) {
            console.error('保存列宽失败:', e);
        }
        
        // 移除全局事件监听器
        if (handleMouseMove) {
            document.removeEventListener('mousemove', handleMouseMove);
        }
        if (handleMouseUp) {
            document.removeEventListener('mouseup', handleMouseUp);
        }
        
        col = null;
    };

    // 为每列添加调整事件
    cols.forEach((th: HTMLTableCellElement, index: number) => {
        const resizer = th.querySelector('.cursor-col-resize');
        if (!resizer) return;

        // 移除可能的旧事件监听器
        const newResizer = resizer.cloneNode(true) as HTMLElement;
        if (resizer.parentNode) {
            resizer.parentNode.replaceChild(newResizer, resizer);
        }

        // 扩展点击区域(虽然视觉上是1px但实际可点击区域可以更大)
        newResizer.style.cursor = 'col-resize';
        // 在右边添加4px的padding作为点击区域，但视觉上仍是1px
        newResizer.style.paddingRight = '4px';
        newResizer.style.paddingLeft = '4px';
        // 移动位置以覆盖列边框
        newResizer.style.right = '-5px';
        
        // 添加拖动开始事件处理
        newResizer.addEventListener('mousedown', function(e: MouseEvent) {
            e.stopPropagation(); // 阻止事件冒泡
            e.preventDefault(); // 防止选中文本
            
            console.log(`开始调整第${index+1}列宽度`);
            isResizing = true;
            col = th;
            startX = e.pageX;
            startWidth = col.offsetWidth;
            
            // 添加视觉反馈
            col.classList.add('resizing-column');
            document.body.classList.add('column-resizing');
            
            // 添加全局事件监听器
            if (handleMouseMove) {
                document.addEventListener('mousemove', handleMouseMove);
            }
            if (handleMouseUp) {
                document.addEventListener('mouseup', handleMouseUp);
            }
        });
    });

    console.log(`已为${cols.length}列添加宽度调整功能`);
};

// 监听表格渲染完成
const onTableReady = (): void => {
    // 延迟执行列宽调整功能，确保DOM已完全加载
    setTimeout(() => {
        initColumnResizing();
    }, 300);
};

// 处理手动搜索
const handleSearch = (): void => {
    if (bomCode.value.trim()) {
        // 清除可能的防抖等待
        autoTriggerSearch.cancel && autoTriggerSearch.cancel();

        // 构建URL查询参数
        const queryParams = new URLSearchParams();
        queryParams.set('code', bomCode.value);
        queryParams.set('mode', displayMode.value);
        queryParams.set('include_optional', includeOptional.value.toString());

        if (includeOptional.value && customerCode.value) {
            queryParams.set('customer_code', customerCode.value);
        }

        // 更新URL，但不刷新页面
        const url = new URL(window.location.href);
        url.search = queryParams.toString();
        window.history.replaceState({}, '', url.toString());

        // 当可选件选中时，每次搜索重新获取与新料号相关的客户列表
        if (includeOptional.value) {
            // 不清空当前选中的客户，只重新获取列表
            fetchCustomers();
        }

        // 获取BOM数据
        fetchBomTree(bomCode.value, displayMode.value);
    } else {
        error.value = '请输入有效的BOM料号';
    }
};

// 控制树的全局展开状态
const setGlobalExpandState = (state: string): void => {
    // 如果已经是该状态，先重置再设置
    if (globalExpandState.value === state) {
        globalExpandState.value = 'reset';
        setTimeout(() => {
            globalExpandState.value = state;
        }, 50);
    } else {
        globalExpandState.value = state;
    }

    // 触发一个resize事件让表格重新计算高度
    setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
    }, 150);
};

// 导出Excel功能
const exportToExcel = (): void => {
    // 防止重复点击
    if (exporting.value) return;

    exporting.value = true;

    // 先确保所有节点都已展开
    setGlobalExpandState('all');

    // 给DOM一些时间来更新
    setTimeout(() => {
        try {
            // 获取表格元素
            const table = document.querySelector('table');
            if (!table) {
                console.error('未找到表格元素');
                exporting.value = false;
                return;
            }

            // 获取表头
            const headers: string[] = [];

            // 手动设置表头，确保第一列是料号
            headers.push('料号'); // 第一列是料号

            // 获取其他列的表头
            table.querySelectorAll('thead th').forEach((th, index) => {
                // 跳过第一列（展开按钮列）
                if (index === 0) return;

                const text = th.textContent || '';
                if (text) headers.push(text.trim());
            });

            // 获取所有行数据
            const rows: any[][] = [];
            // 添加表头行
            rows.push(headers);

            // 获取所有数据行 - 需要提取层级信息
            table.querySelectorAll('tbody tr').forEach((tr: Element) => {
                const rowData: string[] = [];

                // 获取层级信息 - 从HTML结构中提取
                let level = 0;
                // 尝试从DOM元素的计算样式中获取padding-left
                const firstCellDiv = tr.querySelector('td:first-child div');
                if (firstCellDiv) {
                    // 使用getComputedStyle获取计算后的样式，而不是内联样式
                    const computedStyle = window.getComputedStyle(firstCellDiv);
                    const paddingLeft = computedStyle.paddingLeft;
                    if (paddingLeft) {
                        // 格式例如: "12px" - 每层级缩进12px
                        const paddingValue = parseInt(paddingLeft);
                        if (!isNaN(paddingValue)) {
                            level = paddingValue / 12;
                        }
                    }
                }

                // 如果无法从样式获取，尝试从类名或数据属性获取层级信息
                if (level === 0) {
                    // 查找行中可能包含层级信息的类名
                    const classList = Array.from(tr.classList);
                    const levelClass = classList.find(cls => cls.startsWith('level-'));
                    if (levelClass) {
                        const levelMatch = levelClass.match(/level-(\d+)/);
                        if (levelMatch && levelMatch[1]) {
                            level = parseInt(levelMatch[1]);
                        }
                    }
                }

                // 如果仍然无法获取层级，尝试从行的位置和缩进关系推断
                if (level === 0) {
                    // 查找前一个兄弟元素的缩进级别
                    const prevRow = tr.previousElementSibling;
                    if (prevRow) {
                        const prevCellDiv = prevRow.querySelector('td:first-child div');
                        if (prevCellDiv) {
                            const prevComputedStyle = window.getComputedStyle(prevCellDiv);
                            const prevPaddingLeft = prevComputedStyle.paddingLeft;
                            const currentCellDiv = tr.querySelector('td:first-child div');
                            const currentPaddingLeft = currentCellDiv ? 
                                window.getComputedStyle(currentCellDiv).paddingLeft : '0px';

                            // 比较当前行和前一行的缩进，判断层级关系
                            const prevPadding = parseInt(prevPaddingLeft) || 0;
                            const currentPadding = parseInt(currentPaddingLeft) || 0;

                            if (currentPadding > prevPadding) {
                                // 如果当前行缩进更多，说明是子节点
                                const prevDataset = (prevRow as HTMLElement).dataset;
                                level = (prevDataset && prevDataset.level ? parseInt(prevDataset.level) : 0) + 1;
                            } else if (currentPadding === prevPadding) {
                                // 如果缩进相同，说明是同级节点
                                const prevDataset = (prevRow as HTMLElement).dataset;
                                level = prevDataset && prevDataset.level ? parseInt(prevDataset.level) : 0;
                            } else {
                                // 如果缩进更少，需要回溯找到对应的父节点级别
                                const prevDataset = (prevRow as HTMLElement).dataset;
                                level = Math.max(0, (prevDataset && prevDataset.level ? parseInt(prevDataset.level) : 0) - 1);
                            }
                        }
                    }
                }

                // 存储计算出的层级到行元素，方便后续行使用
                const trElement = tr as HTMLElement;
                if (trElement.dataset) {
                    trElement.dataset.level = level.toString();
                }

                // 收集单元格数据（现在层级和料号已合并到第一列）
                const cells = tr.querySelectorAll('td');

                // 处理第一列（包含层级和料号）
                if (cells.length > 0) {
                    const firstCell = cells[0];
                    let itemCode = firstCell.textContent?.trim() || '';

                    // 对料号添加缩进指示，使Excel中显示层级关系
                    if (level > 0) {
                        let prefix = '';
                        for (let i = 0; i < level; i++) {
                            prefix += '  '; // 每层级增加两个空格
                        }

                        // 添加层级指示符
                        if (level === 1) {
                            // 第一层使用特殊标记
                            itemCode = prefix + '▶ ' + itemCode;
                        } else {
                            // 其他层级使用标准标记
                            itemCode = prefix + '└ ' + itemCode;
                        }
                    }

                    // 将处理后的料号添加到数据中
                    rowData.push(itemCode);

                    // 收集其他列的数据（从第二列开始）
                    for (let i = 1; i < cells.length; i++) {
                        rowData.push(cells[i].textContent?.trim() || '');
                    }
                }

                // 确保行有数据再添加
                if (rowData.length > 0) {
                    rows.push(rowData);
                }
            });

            // 创建工作表
            const worksheet = XLSX.utils.aoa_to_sheet(rows);

            // 创建工作簿
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'BOM树结构');

            // 设置列宽
            const colWidths = [
                { wch: 150 },  // 料件编号（增加宽度以容纳缩进和层级标识）
                { wch: 150 },  // 品名
                { wch: 180 },  // 规格
                { wch: 80 },   // 补给策略
                { wch: 80 },   // 主件底数
                { wch: 80 },   // 组成用量
                { wch: 60 },   // 单位
                { wch: 80 },   // 毛重
                { wch: 80 },   // 净重
                { wch: 80 },   // 长
                { wch: 80 },   // 宽
                { wch: 80 },   // 高
                { wch: 70 },   // 是否可选件
                { wch: 90 },   // 客户编号
                { wch: 120 },  // 客户名称
                { wch: 120 },  // 工单展开选项
                { wch: 70 },   // 代买料
                { wch: 70 },   // 客供料
                { wch: 90 },   // 生效日期
                { wch: 90 }    // 失效日期
            ];
            worksheet['!cols'] = colWidths;

            // 使用当前BOM料号和有效日期作为文件名
            const fileName = `BOM多阶展开_${bomCode.value || '未知料号'}_${effectiveDate.value}.xlsx`;

            // 使用xlsx的writeFile方法导出Excel
            const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
            const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' });
            saveAs(blob, fileName);

            console.log('Excel导出成功:', fileName);
        } catch (err) {
            console.error('导出Excel失败:', err);
        } finally {
            // 导出完成后重置状态
            exporting.value = false;
        }
    }, 1500); // 给1.5秒时间让树完全展开
};

// 添加导航函数
const navigateToParent = (): void => {
    // 使用Inertia.js的router进行导航，以保持SPA体验
    router.visit('/products/bom');
};
</script>

<template>
    <AppLayout title="BOM树结构" class="h-screen flex flex-col">
        <div class="flex flex-col flex-grow bg-gray-900 dark:bg-gray-900 overflow-hidden shadow-xl sm:rounded-lg p-6">
            <!-- 固定的筛选条件部分 -->
            <div class="filter-container pb-2">
                <div class="flex justify-between items-center mb-4">
                    <div class="flex items-center">
                        <!-- 返回上级菜单按钮 -->
                        <button
                            @click="navigateToParent"
                            class="mr-3 text-gray-300 hover:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900 rounded-md p-1"
                            title="返回BOM管理"
                        >
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                        </button>
                        <h2 class="text-xl font-semibold text-gray-200 dark:text-gray-200">BOM结构查询</h2>
                    </div>

                    <!-- 展示模式选择 -->
                    <div class="flex items-center mr-4">
                        <span class="mr-2 text-gray-300 dark:text-gray-300">展示模式：</span>
                        <div class="flex space-x-2">
                            <button
                                v-for="mode in modeOptions"
                                :key="mode.value"
                                @click="handleModeChange(mode.value)"
                                :class="[
                                    'px-3 py-1 text-sm rounded-md',
                                    displayMode === mode.value
                                        ? 'bg-blue-600 text-white dark:bg-blue-700'
                                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                                ]"
                            >
                                {{ mode.label }}
                            </button>
                        </div>
                    </div>

                    <!-- 搜索框 -->
                    <div class="flex items-center">
                        <!-- 可选件复选框 -->
                        <div class="flex items-center mr-4">
                            <input
                                id="include-optional"
                                type="checkbox"
                                v-model="includeOptional"
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                            >
                            <label for="include-optional" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
                                可选件
                            </label>
                        </div>

                        <!-- 客户下拉菜单 -->
                        <div v-if="includeOptional" class="relative mr-2">
                            <select
                                v-model="customerCode"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-44 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            >
                                <option value="">所有客户</option>
                                <option v-for="customer in customers" :key="customer.code" :value="customer.code">
                                    {{ customer.code }} - {{ customer.name }}
                                </option>
                            </select>
                            <div v-if="loadingCustomers" class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <div class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500"></div>
                            </div>
                        </div>

                        <!-- 日期选择 -->
                        <div class="relative mr-2">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <input
                                v-model="effectiveDate"
                                type="date"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            >
                        </div>

                        <!-- 料号输入框 -->
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                </svg>
                            </div>
                            <input
                                v-model="bomCode"
                                type="search"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                placeholder="输入BOM料号..."
                                @keyup.enter="handleSearch"
                            >
                        </div>
                        <button
                            @click="handleSearch"
                            class="ml-2 text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                        >
                            查询
                        </button>
                    </div>
                </div>

                <!-- 当前模式显示 -->
                <div class="mb-2 bg-gray-800 dark:bg-gray-800 p-2 rounded-lg flex justify-between items-center">
                    <span class="text-sm text-gray-300 dark:text-gray-300">
                        当前展示模式：<span class="font-medium">{{ getModeLabel(currentInfo.mode) }}</span> |
                        料号：<span class="font-medium">{{ currentInfo.code || '未指定' }}</span> |
                        有效日期：<span class="font-medium">{{ currentInfo.date }}</span> |
                        可选件：<span class="font-medium">{{ currentInfo.includeOptional ? '显示' : '不显示' }}</span>
                        <span v-if="currentInfo.includeOptional && currentInfo.customerCode">
                            | 客户：<span class="font-medium">{{ currentInfo.customerCode }}</span>
                        </span>
                    </span>

                    <!-- 添加展开/闭合按钮 -->
                    <div class="flex space-x-2">
                        <button
                            @click="setGlobalExpandState('all')"
                            class="button-expand-all px-3 py-1 text-sm rounded-md bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        >
                            全部展开
                        </button>
                        <button
                            @click="setGlobalExpandState('minimal')"
                            class="button-collapse-default px-3 py-1 text-sm rounded-md bg-gray-600 text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                        >
                            收起
                        </button>
                        <button
                            @click="exportToExcel"
                            :disabled="exporting"
                            :class="[
                                'button-export-excel px-3 py-1 text-sm rounded-md flex items-center',
                                exporting
                                    ? 'bg-green-500 opacity-70 cursor-not-allowed'
                                    : 'bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2'
                            ]"
                        >
                            <svg v-if="!exporting" class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
                            </svg>
                            <svg v-else class="w-4 h-4 mr-1 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {{ exporting ? '导出中...' : '导出Excel' }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- 加载中 -->
            <div v-if="loading" class="flex-grow flex justify-center items-center p-8">
                <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
                <span class="ml-3 text-gray-600 dark:text-gray-400">正在加载BOM数据...</span>
            </div>

            <!-- 错误信息 -->
            <div v-else-if="error" class="flex-grow bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded dark:bg-red-900 dark:text-red-300 dark:border-red-700">
                <p>{{ error }}</p>
            </div>

            <!-- BOM树结构 -->
            <div v-else-if="bomData" class="flex-grow flex flex-col">
                <div class="bg-gray-700 p-2 border-b border-gray-600">
                    <h3 class="text-lg font-medium text-gray-200">
                        {{ bomData.specification }}
                    </h3>
                </div>

                <!-- 表格容器，支持水平和垂直滚动 -->
                <div ref="tableContainerRef" class="overflow-x-auto">
                    <!-- 使用table替代div结构，实现更好的对齐和列宽调整 -->
                    <table class="min-w-full border-collapse">
                        <colgroup>
                            <!-- 定义预设的列宽，根据内容确保列宽度足够显示完整内容 -->
                            <col style="width: 150px;"> <!-- 料件编号 -->
                            <col style="width: 150px;"> <!-- 品名 -->
                            <col style="width: 180px;"> <!-- 规格 -->
                            <col style="width: 80px;">  <!-- 补给策略 -->
                            <col style="width: 80px;">  <!-- 主件底数 -->
                            <col style="width: 80px;">  <!-- 组成用量 -->
                            <col style="width: 60px;">  <!-- 单位 -->
                            <col style="width: 80px;">  <!-- 毛重 -->
                            <col style="width: 80px;">  <!-- 净重 -->
                            <col style="width: 80px;">  <!-- 长 -->
                            <col style="width: 80px;">  <!-- 宽 -->
                            <col style="width: 80px;">  <!-- 高 -->
                            <col style="width: 70px;">  <!-- 是否可选件 -->
                            <col style="width: 90px;">  <!-- 客户编号 -->
                            <col style="width: 120px;"> <!-- 客户名称 -->
                            <col style="width: 120px;"> <!-- 工单展开选项 -->
                            <col style="width: 70px;">  <!-- 代买料 -->
                            <col style="width: 70px;">  <!-- 客供料 -->
                            <col style="width: 90px;">  <!-- 生效日期 -->
                            <col style="width: 90px;">  <!-- 失效日期 -->
                        </colgroup>
                        <thead>
                            <tr>
                                <th class="px-2 py-3 text-left text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">料号</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-left text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">品名</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-left text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">规格</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">补给策略</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">主件底数</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">组成用量</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">单位</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">毛重(KG)</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">净重(KG)</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">长(mm)</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">宽(mm)</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">厚度(mm)</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">可选件</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">客户编号</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">客户名称</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">工单展开选项</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">代买料</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">客供料</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">生效日期</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                                <th class="px-2 py-3 text-center text-base font-medium text-gray-200 uppercase tracking-wider sticky top-0 z-10 bg-gray-800 border-b border-gray-700 relative">
                                    <div class="w-full">失效日期</div>
                                    <div class="absolute top-0 right-0 w-1 h-full cursor-col-resize z-10 bg-gray-500 opacity-10 hover:bg-blue-400 hover:opacity-30"></div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-600">
                            <bom-tree
                                :node="bomData"
                                :level="0"
                                :expanded="true"
                                :mode="displayMode"
                                :global-expand-state="globalExpandState"
                            ></bom-tree>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 无数据 -->
            <div v-else class="flex-grow bg-gray-50 p-8 text-center text-gray-500 rounded-lg dark:bg-gray-700 dark:text-gray-400">
                请输入有效的BOM料号进行查询
            </div>
        </div>
    </AppLayout>
</template>

<style scoped>
/* 确保整个页面占满屏幕高度 */
:deep(.min-h-screen) {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 确保主要内容区域占据剩余空间 */
:deep(.max-w-7xl) {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

/* 表格样式 */
.overflow-x-auto {
    overflow-x: auto;
    overflow-y: auto;
    width: 100%;
    border: 1px solid #374151; /* 深色边框 */
    border-radius: 0.375rem;
    max-height: calc(100vh - 260px); /* 限制最大高度，给筛选条件留出空间 */
    position: relative;
    background-color: #1f2937; /* 深色背景 */
}

table {
    table-layout: fixed;
    min-width: 100%;
    width: max-content;
    border-collapse: separate;
    border-spacing: 0;
}

/* 确保表头固定在顶部 */
thead {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #374151; /* 深色表头 */
}

tbody {
    /* 确保tbody可以自由扩展 */
    height: auto;
}

th, td {
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border-right: 1px solid #4b5563;
    border-bottom: 1px solid #4b5563;
}

/* 对齐单元格中的文本 */
th div, td {
    padding: 8px;
    line-height: 1.5;
}

/* 使得所有单元格的内容在单行显示，超出部分省略 */
th div, td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

td {
    color: #e5e7eb; /* 浅色文本 */
}

th:last-child,
td:last-child {
    border-right: none;
}

/* 滚动条样式美化 */
.overflow-x-auto::-webkit-scrollbar {
    height: 8px;
    width: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
    background: #374151;
    border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* 展开/折叠按钮样式 */
.button-expand-all,
.button-collapse-default,
.button-export-excel {
    transition: all 0.2s ease;
}

.button-expand-all:active,
.button-collapse-default:active,
.button-export-excel:active {
    transform: scale(0.95);
}

/* 加载中的导出按钮样式 */
.button-export-excel.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

/* 固定筛选条件部分 */
.filter-container {
    position: sticky;
    top: 0;
    z-index: 20;
    background-color: #111827; /* 深色背景与BOM管理一致 */
}

/* 暗黑模式下的固定筛选条件背景 */
.dark .filter-container {
    background-color: #111827; /* 与系统深色背景一致 */
}

/* 表格列宽调整样式 */
.cursor-col-resize {
    cursor: col-resize;
    transition: background-color 0.2s;
    /* 鼠标悬停时区域变宽，更容易被点击 */
    transform-origin: center;
}

/* 当鼠标悬停在列分隔线上时加强视觉效果 */
.cursor-col-resize:hover {
    background-color: rgba(59, 130, 246, 0.5) !important; /* 蓝色高亮 */
    width: 2px !important; /* 增加宽度 */
    opacity: 0.7 !important;
}

th {
    position: relative;
    user-select: none; /* 防止拖动时选中文本 */
}

/* 当调整列宽时的全局样式 */
body.column-resizing {
    cursor: col-resize;
    user-select: none;
}

/* 被拖动的列表头高亮 */
th.resizing-column {
    background-color: rgba(59, 130, 246, 0.1);
}
</style>