<script setup lang="ts">
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref } from 'vue';

// 定义数据类型接口
interface InboundRecord {
    id: number;
    code: string;
    purchase_order: string;
    supplier: string;
    receipt_date: string;
    status: '待验收' | '验收中' | '已验收';
    inspector: string;
}

// 定义状态样式类型
interface StatusClasses {
    [key: string]: string;
}

// 初始化数据时指定类型
const inboundRecords = ref<InboundRecord[]>([
    { id: 1, code: 'PI-2023-001', purchase_order: 'PO-2023-001', supplier: '苏州某某电子有限公司', receipt_date: '2023-11-25', status: '已验收', inspector: '张工' },
    { id: 2, code: 'PI-2023-002', purchase_order: 'PO-2023-003', supplier: '昆山某某科技有限公司', receipt_date: '2023-12-01', status: '待验收', inspector: '李工' },
    { id: 3, code: 'PI-2023-003', purchase_order: 'PO-2023-005', supplier: '东莞某某精密制造有限公司', receipt_date: '2023-12-05', status: '验收中', inspector: '王工' },
]);

// 状态样式映射
const statusClasses: StatusClasses = {
    '待验收': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    '验收中': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    '已验收': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
};

// 功能方法可以在此添加，并添加正确的类型标注
const handleSearch = (): void => {
    // 实现搜索功能
    console.log('搜索功能待实现');
};

const handleCreateInbound = (): void => {
    // 实现新增入库单功能
    console.log('新增入库单功能待实现');
};

const handleExport = (): void => {
    // 实现导出功能
    console.log('导出功能待实现');
};

const handleView = (record: InboundRecord): void => {
    // 实现查看功能
    console.log('查看入库单:', record);
};

const handleInspect = (record: InboundRecord): void => {
    // 实现验收功能
    console.log('验收入库单:', record);
};

const handleDelete = (record: InboundRecord): void => {
    // 实现删除功能
    console.log('删除入库单:', record);
};
</script>

<template>
    <AppLayout title="采购入库">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-200">采购入库</h2>
                <div class="flex space-x-2">
                    <button @click="handleCreateInbound" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
                        新增入库单
                    </button>
                    <button @click="handleExport" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md">
                        导出数据
                    </button>
                </div>
            </div>
            
            <div class="mb-4">
                <div class="flex space-x-2">
                    <input type="text" placeholder="搜索入库单..." class="rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 flex-grow">
                    <select class="rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm px-4 py-2 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <option value="">全部状态</option>
                        <option value="待验收">待验收</option>
                        <option value="验收中">验收中</option>
                        <option value="已验收">已验收</option>
                    </select>
                    <button @click="handleSearch" class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md">
                        搜索
                    </button>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                入库单号
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                采购单号
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                供应商
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                收货日期
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                状态
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                验收人
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <tr v-for="record in inboundRecords" :key="record.id">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ record.code }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ record.purchase_order }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ record.supplier }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ record.receipt_date }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                                <span :class="statusClasses[record.status]" class="px-2 py-1 text-xs rounded-full">
                                    {{ record.status }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">{{ record.inspector }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button @click="handleView(record)" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">查看</button>
                                <button @click="handleInspect(record)" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3">验收</button>
                                <button @click="handleDelete(record)" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="mt-4 flex justify-end">
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">
                        <span class="sr-only">上一页</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">1</a>
                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">2</a>
                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">3</a>
                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">
                        <span class="sr-only">下一页</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                </nav>
            </div>
        </div>
    </AppLayout>
</template> 