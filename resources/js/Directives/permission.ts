import { DirectiveBinding, ComponentPublicInstance } from 'vue';

interface PermissionInstance extends ComponentPublicInstance {
  $page?: {
    props?: {
      permissions?: Record<string, Record<string, boolean>>;
    }
  }
}

type PermissionValue = string | string[];

/**
 * 权限检查指令
 * 使用方式：v-permission="'roles.view'"
 * 或者：v-permission="['roles.view', 'roles.edit']" (满足任一权限即显示)
 */
export const permission = {
    mounted(el: HTMLElement, binding: DirectiveBinding<PermissionValue>, vnode: any) {
        try {
            // 安全地获取权限信息，确保所有对象都已定义
            const instance = binding.instance as PermissionInstance;
            const permissions = instance?.$page?.props?.permissions || {};
            const requiredPermission = binding.value;
            
            if (!checkPermission(requiredPermission, permissions)) {
                // 如果没有权限，则移除元素
                el.parentNode && el.parentNode.removeChild(el);
            }
        } catch (error) {
            console.error('权限检查错误:', error);
            // 出错时保守处理，默认不显示元素
            el.parentNode && el.parentNode.removeChild(el);
        }
    }
};

/**
 * 检查是否拥有指定权限
 * @param {string|string[]} requiredPermission 所需权限
 * @param {Record<string, Record<string, boolean>>} permissions 用户拥有的权限
 * @returns {boolean}
 */
function checkPermission(
    requiredPermission: PermissionValue, 
    permissions: Record<string, Record<string, boolean>>
): boolean {
    // 如果没有权限信息，直接返回false
    if (!permissions) return false;
    
    // 如果是数组，检查是否有任一权限
    if (Array.isArray(requiredPermission)) {
        return requiredPermission.some(perm => checkSinglePermission(perm, permissions));
    }
    
    // 否则检查单个权限
    return checkSinglePermission(requiredPermission, permissions);
}

/**
 * 检查单个权限
 * @param {string} permission 权限字符串，格式为："module.action"
 * @param {Record<string, Record<string, boolean>>} permissions 用户拥有的权限对象
 * @returns {boolean}
 */
function checkSinglePermission(
    permission: string, 
    permissions: Record<string, Record<string, boolean>>
): boolean {
    const parts = permission.split('.');
    if (parts.length !== 2) return false;
    
    const [module, action] = parts;
    return permissions[module]?.[action] === true;
} 