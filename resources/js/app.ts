import './bootstrap';
import '../css/app.css';

import { createApp, h, type App as VueAppType } from 'vue';
import { createInertiaApp, type Page } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { ZiggyVue } from '../../vendor/tightenco/ziggy';
import { permission } from './Directives/permission';
import { PermissionPlugin } from './Plugins/PermissionPlugin';
import type { PageProps } from './types/index';

// 导入PrimeVue及其组件
import PrimeVue from 'primevue/config';
import ToastService from 'primevue/toastservice';
import Toast from 'primevue/toast';

// 导入PrimeVue样式
import 'primevue/resources/themes/lara-light-blue/theme.css'; // 主题
import 'primevue/resources/primevue.min.css'; // 核心样式
import 'primeicons/primeicons.css'; // 图标

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

createInertiaApp({
    title: (title: string) => `${title} - ${appName}`,
    resolve: (name: string) => resolvePageComponent(`./Pages/${name}.vue`, import.meta.glob<any>('./Pages/**/*.vue')),
    setup({ el, App, props, plugin }) {
        const vueApp = createApp({
            render: () => h(App, props),
            errorCaptured(err: unknown, instance: unknown, info: string): boolean {
                console.error('全局错误:', err, info);
                return false;
            }
        });
        
        vueApp
            .use(plugin)
            .use(ZiggyVue)
            .use(PermissionPlugin)
            .use(PrimeVue, { ripple: true })
            .use(ToastService)
            .component('Toast', Toast)
            .directive('permission', permission)
            .mount(el);
            
        return vueApp;
    },
    progress: {
        color: '#4B5563',
    },
}); 