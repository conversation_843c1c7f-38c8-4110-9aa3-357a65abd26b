{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "lib": ["esnext", "dom", "dom.iterable"], "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["resources/js/*"]}, "types": ["node", "vite/client"], "useDefineForClassFields": true, "isolatedModules": true, "allowJs": true, "checkJs": false, "outDir": "dist", "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "experimentalDecorators": true, "typeRoots": ["./node_modules/@types", "./resources/js/types"]}, "include": ["resources/js/**/*.ts", "resources/js/**/*.d.ts", "resources/js/**/*.tsx", "resources/js/**/*.vue"], "exclude": ["node_modules", "public", "dist", "vendor"], "references": [{"path": "./tsconfig.node.json"}], "vueCompilerOptions": {"target": 3.3, "experimentalModelPropName": true}}