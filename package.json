{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "type-check": "vue-tsc --noEmit", "lint": "eslint 'resources/js/**/*.{ts,vue}'", "check": "npm run type-check && npm run lint", "tsc-watch": "vue-tsc --noEmit --watch"}, "devDependencies": {"@inertiajs/vue3": "^1.0.14", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/file-saver": "^2.0.7", "@types/node": "^20.17.47", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.16", "axios": "^1.7.4", "concurrently": "^9.0.1", "eslint": "^9.27.0", "eslint-plugin-vue": "^10.1.0", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.3.3", "vite": "^6.0.11", "vue": "^3.3.13", "vue-tsc": "^1.8.27"}, "dependencies": {"element-plus": "^2.9.6", "file-saver": "^2.0.5", "primeicons": "^7.0.0", "primevue": "^3.53.1", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13"}}