---
description: 
globs: 
alwaysApply: true
---
# EquipDev ERP系统架构指南

## 项目概述
EquipDev是一个使用Laravel11为框架的ERP系统，主要功能包括BOM管理、销售管理、采购管理、仓储管理、生产管理、物流管理、财务管理、BI报表和基础信息管理等。

## 要遵守的规则
- MVC 控制器中进行参数合法性验证，逻辑放在模型层
- API数据访问路由要进行CORS验证
- First Principles（第一性原理）：梳理最核心需求与边界
- YAGNI：只实现当前真正需要的功能
- KISS：保持设计和实现的简单性
- SOLID：面向对象/模块化设计时，遵循单一职责、开放封闭等
- DRY：消除重复，提炼公用逻辑

## 前端
- vue3 + TypeScript

### 根据场景动态调整顺序
- 架构级／需求分析（Project Kickoff） First Principles →  YAGNI → KISS → SOLID → DRY
- 新功能迭代／增量开发：YAGNI → KISS → SOLID → DRY → First Principles
- 小函数／工具库实现：KISS → DRY → YAGNI → SOLID → First Principles
- 复杂业务组件／面向对象建模：First Principles → SOLID → YAGNI → KISS → DRY

## 核心文件结构

### 入口点和配置
- 主要入口点: [artisan](mdc:artisan)
- 前端构建配置: [vite.config.js](mdc:vite.config.js)
- 数据库配置: [config/database.php](mdc:config/database.php)

### 路由定义
- API路由: [routes/api.php](mdc:routes/api.php)
- Web路由: [routes/web.php](mdc:routes/web.php)

### 控制器
- 基础控制器: [app/Http/Controllers/Controller.php](mdc:app/Http/Controllers/Controller.php)
- 客户管理: @app/Http/Controllers/CustomerController.php
- 订单管理: @app/Http/Controllers/OrderController.php
- 料件管理: [app/Http/Controllers/MaterialController.php](mdc:app/Http/Controllers/MaterialController.php)
- BOM管理: [app/Http/Controllers/BOMController.php](mdc:app/Http/Controllers/BOMController.php)
- 用户管理: [app/Http/Controllers/UserController.php](mdc:app/Http/Controllers/UserController.php)
- 部门管理: [app/Http/Controllers/DepartmentController.php](mdc:app/Http/Controllers/DepartmentController.php)
- 权限管理: [app/Http/Controllers/PermissionController.php](mdc:app/Http/Controllers/PermissionController.php)
- 角色管理: [app/Http/Controllers/RoleController.php](mdc:app/Http/Controllers/RoleController.php)
- API认证: [app/Http/Controllers/API/AuthController.php](mdc:app/Http/Controllers/API/AuthController.php)

### 模型
- 用户模型: [app/Models/User.php](mdc:app/Models/User.php)
- 客户模型: @app/Models/Customer.php
- 订单模型: @app/Models/Order.php
- 料件模型: [app/Models/Material.php](mdc:app/Models/Material.php)
- BOM模型: [app/Models/BOM.php](mdc:app/Models/BOM.php)
- 部门模型: [app/Models/Department.php](mdc:app/Models/Department.php)
- 角色模型: [app/Models/Role.php](mdc:app/Models/Role.php)
- 权限模型: [app/Models/Permission.php](mdc:app/Models/Permission.php)

## 功能模块

### 认证与授权
- API认证使用Laravel Sanctum实现
- 用户登录路由: `/api/login`
- 用户信息获取: `/api/user`
- 用户登出: `/api/logout`

### 客户管理
- 客户列表: `/api/sales/customers/getList`
- 客户详情: `/api/sales/customers/show/{id}`
- 创建客户: `/api/sales/customers/create`
- 更新客户: `/api/sales/customers/update/{id}`
- 删除客户: `/api/sales/customers/{id}`

### 料件管理
- 料件列表: `/api/materials`
- 产品分类: `/api/materials/product-categories`

### BOM管理
- BOM列表: `/api/BOM`
- BOM详情(树结构): `/api/BOM/tree/{code}`
- 产品分类: `/api/BOM/product-categories`

## 前端结构
- JavaScript资源: [resources/js](mdc:resources/js)
- CSS资源: [resources/css](mdc:resources/css)
- 视图文件: [resources/views](mdc:resources/views)

## 数据库结构
- 数据库迁移: [database/migrations](mdc:database/migrations)
- 数据填充器: [database/seeders](mdc:database/seeders)

## 开发工具
- Tailwind配置: [tailwind.config.js](mdc:tailwind.config.js)
- PostCSS配置: [postcss.config.js](mdc:postcss.config.js)
- PHPUnit测试: [phpunit.xml](mdc:phpunit.xml)
