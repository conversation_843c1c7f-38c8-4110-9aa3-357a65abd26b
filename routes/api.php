<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\SupplierController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ExchangeRateController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// 认证相关路由（不需要认证）
Route::post('/login', [AuthController::class, 'login'])->name('api.login');

// 需要认证的路由
Route::middleware('auth:sanctum')->group(function () {  // 认证
    // 登出
    Route::post('/logout', [AuthController::class, 'logout']);

    // 用户相关
    Route::prefix('user')->name('user.')->group(function () {
        // 获取当前用户信息
        Route::get('/', [AuthController::class, 'me']);
        // 获取所有用户列表（用于角色分配）
        Route::get('/list', [UserController::class, 'getUserList']);
        // 获取用户授权公司列表
        Route::get('/companies', [UserController::class, 'getUserCompanies']);
    });


    // 料件管理相关路由
    Route::prefix('materials')->name('materials.')->group(function () {
        Route::get('/', [App\Http\Controllers\MaterialController::class, 'getMaterialList']);  // 获取料件列表
        Route::get('/product-categories', [App\Http\Controllers\MaterialController::class, 'getProductCategory']);  // 获取产品分类
    });

    // BOM管理相关路由
    Route::prefix('BOM')->name('BOM.')->group(function () {
        Route::get('/', [App\Http\Controllers\BOMController::class, 'index']);  // 获取BOM列表
        Route::get('/product-categories', [App\Http\Controllers\BOMController::class, 'getProductCategory']);  // 获取产品分类
        Route::get('/customers', [App\Http\Controllers\BOMController::class, 'getCustomers']);  // 获取客户列表，用于区分可选件的客户关系
        Route::any('/tree/{code}', [App\Http\Controllers\BOMController::class, 'getBOMByCode']);  // 获取BOM信息（包含树结构）
    });

    // 销售管理相关路由
    Route::prefix('sales')->name('sales.')->group(function () {
        // 客户管理
        Route::prefix('customers')->name('customers.')->group(function () {
            Route::get('/form-data', [CustomerController::class, 'getFormData']);  // 获取表单下拉数据
            Route::get('/getList', [CustomerController::class, 'index']);  // 获取客户列表
            Route::get('/show/{id}', [CustomerController::class, 'show']);  // 获取单个客户详情
            Route::post('/create', [CustomerController::class, 'store']);  // 创建新客户
            Route::put('/update/{id}', [CustomerController::class, 'update']);  // 更新客户信息
            Route::delete('/{id}', [CustomerController::class, 'destroy']);  // 删除客户

            Route::get('/getSimpleList', [CustomerController::class, 'getSimpleList']);  // 获取简单的客户列表
        });

        // 订单管理
        Route::prefix('orders')->name('orders.')->group(function () {
            Route::get('/form-data', [OrderController::class, 'getFormData']);  // 获取表单下拉数据
            Route::get('/getList', [OrderController::class, 'index']);  // 获取订单列表
            Route::get('/materials', [OrderController::class, 'getMaterials']);  // 获取物料列表（用于订单明细）
            Route::get('/{orderCode}', [OrderController::class, 'show']);  // 获取单个订单详情
            Route::post('/create', [OrderController::class, 'store']);  // 创建新订单
            Route::put('/update/{orderCode}', [OrderController::class, 'update']);  // 更新订单信息
            Route::delete('/{orderCode}', [OrderController::class, 'destroy']);  // 删除订单
            Route::put('/{orderCode}/approve', [OrderController::class, 'approve']);  // 审核订单
            Route::put('/{orderCode}/cancel', [OrderController::class, 'cancel']);  // 作废订单
            Route::put('/{orderCode}/close', [OrderController::class, 'close']);  // 结案订单
            Route::get('/customers', [OrderController::class, 'getCustomerList']);  // 获取已有订单的客户列表
            Route::get('/customer-defaults/{customerCode}', [OrderController::class, 'getCustomerDefaults']);  // 获取客户默认信息
        });
    });

    // 采购管理相关路由
    Route::prefix('purchases')->name('purchases.')->group(function () {
        // 供应商管理
        Route::prefix('suppliers')->name('suppliers.')->group(function () {
            Route::get('/form-data', [SupplierController::class, 'getFormData']);  // 获取表单下拉数据
            Route::get('/getList', [SupplierController::class, 'index']);  // 获取供应商列表
            Route::get('/show/{id}', [SupplierController::class, 'show']);  // 获取单个供应商详情
            Route::post('/create', [SupplierController::class, 'store']);  // 创建新供应商
            Route::put('/update/{id}', [SupplierController::class, 'update']);  // 更新供应商信息
            Route::delete('/{id}', [SupplierController::class, 'destroy']);  // 删除供应商
        });
    });

    // 汇率相关路由
    Route::prefix('exchange-rates')->name('exchange-rates.')->group(function () {
        Route::get('/usd-cny', [ExchangeRateController::class, 'getUsdCnyRate']);  // 获取美元兑人民币汇率
    });

});
