<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use App\Http\Controllers\UserController;
use App\Http\Controllers\DepartmentController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\ProfileController;

// 测试路由
Route::get('/test', function () {
    $companies = DB::table('user_company')->where('user_id', Auth::user()->id)->get();

    // if(in_array('SG', $companies->pluck('company_code')->toArray())) {
    //     return 'true';
    // }

    $data = [
        'user' => Auth::user(),
        'companies_count' => $companies->count(),
        'companies' => $companies->pluck('company_code')->toArray(),
        'defaultCompany' => Auth::user()->defaultCompany,
        'formData' => Auth::user()->only('account', 'name'),//当前登录用户的account及name
    ];
    return $data;
});

Route::get('/', function () {
    return redirect()->route('login');
});

// 添加GET方式的logout路由
Route::get('/logout', function () {
    Auth::logout();
    request()->session()->invalidate();
    request()->session()->regenerateToken();
    return redirect('/');
})->name('logout.get');

Route::middleware([
    'auth:sanctum',// 认证
    config('jetstream.auth_session'),// 会话
    'verified',// 验证
])->group(function () {
    // API测试页面
    Route::get('/api-test', function() {
        return Inertia::render('API/Test');
    })->name('api.test');

    // 添加一个路由，用于从顶部Logo或文字导航到控制台
    Route::get('/home', function () {
        return redirect()->route('dashboard');
    })->name('home');

    // 控制台
    Route::get('/dashboard', function () {
        return Inertia::render('Dashboard');
    })->middleware('permission:dashboard.view')->name('dashboard');

    // 产品管理模块
    Route::prefix('products')->name('products.')->middleware('permission:products.view')->group(function () {
        Route::get('/bom', function() {
            return Inertia::render('Products/Bom');
        })->name('bom');

        Route::get('/bom-tree/{code?}', function($code = null) {
            return Inertia::render('Products/BomTreeView', [
                'code' => $code
            ]);
        })->name('bom-tree');

        Route::get('/materials', function() {
            return Inertia::render('Products/Materials');
        })->name('materials');
    });

    // 销售管理模块
    Route::prefix('sales')->name('sales.')->middleware('permission:sales.view')->group(function () {
        Route::get('/customers', function() {
            return Inertia::render('Sales/Customers');
        })->name('customers');

        Route::get('/price', function() {
            return Inertia::render('Sales/Price');
        })->name('price');

        Route::get('/orders', function() {
            return Inertia::render('Sales/Orders');
        })->name('orders');

        Route::get('/orders/create', function(Request $request) {
            return Inertia::render('Sales/OrderCreate', [
                'company_code' => $request->query('company_code')
            ]);
        })->name('orders.create');

        Route::get('/orders/edit/{orderCode}', function($orderCode) {
            return Inertia::render('Sales/OrderEdit', [
                'orderCode' => $orderCode
            ]);
        })->name('orders.edit');

        Route::get('/contracts', function() {
            return Inertia::render('Sales/Contracts');
        })->name('contracts');

        Route::get('/tracking', function() {
            return Inertia::render('Sales/Tracking');
        })->name('tracking');

        Route::get('/delivery', function() {
            return Inertia::render('Sales/Delivery');
        })->name('delivery');

        Route::get('/invoices', function() {
            return Inertia::render('Sales/Invoices');
        })->name('invoices');
    });

    // 采购管理模块
    Route::prefix('purchases')->name('purchases.')->middleware('permission:purchases.view')->group(function () {
        Route::get('/suppliers', function() {
            return Inertia::render('Purchases/Suppliers');
        })->name('suppliers');

        Route::get('/price', function() {
            return Inertia::render('Purchases/Price');
        })->name('price');

        Route::get('/mrp', function() {
            return Inertia::render('Purchases/Mrp');
        })->name('mrp');

        Route::get('/orders', function() {
            return Inertia::render('Purchases/Orders');
        })->name('orders');

        Route::get('/pricing', function() {
            return Inertia::render('Purchases/Pricing');
        })->name('pricing');

        Route::get('/invoices', function() {
            return Inertia::render('Purchases/Invoices');
        })->name('invoices');
    });

    // 仓库管理模块
    Route::prefix('warehouse')->name('warehouse.')->middleware('permission:warehouse.view')->group(function () {
        Route::get('/purchase-in', function() {
            return Inertia::render('Warehouse/PurchaseIn');
        })->name('purchase-in');

        Route::get('/other-in', function() {
            return Inertia::render('Warehouse/OtherIn');
        })->name('other-in');

        Route::get('/material-out', function() {
            return Inertia::render('Warehouse/MaterialOut');
        })->name('material-out');

        Route::get('/shipping', function() {
            return Inertia::render('Warehouse/Shipping');
        })->name('shipping');
    });

    // 物流管理模块
    Route::prefix('logistics')->name('logistics.')->middleware('permission:logistics.view')->group(function () {
        Route::get('/packing', function() {
            return Inertia::render('logistics/Packing');
        })->name('packing');

        Route::get('/encasement', function() {
            return Inertia::render('logistics/Encasement');
        })->name('encasement');

        Route::get('/customsDeclaration', function() {
            return Inertia::render('logistics/CustomsDeclaration');
        })->name('customsDeclaration');

        Route::get('/shipping', function() {
            return Inertia::render('logistics/Shipping');
        })->name('shipping');
    });

    // 生产管理模块
    Route::prefix('production')->name('production.')->middleware('permission:production.view')->group(function () {
        Route::get('/plans', function() {
            return Inertia::render('Production/Plans');
        })->name('plans');

        Route::get('/work-orders', function() {
            return Inertia::render('Production/WorkOrders');
        })->name('work-orders');

        Route::get('/material-requisitions', function() {
            return Inertia::render('Production/MaterialRequisitions');
        })->name('material-requisitions');
    });

    // 质量管理模块
    Route::prefix('QC')->name('QC.')->middleware('permission:QC.view')->group(function () {
        Route::get('/inspection', function() {
            return Inertia::render('QC/Inspection');
        })->name('inspection');
    });

    // 财务管理模块
    Route::prefix('finance')->name('finance.')->middleware('permission:finance.view')->group(function () {
        Route::get('/cost-accounting', function() {
            return Inertia::render('Finance/CostAccounting');
        })->name('cost-accounting');

        Route::get('/receivables', function() {
            return Inertia::render('Finance/Receivables');
        })->name('receivables');

        Route::get('/payables', function() {
            return Inertia::render('Finance/Payables');
        })->name('payables');
    });

    // BI报表模块
    Route::prefix('reports')->name('reports.')->middleware('permission:reports.view')->group(function () {
        Route::get('/annual', function() {
            return Inertia::render('Reports/Annual');
        })->name('annual');

        Route::get('/monthly', function() {
            return Inertia::render('Reports/Monthly');
        })->name('monthly');
    });

    // 基础信息模块
    Route::prefix('settings')->name('settings.')->group(function () {
        // 部门管理路由
        Route::get('/departments', [DepartmentController::class, 'index'])->middleware('permission:settings.departments.view')->name('departments');
        Route::get('/departments/create', [DepartmentController::class, 'create'])->middleware('permission:settings.departments.create')->name('departments.create');
        Route::post('/departments', [DepartmentController::class, 'store'])->middleware('permission:settings.departments.create')->name('departments.store');
        Route::get('/departments/{department}', [DepartmentController::class, 'show'])->middleware('permission:settings.departments.view')->name('departments.show');
        Route::get('/departments/{department}/edit', [DepartmentController::class, 'edit'])->middleware('permission:settings.departments.edit')->name('departments.edit');
        Route::put('/departments/{department}', [DepartmentController::class, 'update'])->middleware('permission:settings.departments.edit')->name('departments.update');
        Route::delete('/departments/{department}', [DepartmentController::class, 'destroy'])->middleware('permission:settings.departments.delete')->name('departments.destroy');

        // 角色管理路由
        Route::get('/roles', [RoleController::class, 'index'])->middleware('permission:settings.roles.view')->name('roles');
        Route::get('/roles/create', [RoleController::class, 'create'])->middleware('permission:settings.roles.create')->name('roles.create');
        Route::post('/roles', [RoleController::class, 'store'])->middleware('permission:settings.roles.create')->name('roles.store');
        Route::get('/roles/{role}', [RoleController::class, 'show'])->middleware('permission:settings.roles.view')->name('roles.show');
        Route::get('/roles/{role}/edit', [RoleController::class, 'edit'])->middleware('permission:settings.roles.edit')->name('roles.edit');
        Route::put('/roles/{role}', [RoleController::class, 'update'])->middleware('permission:settings.roles.edit')->name('roles.update');
        Route::delete('/roles/{role}', [RoleController::class, 'destroy'])->middleware('permission:settings.roles.delete')->name('roles.destroy');
        Route::post('/roles/{role}/assign-users', [RoleController::class, 'assignUsers'])->middleware('permission:settings.roles.edit')->name('roles.assign-users');

        // 权限管理路由
        Route::get('/permissions', [PermissionController::class, 'index'])->middleware('permission:settings.permissions.view')->name('permissions');
        Route::get('/permissions/create', [PermissionController::class, 'create'])->middleware('permission:settings.permissions.create')->name('permissions.create');
        Route::post('/permissions', [PermissionController::class, 'store'])->middleware('permission:settings.permissions.create')->name('permissions.store');
        Route::get('/permissions/{permission}', [PermissionController::class, 'show'])->middleware('permission:settings.permissions.view')->name('permissions.show');
        Route::get('/permissions/{permission}/edit', [PermissionController::class, 'edit'])->middleware('permission:settings.permissions.edit')->name('permissions.edit');
        Route::put('/permissions/{permission}', [PermissionController::class, 'update'])->middleware('permission:settings.permissions.edit')->name('permissions.update');
        Route::delete('/permissions/{permission}', [PermissionController::class, 'destroy'])->middleware('permission:settings.permissions.delete')->name('permissions.destroy');

        // 用户管理路由
        Route::get('/users', [UserController::class, 'index'])->middleware('permission:settings.users.view')->name('users');
        Route::get('/users/create', [UserController::class, 'create'])->middleware('permission:settings.users.create')->name('users.create');
        Route::post('/users', [UserController::class, 'store'])->middleware('permission:settings.users.create')->name('users.store');
        Route::get('/users/{user}', [UserController::class, 'show'])->middleware('permission:settings.users.view')->name('users.show');
        Route::get('/users/{user}/edit', [UserController::class, 'edit'])->middleware('permission:settings.users.edit')->name('users.edit');
        Route::put('/users/{user}', [UserController::class, 'update'])->middleware('permission:settings.users.edit')->name('users.update');
        Route::delete('/users/{user}', [UserController::class, 'destroy'])->middleware('permission:settings.users.delete')->name('users.destroy');
    });

    // 个人资料相关路由
    Route::get('/user/profile', [ProfileController::class, 'edit'])->name('profile.show');
});
