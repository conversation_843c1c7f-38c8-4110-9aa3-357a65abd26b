<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('orders')) {
            Schema::create('orders', function (Blueprint $table) {
                $table->id();
                $table->string('company_code', 10)->comment('公司编码:新加坡贸易公司SG,工具箱柜TB,泰国工厂TH,装备发展ED,机电设备EM,天狼机械TL');
                $table->string('order_code', 20)->unique()->comment('订单编号(公司编号+10位流水号yymmdd****)');
                $table->string('customer_code', 20)->index()->comment('客户编号');
                $table->string('sales_account', 20)->index()->comment('销售员账号');
                $table->integer('department_id')->unsigned()->index()->comment('部门');
                $table->timestamp('order_date')->index()->comment('订单日期');
                $table->string('customer_order_number', 255)->comment('客户订单号');

                $table->string('sales_type_code', 20)->comment('销售分类');
                $table->string('currency_code', 20)->comment('币种');
                $table->decimal('exchange_rate', 10, 6)->comment('汇率');
                $table->string('exchange_rate_base_code', 20)->comment('汇率计算基准');
                $table->string('tax_type_code', 20)->comment('税种');
                $table->decimal('tax_rate', 10, 4)->comment('税率');
                $table->string('trade_term_code', 20)->comment('交易条件');
                $table->string('receipt_payment_term_code', 20)->comment('收款条件');
                $table->string('invoice_type_code', 20)->comment('发票类型');
                $table->string('pricing_method_code', 20)->comment('取价方式');

                
                $table->string('create_account', 20)->comment('创建人账号');
                $table->string('update_account', 20)->comment('更新人账号');

                $table->enum('status', ['N', 'Y', 'C', 'X'])->default('N')->comment('状态(N:未审核,Y:已审核,C:结案,X:作废)');
                $table->text('remark')->nullable()->comment('备注');
                $table->timestamps();
                $table->softDeletes();
                $table->comment('订单信息');

                $table->index(['company_code', 'order_date', 'department_id', 'sales_account', 'customer_code', 'status'], 'order_index');
            });
        }

        if (!Schema::hasTable('order_details')) {
            Schema::create('order_details', function (Blueprint $table) {
                $table->id();
                $table->string('company_code', 10)->comment('公司编码:新加坡贸易公司SG,工具箱柜TB,泰国工厂TH,装备发展ED,机电设备EM,天狼机械TL');
                $table->string('order_code', 20)->index()->comment('订单编号');
                $table->integer('order_item')->comment('订单项次');
                $table->string('material_code', 20)->index()->comment('物料编号');
                $table->decimal('quantity', 10, 2)->comment('订单数量');
                $table->decimal('production_quantity', 10, 2)->comment('生产数量');
                $table->decimal('unit_price', 10, 4)->comment('单价');
                $table->decimal('standard_unit_price', 10, 4)->comment('标准单价');
                $table->string('tax_type_code', 20)->comment('税种');
                $table->decimal('tax_rate', 10, 4)->comment('税率');
                $table->decimal('total_price_no_tax', 10, 4)->comment('税前金额');
                $table->decimal('total_price_tax', 10, 4)->comment('含税金额');
                $table->decimal('tax_amount', 10, 4)->comment('税额');
                $table->string('clear_table_number', 50)->nullable()->comment('清表编号');
                $table->string('customer_material_code', 50)->nullable()->comment('客户料号');
                $table->string('customer_material_specification', 50)->nullable()->comment('客户料号规格');

                // $table->json('colors')->comment('部件颜色(JSON格式)');
                $table->string('color', 255)->nullable()->comment('颜色说明');
                $table->string('brand', 255)->nullable()->comment('商标');
                $table->string('description', 255)->nullable()->comment('说明书');
                $table->string('inner_packing', 255)->nullable()->comment('内包装');
                $table->string('outer_packing', 255)->nullable()->comment('外包装');
                $table->string('warning', 255)->nullable()->comment('警告');
                $table->string('borrow', 255)->nullable()->comment('借用');

                $table->date('expected_completion_date')->nullable()->comment('预计完工日期');
                $table->date('expected_delivery_date')->nullable()->comment('预计交货日期');
                $table->date('actual_completion_date')->nullable()->comment('实际完工日期');
                $table->date('actual_delivery_date')->nullable()->comment('实际出货日期');

                $table->unsignedBigInteger('order_price_id')->nullable()->comment('销售核价单编号');
                $table->integer('order_price_item')->nullable()->comment('销售核价单项次');

                $table->string('create_account', 20)->comment('创建人账号');
                $table->string('update_account', 20)->comment('更新人账号');
                $table->enum('status', ['1', '2', '3', '4', '5', '6', '7', '8'])->default('1')->comment('状态(1:一般,2:结案,3:长结,4:短结,5:留置,6:已排产,7:已作废,8:完工入库)');
                $table->text('remark')->nullable()->comment('备注');
                $table->text('remark_2')->nullable()->comment('备注2');
                $table->timestamps();
                $table->softDeletes();
                $table->comment('订单明细信息');

                $table->index(['company_code', 'order_code', 'order_item', 'material_code', 'status'], 'order_detail_index');
                $table->unique(['company_code', 'order_code', 'order_item', 'material_code'], 'order_detail_unique');
            });
        }

        if (!Schema::hasTable('order_prices')) {
            Schema::create('order_prices', function (Blueprint $table) {
                $table->id();
                $table->string('company_code', 10)->comment('公司编码:新加坡贸易公司SG,工具箱柜TB,泰国工厂TH,装备发展ED,机电设备EM,天狼机械TL');
                $table->string('customer_code', 20)->index()->comment('客户编号');
                $table->string('create_account', 20)->comment('创建人账号');
                $table->timestamp('effective_date')->comment('生效日期');
                $table->timestamp('expiry_date')->nullable()->comment('失效日期');
                $table->text('remark')->nullable()->comment('备注');
                $table->timestamps();
                $table->softDeletes();
                $table->comment('销售核价单');

                $table->index(['company_code', 'customer_code', 'create_account','effective_date'], 'order_price_index');
                $table->unique(['company_code', 'customer_code', 'create_account','effective_date'], 'order_price_unique');
            });
        }

        if (!Schema::hasTable('order_price_details')) {
            Schema::create('order_price_details', function (Blueprint $table) {
                $table->id();
                $table->string('company_code', 10)->comment('公司编码:新加坡贸易公司SG,工具箱柜TB,泰国工厂TH,装备发展ED,机电设备EM,天狼机械TL');
                $table->unsignedBigInteger('order_price_id')->nullable()->comment('销售核价单编号');
                $table->integer('order_price_item')->comment('销售核价单项次');
                $table->string('material_code', 20)->index()->comment('物料编号');
                $table->decimal('unit_price', 10, 4)->comment('单价');
                $table->string('create_account', 20)->comment('创建人账号');
                $table->text('remark')->nullable()->comment('备注');
                $table->timestamps();
                $table->softDeletes();
                $table->comment('销售核价单明细');

                $table->index(['company_code', 'order_price_id', 'order_price_item', 'material_code'], 'order_price_detail_index');
                $table->unique(['company_code', 'order_price_id', 'order_price_item', 'material_code'], 'order_price_detail_unique');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
        Schema::dropIfExists('order_details');
    }
};
