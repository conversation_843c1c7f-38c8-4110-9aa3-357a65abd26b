<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 创建部门主管多对多关系表
        if (!Schema::hasTable('department_manager')) {
            Schema::create('department_manager', function (Blueprint $table) {
                $table->foreignId('department_id')->constrained()->onDelete('cascade');
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->timestamps();
                
                $table->primary(['department_id', 'user_id']);
            });
        }
        
        // 迁移现有的单主管数据到多对多关系表
        if (Schema::hasColumn('departments', 'manager_id')) {
            // 在PHP代码中进行数据迁移
            $departments = DB::table('departments')->whereNotNull('manager_id')->get();
            foreach ($departments as $department) {
                // 检查是否已有记录，避免重复插入
                $exists = DB::table('department_manager')
                    ->where('department_id', $department->id)
                    ->where('user_id', $department->manager_id)
                    ->exists();
                    
                if (!$exists) {
                    DB::table('department_manager')->insert([
                        'department_id' => $department->id,
                        'user_id' => $department->manager_id,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            }
        }
        
        // 尝试移除外键约束和字段
        try {
            // 尝试移除外键约束
            Schema::table('departments', function (Blueprint $table) {
                try {
                    $table->dropForeign(['manager_id']);
                } catch (\Exception $e) {
                    // 忽略错误，可能外键约束不存在
                }
            });
            
            // 再移除字段
            if (Schema::hasColumn('departments', 'manager_id')) {
                Schema::table('departments', function (Blueprint $table) {
                    $table->dropColumn('manager_id');
                });
            }
        } catch (\Exception $e) {
            // 记录错误但继续执行
            \Log::error("移除manager_id时出错: " . $e->getMessage());
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 先添加manager_id字段
        if (!Schema::hasColumn('departments', 'manager_id')) {
            Schema::table('departments', function (Blueprint $table) {
                $table->foreignId('manager_id')->nullable();
            });
            
            // 添加外键约束
            Schema::table('departments', function (Blueprint $table) {
                $table->foreign('manager_id')->references('id')->on('users')->nullOnDelete();
            });
            
            // 将多对多关系表中的第一个主管数据迁移回departments表
            $departmentManagers = DB::table('department_manager')
                ->select('department_id', 'user_id')
                ->orderBy('created_at')
                ->get()
                ->groupBy('department_id');
                
            foreach ($departmentManagers as $departmentId => $managers) {
                if ($managers->isNotEmpty()) {
                    DB::table('departments')
                        ->where('id', $departmentId)
                        ->update(['manager_id' => $managers->first()->user_id]);
                }
            }
        }
        
        Schema::dropIfExists('department_manager');
    }
};
