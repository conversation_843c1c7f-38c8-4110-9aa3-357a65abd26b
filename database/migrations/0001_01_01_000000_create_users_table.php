<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('users')) {
            Schema::create('users', function (Blueprint $table) {
                $table->id();
                $table->string('account', 20)->unique()->comment('账号');
                $table->string('name', 20)->comment('姓名');
                $table->string('locale', 10)->default('zh_CN')->comment('语言');
                $table->string('default_company_code', 20)->index()->comment('默认公司code');
                $table->string('department_id', 20)->nullable()->comment('部门');
                $table->string('email', 100)->nullable()->comment('邮箱');
                $table->string('employee_id')->nullable()->comment('工号');
                $table->string('position')->nullable()->comment('职位');
                $table->string('phone')->nullable()->comment('手机号码');
                $table->string('address')->nullable()->comment('家庭地址');
                $table->date('hire_date')->nullable()->comment('入职日期');
                $table->string('status')->default('在职')->comment('状态：在职、离职、休假');
                $table->string('emergency_contact')->nullable()->comment('紧急联系人');
                $table->string('emergency_phone')->nullable()->comment('紧急联系电话');
                $table->text('notes')->nullable()->comment('备注');
                
                $table->timestamp('email_verified_at')->nullable();
                $table->string('password');
                $table->rememberToken();
                $table->foreignId('current_team_id')->nullable();
                $table->string('profile_photo_path', 2048)->nullable();
                $table->timestamps();
                $table->softDeletes();
                $table->comment('用户信息');


            });
        }

        if (!Schema::hasTable('password_reset_tokens')) {
            Schema::create('password_reset_tokens', function (Blueprint $table) {
                $table->foreignId('user_id')->primary();
                $table->string('token');
                $table->timestamp('created_at')->nullable();
                $table->comment('密码重置令牌');
            });
        }

        if (!Schema::hasTable('sessions')) {
            Schema::create('sessions', function (Blueprint $table) {
                $table->string('id')->primary();
                $table->foreignId('user_id')->nullable()->index();
                $table->string('ip_address', 45)->nullable();
                $table->text('user_agent')->nullable();
                $table->longText('payload');
                $table->integer('last_activity')->index();
                $table->comment('会话信息');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
