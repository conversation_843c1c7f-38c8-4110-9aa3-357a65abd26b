<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('colors')) {
            Schema::create('colors', function (Blueprint $table) {
                $table->id();
                $table->string('company_code', 10)->comment('公司编码:新加坡贸易公司SG,工具箱柜TB,泰国工厂TH,装备发展ED,机电设备EM,天狼机械TL');
                $table->string('color_code', 20)->unique()->comment('颜色编号');
                $table->string('color_name', 50)->comment('颜色名称');
                $table->enum('status', ['Y', 'N'])->default('Y')->comment('状态码(Y:有效, N:无效)');
                $table->comment('颜色信息');
                $table->timestamps();
                $table->softDeletes();

                $table->index(['company_code', 'color_code', 'status'], 'color_index');
                $table->unique(['company_code', 'color_code']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('colors');
    }
    
};
