import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

export default defineConfig({
    plugins: [
        laravel({
            input: 'resources/js/app.ts',
            refresh: true,
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
            script: {
                defineModel: true,
                propsDestructure: true,
            },
        }),
    ],
    server: {
        host: '0.0.0.0',
        hmr: {
            host: 'localhost'
        },
        cors: true,
        proxy: {
            // 代理API请求
            '/api': {
                target: 'https://equipdev.com',
                changeOrigin: true,
                secure: false,
                rewrite: (path) => path
            },
        }
    },
    resolve: {
        alias: {
            '@': resolve(__dirname, './resources/js'),
        },
        extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    },
    optimizeDeps: {
        include: ['vue', '@inertiajs/vue3'],
    },
    build: {
        sourcemap: true,
        chunkSizeWarningLimit: 1000,
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ['vue', '@inertiajs/vue3', 'axios'],
                    ui: ['primevue/config', 'primevue/toastservice', 'primevue/toast']
                }
            }
        }
    },
    css: {
        devSourcemap: true
    }
}); 